// features/more/presentation/widgets/more_header_widget.dart
import 'package:flutter/material.dart';

import 'package:easy_localization/easy_localization.dart';
import 'package:go_router/go_router.dart';
import 'package:tripooo_user/core/theme/theme.dart';
import 'package:tripooo_user/core/widgets/widgets.dart';
import 'package:tripooo_user/core/routing/app_router.dart';
import 'package:tripooo_user/features/onboarding/utils/responsive_helper.dart';
import '../../../Auth/presentation/cubit/auth_cubit.dart';

class MoreHeaderWidget extends StatelessWidget {
  final AuthState authState;

  const MoreHeaderWidget({super.key, required this.authState});

  @override
  Widget build(BuildContext context) {
    final isTablet = ResponsiveHelper.isTablet(context);

    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [AppColors.primary, AppColors.primary.withValues(alpha: 0.8)],
        ),
      ),
      child: Safe<PERSON>rea(
        child: Padding(
          padding: EdgeInsets.all(isTablet ? 32 : 24),
          child: Column(
            children: [
              // Profile Avatar
              _buildAvatar(isTablet),

              SizedBox(height: isTablet ? 20 : 16),

              // User Name or Guest
              _buildUserName(isTablet),

              SizedBox(height: isTablet ? 12 : 8),

              // User Email or Login Prompt
              _buildUserEmail(isTablet),

              // Login Button for Guests
              if (authState is! AuthUserLoaded) ...[
                SizedBox(height: isTablet ? 24 : 20),
                _buildLoginButton(context, isTablet),
              ],
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildAvatar(bool isTablet) {
    return CircleAvatar(
      radius: isTablet ? 60 : 50,
      backgroundColor: Colors.white.withValues(alpha: 0.2),
      child:
          authState is AuthUserLoaded &&
              (authState as AuthUserLoaded).user.img != null &&
              (authState as AuthUserLoaded).user.img!.isNotEmpty
          ? ClipOval(
              child: Image.network(
                (authState as AuthUserLoaded).user.img!,
                width: isTablet ? 120 : 100,
                height: isTablet ? 120 : 100,
                fit: BoxFit.cover,
                errorBuilder: (context, error, stackTrace) {
                  return Icon(
                    Icons.person,
                    size: isTablet ? 60 : 50,
                    color: Colors.white,
                  );
                },
              ),
            )
          : Icon(Icons.person, size: isTablet ? 60 : 50, color: Colors.white),
    );
  }

  Widget _buildUserName(bool isTablet) {
    return Text(
      authState is AuthUserLoaded
          ? (authState as AuthUserLoaded).user.name
          : 'guest_user'.tr(),
      style: AppTextStyles.heading.copyWith(
        fontSize: isTablet ? 28 : 24,
        color: Colors.white,
        fontWeight: FontWeight.bold,
      ),
      textAlign: TextAlign.center,
    );
  }

  Widget _buildUserEmail(bool isTablet) {
    return Text(
      authState is AuthUserLoaded
          ? (authState as AuthUserLoaded).user.email
          : 'login_to_access_features'.tr(),
      style: AppTextStyles.subtitle.copyWith(
        fontSize: isTablet ? 18 : 16,
        color: Colors.white.withValues(alpha: 0.9),
      ),
      textAlign: TextAlign.center,
    );
  }

  Widget _buildLoginButton(BuildContext context, bool isTablet) {
    return SizedBox(
      width: isTablet ? 200 : 160,
      child: CustomButton(
        text: 'login'.tr(),
        onPressed: () => context.go(AppRoutes.login),
        backgroundColor: Colors.white,
        foregroundColor: AppColors.primary,
        size: ButtonSize.medium,
        height: isTablet ? 50 : 44,
      ),
    );
  }
}
