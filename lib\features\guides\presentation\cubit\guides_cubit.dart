// features/guides/presentation/cubit/guides_cubit.dart
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:tripooo_user/features/guides/domain/usecases/get_guides_usecase.dart';
import 'package:tripooo_user/features/guides/presentation/cubit/guides_state.dart';

class GuidesCubit extends Cubit<GuidesState> {
  final GetGuidesUseCase getGuidesUseCase;

  GuidesCubit({
    required this.getGuidesUseCase,
  }) : super(GuidesInitial());

  Future<void> getGuides({
    int? countryId,
    int? dayPriceFrom,
    int? dayPriceTo,
    int? hourPriceFrom,
    int? hourPriceTo,
    bool refresh = false,
  }) async {
    if (refresh || state is GuidesInitial) {
      emit(GuidesLoading());
    }

    final currentState = state;
    int page = 1;
    
    if (currentState is GuidesLoaded && !refresh) {
      page = currentState.currentPage + 1;
      if (currentState.hasReachedMax) return;
    }

    final result = await getGuidesUseCase(GetGuidesParams(
      countryId: countryId,
      dayPriceFrom: dayPriceFrom,
      dayPriceTo: dayPriceTo,
      hourPriceFrom: hourPriceFrom,
      hourPriceTo: hourPriceTo,
      page: page,
      limit: 10,
    ));

    result.fold(
      (failure) => emit(GuidesError(message: failure.message)),
      (guides) {
        if (currentState is GuidesLoaded && !refresh) {
          final updatedGuides = List.of(currentState.guides)..addAll(guides);
          emit(GuidesLoaded(
            guides: updatedGuides,
            hasReachedMax: guides.length < 10,
            currentPage: page,
          ));
        } else {
          emit(GuidesLoaded(
            guides: guides,
            hasReachedMax: guides.length < 10,
            currentPage: page,
          ));
        }
      },
    );
  }

  Future<void> refreshGuides({
    int? countryId,
    int? dayPriceFrom,
    int? dayPriceTo,
    int? hourPriceFrom,
    int? hourPriceTo,
  }) async {
    await getGuides(
      countryId: countryId,
      dayPriceFrom: dayPriceFrom,
      dayPriceTo: dayPriceTo,
      hourPriceFrom: hourPriceFrom,
      hourPriceTo: hourPriceTo,
      refresh: true,
    );
  }

  void reset() {
    emit(GuidesInitial());
  }
}
