// features/Auth/presentation/pages/biometric_auth_screen.dart
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:go_router/go_router.dart';
import 'package:local_auth/local_auth.dart';
import 'package:tripooo_user/core/routing/app_router.dart';
import 'package:tripooo_user/core/theme/theme.dart';
import 'package:tripooo_user/core/widgets/widgets.dart';
import '../cubit/auth_cubit.dart';

class BiometricAuthScreen extends StatefulWidget {
  const BiometricAuthScreen({super.key});

  @override
  State<BiometricAuthScreen> createState() => _BiometricAuthScreenState();
}

class _BiometricAuthScreenState extends State<BiometricAuthScreen> {
  @override
  void initState() {
    super.initState();
    // Check biometric availability when screen opens
    context.read<AuthCubit>().checkBiometricAvailability();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        elevation: 0,
        leading: IconButton(
          icon: Icon(Icons.arrow_back, color: AppColors.textPrimary),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          'biometric_authentication'.tr(),
          style: AppTextStyles.heading.copyWith(
            fontSize: 20,
            color: AppColors.textPrimary,
          ),
        ),
        centerTitle: true,
      ),
      body: BlocListener<AuthCubit, AuthState>(
        listener: (context, state) async {
          if (state is AuthBiometricSuccess) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text('biometric_authentication_successful'.tr()),
                backgroundColor: AppColors.success,
              ),
            );
            // Add small delay to ensure data is saved
            await Future.delayed(const Duration(milliseconds: 200));
            if (context.mounted) {
              // Force navigation to home
              context.go(AppRoutes.home);
            }
          } else if (state is AuthError) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(state.message),
                backgroundColor: AppColors.error,
              ),
            );
          }
        },
        child: BlocBuilder<AuthCubit, AuthState>(
          builder: (context, state) {
            if (state is AuthLoading) {
              return Center(child: CircularProgressIndicator());
            }

            if (state is AuthBiometricAvailable) {
              return _buildBiometricAvailable(
                context,
                state.availableBiometrics,
              );
            }

            if (state is AuthBiometricUnavailable) {
              return _buildBiometricUnavailable(context);
            }

            return _buildInitialState(context);
          },
        ),
      ),
    );
  }

  Widget _buildInitialState(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.fingerprint, size: 64, color: AppColors.textTertiary),
          SizedBox(height: 16),
          Text(
            'checking_biometric_availability'.tr(),
            style: AppTextStyles.subtitle.copyWith(
              color: AppColors.textSecondary,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBiometricAvailable(
    BuildContext context,
    List<BiometricType> biometrics,
  ) {
    return Padding(
      padding: const EdgeInsets.all(24.0),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // Biometric Icon
          Icon(
            _getBiometricIcon(biometrics),
            size: 120,
            color: AppColors.primary,
          ),

          SizedBox(height: 32),

          // Title
          Text(
            'biometric_login'.tr(),
            style: AppTextStyles.heading.copyWith(
              fontSize: 28,
              color: AppColors.textPrimary,
            ),
            textAlign: TextAlign.center,
          ),

          SizedBox(height: 16),

          // Subtitle
          Text(
            'use_biometric_to_login'.tr(),
            style: AppTextStyles.subtitle.copyWith(
              color: AppColors.textSecondary,
            ),
            textAlign: TextAlign.center,
          ),

          SizedBox(height: 48),

          // Available Biometric Types
          Text(
            'available_biometrics'.tr(),
            style: AppTextStyles.bodyMedium.copyWith(
              fontWeight: FontWeight.w600,
              color: AppColors.textPrimary,
            ),
          ),

          SizedBox(height: 16),

          ...biometrics.map((biometric) => _buildBiometricType(biometric)),

          SizedBox(height: 48),

          // Authenticate Button
          CustomButton(
            text: 'authenticate'.tr(),
            onPressed: () =>
                context.read<AuthCubit>().authenticateWithBiometrics(),
            isFullWidth: true,
            size: ButtonSize.large,
            icon: Icon(
              _getBiometricIcon(biometrics),
              color: AppColors.white,
              size: 20,
            ),
          ),

          SizedBox(height: 16),

          // Use Password Instead
          TextButton(
            onPressed: () => context.goToLogin(),
            child: Text(
              'use_password_instead'.tr(),
              style: AppTextStyles.bodyMedium.copyWith(
                color: AppColors.primary,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBiometricUnavailable(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(24.0),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.security, size: 120, color: AppColors.textTertiary),

          SizedBox(height: 32),

          Text(
            'biometric_unavailable'.tr(),
            style: AppTextStyles.heading.copyWith(
              fontSize: 24,
              color: AppColors.textPrimary,
            ),
            textAlign: TextAlign.center,
          ),

          SizedBox(height: 16),

          Text(
            'biometric_unavailable_message'.tr(),
            style: AppTextStyles.subtitle.copyWith(
              color: AppColors.textSecondary,
            ),
            textAlign: TextAlign.center,
          ),

          SizedBox(height: 48),

          CustomButton(
            text: 'continue_with_password'.tr(),
            onPressed: () => context.goToLogin(),
            isFullWidth: true,
            size: ButtonSize.large,
          ),

          SizedBox(height: 16),

          TextButton(
            onPressed: () =>
                context.read<AuthCubit>().checkBiometricAvailability(),
            child: Text(
              'check_again'.tr(),
              style: AppTextStyles.bodyMedium.copyWith(
                color: AppColors.primary,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBiometricType(BiometricType biometric) {
    String name;
    IconData icon;

    switch (biometric) {
      case BiometricType.face:
        name = 'face_id'.tr();
        icon = Icons.face;
        break;
      case BiometricType.fingerprint:
        name = 'fingerprint'.tr();
        icon = Icons.fingerprint;
        break;
      case BiometricType.iris:
        name = 'iris_scan'.tr();
        icon = Icons.visibility;
        break;
      case BiometricType.strong:
        name = 'strong_biometric'.tr();
        icon = Icons.security;
        break;
      case BiometricType.weak:
        name = 'weak_biometric'.tr();
        icon = Icons.security_outlined;
        break;
    }

    return Container(
      margin: EdgeInsets.only(bottom: 8),
      padding: EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      decoration: BoxDecoration(
        color: AppColors.surfaceVariant,
        borderRadius: BorderRadius.circular(AppDimensions.radiusMedium),
      ),
      child: Row(
        children: [
          Icon(icon, color: AppColors.primary, size: 20),
          SizedBox(width: 12),
          Text(
            name,
            style: AppTextStyles.bodyMedium.copyWith(
              color: AppColors.textPrimary,
            ),
          ),
        ],
      ),
    );
  }

  IconData _getBiometricIcon(List<BiometricType> biometrics) {
    if (biometrics.contains(BiometricType.face)) {
      return Icons.face;
    } else if (biometrics.contains(BiometricType.fingerprint)) {
      return Icons.fingerprint;
    } else if (biometrics.contains(BiometricType.iris)) {
      return Icons.visibility;
    } else {
      return Icons.security;
    }
  }
}
