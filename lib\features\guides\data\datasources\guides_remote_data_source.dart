// features/guides/data/datasources/guides_remote_data_source.dart
import 'package:dio/dio.dart';
import 'package:tripooo_user/core/error/exceptions.dart';
import 'package:tripooo_user/core/network/api_endpoints.dart';
import 'package:tripooo_user/features/guides/data/models/guide_model.dart';

abstract class GuidesRemoteDataSource {
  /// Get all guides with optional filters
  Future<List<GuideModel>> getGuides({
    int? countryId,
    int? dayPriceFrom,
    int? dayPriceTo,
    int? hourPriceFrom,
    int? hourPriceTo,
    int page = 1,
    int limit = 10,
  });

  /// Get featured guides for home screen
  Future<List<GuideModel>> getFeaturedGuides({int limit = 4});

  /// Get guide details by ID
  Future<GuideModel> getGuideById(int id);

  /// Search guides by name or info
  Future<List<GuideModel>> searchGuides({
    required String query,
    int page = 1,
    int limit = 10,
  });
}

class GuidesRemoteDataSourceImpl implements GuidesRemoteDataSource {
  final Dio dio;

  GuidesRemoteDataSourceImpl({required this.dio});

  @override
  Future<List<GuideModel>> getGuides({
    int? countryId,
    int? dayPriceFrom,
    int? dayPriceTo,
    int? hourPriceFrom,
    int? hourPriceTo,
    int page = 1,
    int limit = 10,
  }) async {
    try {
      final queryParameters = <String, dynamic>{'page': page, 'limit': limit};

      if (countryId != null) queryParameters['country_id'] = countryId;
      if (dayPriceFrom != null)
        queryParameters['day_price_from'] = dayPriceFrom;
      if (dayPriceTo != null) queryParameters['day_price_to'] = dayPriceTo;
      if (hourPriceFrom != null)
        queryParameters['hour_price_from'] = hourPriceFrom;
      if (hourPriceTo != null) queryParameters['hour_price_to'] = hourPriceTo;

      final response = await dio.get(
        ApiEndpoints.guides,
        queryParameters: queryParameters,
      );

      if (response.statusCode == 200) {
        final data = response.data;
        if (data['status'] == 200 && data['data'] != null) {
          final List<dynamic> guidesJson = data['data'];
          return guidesJson.map((json) => GuideModel.fromJson(json)).toList();
        } else {
          throw ServerException(data['msg'] ?? 'Failed to get guides');
        }
      } else {
        throw ServerException('Server error: ${response.statusCode}');
      }
    } on DioException catch (e) {
      throw ServerException(e.message ?? 'Network error');
    } catch (e) {
      throw ServerException(e.toString());
    }
  }

  @override
  Future<List<GuideModel>> getFeaturedGuides({int limit = 4}) async {
    try {
      final response = await dio.get(
        ApiEndpoints.guides,
        queryParameters: {
          'limit': limit,
          'featured': true, // Assuming API supports featured parameter
        },
      );

      if (response.statusCode == 200) {
        final data = response.data;
        if (data['status'] == 200 && data['data'] != null) {
          final List<dynamic> guidesJson = data['data'];
          return guidesJson.map((json) => GuideModel.fromJson(json)).toList();
        } else {
          throw ServerException(data['msg'] ?? 'Failed to get featured guides');
        }
      } else {
        throw ServerException('Server error: ${response.statusCode}');
      }
    } on DioException catch (e) {
      throw ServerException(e.message ?? 'Network error');
    } catch (e) {
      throw ServerException(e.toString());
    }
  }

  @override
  Future<GuideModel> getGuideById(int id) async {
    try {
      final response = await dio.get('${ApiEndpoints.guides}/$id');

      if (response.statusCode == 200) {
        final data = response.data;
        if (data['status'] == 200 && data['data'] != null) {
          return GuideModel.fromJson(data['data']);
        } else {
          throw ServerException(data['msg'] ?? 'Failed to get guide');
        }
      } else {
        throw ServerException('Server error: ${response.statusCode}');
      }
    } on DioException catch (e) {
      throw ServerException(e.message ?? 'Network error');
    } catch (e) {
      throw ServerException(e.toString());
    }
  }

  @override
  Future<List<GuideModel>> searchGuides({
    required String query,
    int page = 1,
    int limit = 10,
  }) async {
    try {
      final response = await dio.get(
        '${ApiEndpoints.guides}/search',
        queryParameters: {'query': query, 'page': page, 'limit': limit},
      );

      if (response.statusCode == 200) {
        final data = response.data;
        if (data['status'] == 200 && data['data'] != null) {
          final List<dynamic> guidesJson = data['data'];
          return guidesJson.map((json) => GuideModel.fromJson(json)).toList();
        } else {
          throw ServerException(data['msg'] ?? 'Failed to search guides');
        }
      } else {
        throw ServerException('Server error: ${response.statusCode}');
      }
    } on DioException catch (e) {
      throw ServerException(e.message ?? 'Network error');
    } catch (e) {
      throw ServerException(e.toString());
    }
  }
}
