// features/Auth/domain/usecases/regenerate_code.dart
import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';
import '../../../../core/error/failures.dart';
import '../../../../core/usecases/usecase.dart';
import '../repositories/auth_repository.dart';

class RegenerateCode implements UseCase<String, RegenerateCodeParams> {
  final AuthRepository repository;

  const RegenerateCode(this.repository);

  @override
  Future<Either<Failure, String>> call(RegenerateCodeParams params) async {
    return await repository.regenerateCode(mobile: params.mobile);
  }
}

class RegenerateCodeParams extends Equatable {
  final String mobile;

  const RegenerateCodeParams({required this.mobile});

  @override
  List<Object> get props => [mobile];
}
