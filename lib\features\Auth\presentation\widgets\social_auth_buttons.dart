// features/Auth/presentation/widgets/social_auth_buttons.dart
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:easy_localization/easy_localization.dart';
import '../cubit/auth_cubit.dart';
import '../../data/models/social_auth_models.dart';

class SocialAuthButtons extends StatelessWidget {
  final int userType;
  final String? fcmToken;

  const SocialAuthButtons({super.key, this.userType = 1, this.fcmToken});

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        // Google Sign In
        SizedBox(
          width: double.infinity,
          child: OutlinedButton.icon(
            style: OutlinedButton.styleFrom(
              padding: EdgeInsets.symmetric(vertical: 12),
              side: BorderSide(color: Colors.grey[300]!),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
            onPressed: () {
              context.read<AuthCubit>().signInWithGoogle(
                fcmToken: fcmToken,
                userType: userType,
              );
            },
            icon: Image.asset(
              'assets/images/google_logo.png',
              height: 24,
              width: 24,
              errorBuilder: (context, error, stackTrace) {
                return Icon(Icons.g_mobiledata, color: Colors.red, size: 24);
              },
            ),
            label: Text(
              'continue_with_google'.tr(),
              style: TextStyle(
                fontSize: 16,
                color: Colors.black87,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ),

        SizedBox(height: 12),

        // Facebook Sign In
        SizedBox(
          width: double.infinity,
          child: OutlinedButton.icon(
            style: OutlinedButton.styleFrom(
              padding: EdgeInsets.symmetric(vertical: 12),
              side: BorderSide(color: Colors.grey[300]!),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
            onPressed: () {
              context.read<AuthCubit>().signInWithFacebook(
                fcmToken: fcmToken,
                userType: userType,
              );
            },
            icon: Image.asset(
              'assets/images/facebook_logo.png',
              height: 24,
              width: 24,
              errorBuilder: (context, error, stackTrace) {
                return Icon(Icons.facebook, color: Colors.blue, size: 24);
              },
            ),
            label: Text(
              'continue_with_facebook'.tr(),
              style: TextStyle(
                fontSize: 16,
                color: Colors.black87,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ),

        SizedBox(height: 12),

        // Apple Sign In (temporarily disabled)
        // TODO: Re-enable after proper iOS setup
        /*
        if (Theme.of(context).platform == TargetPlatform.iOS) ...[
          SizedBox(
            width: double.infinity,
            child: OutlinedButton.icon(
              style: OutlinedButton.styleFrom(
                padding: EdgeInsets.symmetric(vertical: 12),
                side: BorderSide(color: Colors.grey[300]!),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
              onPressed: () {
                context.read<AuthCubit>().signInWithApple(
                  fcmToken: fcmToken,
                  userType: userType,
                );
              },
              icon: Image.asset(
                'assets/images/apple_logo.png',
                height: 24,
                width: 24,
                errorBuilder: (context, error, stackTrace) {
                  return Icon(Icons.apple, color: Colors.black, size: 24);
                },
              ),
              label: Text(
                'continue_with_apple'.tr(),
                style: TextStyle(
                  fontSize: 16,
                  color: Colors.black87,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
          ),
        ],
        */
      ],
    );
  }
}

class SocialAuthButton extends StatelessWidget {
  final SocialProvider provider;
  final VoidCallback onPressed;
  final bool isLoading;

  const SocialAuthButton({
    super.key,
    required this.provider,
    required this.onPressed,
    this.isLoading = false,
  });

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: double.infinity,
      child: OutlinedButton.icon(
        style: OutlinedButton.styleFrom(
          padding: EdgeInsets.symmetric(vertical: 12),
          side: BorderSide(color: Colors.grey[300]!),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
        ),
        onPressed: isLoading ? null : onPressed,
        icon: isLoading
            ? SizedBox(
                width: 24,
                height: 24,
                child: CircularProgressIndicator(strokeWidth: 2),
              )
            : _getProviderIcon(),
        label: Text(
          _getProviderText(),
          style: TextStyle(
            fontSize: 16,
            color: Colors.black87,
            fontWeight: FontWeight.w500,
          ),
        ),
      ),
    );
  }

  Widget _getProviderIcon() {
    switch (provider) {
      case SocialProvider.google:
        return Image.asset(
          'assets/images/google_logo.png',
          height: 24,
          width: 24,
          errorBuilder: (context, error, stackTrace) {
            return Icon(Icons.g_mobiledata, color: Colors.red, size: 24);
          },
        );
      case SocialProvider.facebook:
        return Image.asset(
          'assets/images/facebook_logo.png',
          height: 24,
          width: 24,
          errorBuilder: (context, error, stackTrace) {
            return Icon(Icons.facebook, color: Colors.blue, size: 24);
          },
        );
      case SocialProvider.apple:
        return Image.asset(
          'assets/images/apple_logo.png',
          height: 24,
          width: 24,
          errorBuilder: (context, error, stackTrace) {
            return Icon(Icons.apple, color: Colors.black, size: 24);
          },
        );
    }
  }

  String _getProviderText() {
    switch (provider) {
      case SocialProvider.google:
        return 'continue_with_google'.tr();
      case SocialProvider.facebook:
        return 'continue_with_facebook'.tr();
      case SocialProvider.apple:
        return 'continue_with_apple'.tr();
    }
  }
}
