// features/guides/presentation/cubit/featured_guides_cubit.dart
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:tripooo_user/features/guides/domain/usecases/get_featured_guides_usecase.dart';
import 'package:tripooo_user/features/guides/presentation/cubit/guides_state.dart';

class FeaturedGuidesCubit extends Cubit<FeaturedGuidesState> {
  final GetFeaturedGuidesUseCase getFeaturedGuidesUseCase;

  FeaturedGuidesCubit({required this.getFeaturedGuidesUseCase})
    : super(FeaturedGuidesInitial());

  Future<void> getFeaturedGuides({int limit = 4}) async {
    emit(FeaturedGuidesLoading());

    final result = await getFeaturedGuidesUseCase(
      GetFeaturedGuidesParams(limit: limit),
    );

    result.fold(
      (failure) => emit(FeaturedGuidesError(message: failure.message)),
      (guides) => emit(FeaturedGuidesLoaded(guides: guides)),
    );
  }

  Future<void> refreshFeaturedGuides({int limit = 4}) async {
    await getFeaturedGuides(limit: limit);
  }

  Future<void> getGuideById(int guideId) async {
    emit(FeaturedGuidesLoading());

    // For now, we'll get all guides and find the one with the matching ID
    // In a real app, you'd have a separate API endpoint for this
    final result = await getFeaturedGuidesUseCase(
      GetFeaturedGuidesParams(
        limit: 20,
      ), // Get more guides to find the one we need
    );

    result.fold(
      (failure) => emit(FeaturedGuidesError(message: failure.message)),
      (guides) {
        final guide = guides.firstWhere(
          (g) => g.id == guideId,
          orElse: () => throw Exception('Guide not found'),
        );
        emit(FeaturedGuideDetailsLoaded(guide: guide));
      },
    );
  }

  void reset() {
    emit(FeaturedGuidesInitial());
  }
}
