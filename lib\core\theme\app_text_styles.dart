// core/theme/app_text_styles.dart
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'app_colors.dart';

class AppTextStyles {
  // Font Family
  static String fontFamily = GoogleFonts.almarai().fontFamily.toString();

  // Heading Styles - Based on design specs
  static TextStyle heading = TextStyle(
    color: AppColors.textPrimary, // #000
    fontFamily: fontFamily,
    fontSize: 30,
    fontWeight: FontWeight.w700, // 700
    height: 1.0, // 100% line height
    fontStyle: FontStyle.normal,
  );
  static TextStyle title = TextStyle(
    color: AppColors.textPrimary, // #000
    fontFamily: fontFamily,
    fontSize: 24,
    fontWeight: FontWeight.w700,
  );
  // Subtitle Styles - Based on design specs
  static TextStyle subtitle = TextStyle(
    color: AppColors.textPrimary, // #000
    fontFamily: fontFamily,
    fontSize: 16,
    fontWeight: FontWeight.w400, // 400
    height: 1.6, // 160% line height (25.6px / 16px = 1.6)
    fontStyle: FontStyle.normal,
  );

  // Button Text - Based on design specs
  static TextStyle buttonText = TextStyle(
    color: AppColors.white, // #FFF for primary buttons
    fontFamily: fontFamily,
    fontSize: 16,
    fontWeight: FontWeight.w700, // 700
    height: 1.0, // 100% line height
    fontStyle: FontStyle.normal,
  );

  // Body Text Styles
  static TextStyle bodyLarge = TextStyle(
    color: AppColors.textPrimary,
    fontFamily: fontFamily,
    fontSize: 18,
    fontWeight: FontWeight.w400,
    height: 1.5,
    fontStyle: FontStyle.normal,
  );

  static TextStyle bodyMedium = TextStyle(
    color: AppColors.textPrimary,
    fontFamily: fontFamily,
    fontSize: 16,
    fontWeight: FontWeight.w400,
    height: 1.5,
    fontStyle: FontStyle.normal,
  );

  static TextStyle bodySmall = TextStyle(
    color: AppColors.textSecondary,
    fontFamily: fontFamily,
    fontSize: 14,
    fontWeight: FontWeight.w400,
    height: 1,
    fontStyle: FontStyle.normal,
  );

  // Label Styles
  static TextStyle labelLarge = TextStyle(
    color: AppColors.textPrimary,
    fontFamily: fontFamily,
    fontSize: 16,
    fontWeight: FontWeight.w500,
    height: 1.2,
    fontStyle: FontStyle.normal,
  );

  static TextStyle labelMedium = TextStyle(
    color: AppColors.textPrimary,
    fontFamily: fontFamily,
    fontSize: 14,
    fontWeight: FontWeight.w500,
    height: 1.2,
    fontStyle: FontStyle.normal,
  );

  static TextStyle labelSmall = TextStyle(
    color: AppColors.textSecondary,
    fontFamily: fontFamily,
    fontSize: 12,
    fontWeight: FontWeight.w500,
    height: 1.2,
    fontStyle: FontStyle.normal,
  );

  // Title Styles
  static TextStyle titleLarge = TextStyle(
    color: AppColors.textPrimary,
    fontFamily: fontFamily,
    fontSize: 24,
    fontWeight: FontWeight.w600,
    height: 1.2,
    fontStyle: FontStyle.normal,
  );

  static TextStyle titleMedium = TextStyle(
    color: AppColors.textPrimary,
    fontFamily: fontFamily,
    fontSize: 20,
    fontWeight: FontWeight.w600,
    height: 1.2,
    fontStyle: FontStyle.normal,
  );

  static TextStyle titleSmall = TextStyle(
    color: AppColors.textPrimary,
    fontFamily: fontFamily,
    fontSize: 18,
    fontWeight: FontWeight.w600,
    height: 1.2,
    fontStyle: FontStyle.normal,
  );

  // Special Text Styles
  static TextStyle hintText = TextStyle(
    color: AppColors.hintText,
    fontFamily: fontFamily,
    fontSize: 16,
    fontWeight: FontWeight.w400,
    height: 1.4,
    fontStyle: FontStyle.normal,
  );

  static TextStyle errorText = TextStyle(
    color: AppColors.error,
    fontFamily: fontFamily,
    fontSize: 12,
    fontWeight: FontWeight.w400,
    height: 1.3,
    fontStyle: FontStyle.normal,
  );

  static TextStyle linkText = TextStyle(
    color: AppColors.primary,
    fontFamily: fontFamily,
    fontSize: 16,
    fontWeight: FontWeight.w500,
    height: 1.4,
    decoration: TextDecoration.underline,
    fontStyle: FontStyle.normal,
  );

  static TextStyle captionText = TextStyle(
    color: AppColors.textSecondary,
    fontFamily: fontFamily,
    fontSize: 12,
    fontWeight: FontWeight.w400,
    height: 1.3,
    fontStyle: FontStyle.normal,
  );

  // Tab Text Styles
  static TextStyle tabSelected = TextStyle(
    color: AppColors.white,
    fontFamily: fontFamily,
    fontSize: 16,
    fontWeight: FontWeight.w600,
    height: 1.2,
    fontStyle: FontStyle.normal,
  );

  static TextStyle tabUnselected = TextStyle(
    color: AppColors.textSecondary,
    fontFamily: fontFamily,
    fontSize: 16,
    fontWeight: FontWeight.w600,
    height: 1.2,
    fontStyle: FontStyle.normal,
  );

  // Form Field Label
  static TextStyle fieldLabel = TextStyle(
    color: AppColors.textPrimary,
    fontFamily: fontFamily,
    fontSize: 16,
    fontWeight: FontWeight.w500,
    height: 1.2,
    fontStyle: FontStyle.normal,
  );

  // Country Code Text (for phone field)
  static TextStyle countryCodeText = TextStyle(
    color: AppColors.white,
    fontFamily: fontFamily,
    fontSize: 16,
    fontWeight: FontWeight.w500,
    height: 1.2,
    fontStyle: FontStyle.normal,
  );

  // App Bar Title
  static TextStyle appBarTitle = TextStyle(
    color: AppColors.textPrimary,
    fontFamily: fontFamily,
    fontSize: 20,
    fontWeight: FontWeight.w600,
    height: 1.2,
    fontStyle: FontStyle.normal,
  );
}
