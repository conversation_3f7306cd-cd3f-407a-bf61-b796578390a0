# 🔔 FCM Token Usage Examples

This guide shows you how to use FCM tokens in your Tripooo app.

## 📋 What's Already Set Up

✅ **FCM Service**: Handles Firebase messaging initialization and token generation  
✅ **FCM Helper**: Manages token storage and retrieval  
✅ **FCM Utils**: Utility functions for authentication  
✅ **Auth Extensions**: Easy-to-use methods with automatic FCM token handling  

## 🚀 How to Use FCM Tokens

### Method 1: Using Extension Methods (Recommended)

Import the extension and use the new methods:

```dart
import 'package:flutter_bloc/flutter_bloc.dart';
import 'features/Auth/presentation/cubit/auth_cubit_extensions.dart';

// Login with automatic FCM token
void _login() {
  context.read<AuthCubit>().loginWithFCM(
    mobile: mobileController.text,
    password: passwordController.text,
  );
}

// Register with automatic FCM token
void _register() {
  context.read<AuthCubit>().registerWithFCM(
    name: nameController.text,
    email: emailController.text,
    mobile: mobileController.text,
    password: passwordController.text,
    passwordConfirmation: confirmPasswordController.text,
    userType: 1, // 1 for client, 2 for guide
  );
}

// Social login with automatic FCM token
void _signInWithGoogle() {
  context.read<AuthCubit>().signInWithGoogleWithFCM(userType: 1);
}

void _signInWithFacebook() {
  context.read<AuthCubit>().signInWithFacebookWithFCM(userType: 1);
}

void _signInWithApple() {
  context.read<AuthCubit>().signInWithAppleWithFCM(userType: 1);
}
```

### Method 2: Manual FCM Token Handling

```dart
import 'core/utils/fcm_utils.dart';

// Get FCM token manually
void _loginManually() async {
  String? fcmToken = await FCMUtils.getTokenForAuth();
  
  context.read<AuthCubit>().login(
    mobile: mobileController.text,
    password: passwordController.text,
    fcmToken: fcmToken,
  );
}

// Refresh FCM token if needed
void _refreshAndLogin() async {
  String? fcmToken = await FCMUtils.refreshTokenForAuth();
  
  context.read<AuthCubit>().login(
    mobile: mobileController.text,
    password: passwordController.text,
    fcmToken: fcmToken,
  );
}
```

### Method 3: Direct FCM Service Usage

```dart
import 'core/services/fcm_service.dart';

void _getTokenDirectly() async {
  String? fcmToken = await FCMService.getToken();
  print('FCM Token: $fcmToken');
  
  // Use the token as needed
  context.read<AuthCubit>().login(
    mobile: mobileController.text,
    password: passwordController.text,
    fcmToken: fcmToken,
  );
}
```

## 🔧 Advanced Usage

### Subscribe to Topics

```dart
import 'core/services/fcm_service.dart';

// Subscribe to general notifications
await FCMService.subscribeToTopic('general_notifications');

// Subscribe to user-specific topics
await FCMService.subscribeToTopic('user_${userId}');

// Subscribe to location-based topics
await FCMService.subscribeToTopic('cairo_tours');
```

### Handle Token Refresh

The app automatically handles token refresh, but you can also listen manually:

```dart
import 'core/services/fcm_service.dart';

FCMService.onTokenRefresh((newToken) {
  print('New FCM token: $newToken');
  // Send new token to your server if needed
});
```

### Get Saved Token

```dart
import 'core/services/fcm_helper.dart';

// Get token from local storage
String? savedToken = await FCMHelper.getSavedFCMToken();

// Get token (saved or new)
String? token = await FCMHelper.getFCMToken();
```

## 📱 Complete Login Example

```dart
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'features/Auth/presentation/cubit/auth_cubit.dart';
import 'features/Auth/presentation/cubit/auth_cubit_extensions.dart';

class LoginScreen extends StatefulWidget {
  @override
  _LoginScreenState createState() => _LoginScreenState();
}

class _LoginScreenState extends State<LoginScreen> {
  final TextEditingController mobileController = TextEditingController();
  final TextEditingController passwordController = TextEditingController();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Column(
        children: [
          TextField(
            controller: mobileController,
            decoration: InputDecoration(labelText: 'Mobile'),
          ),
          TextField(
            controller: passwordController,
            decoration: InputDecoration(labelText: 'Password'),
            obscureText: true,
          ),
          ElevatedButton(
            onPressed: _login,
            child: Text('Login'),
          ),
          ElevatedButton(
            onPressed: _signInWithGoogle,
            child: Text('Sign in with Google'),
          ),
        ],
      ),
    );
  }

  void _login() {
    // This automatically gets FCM token and includes it in login
    context.read<AuthCubit>().loginWithFCM(
      mobile: mobileController.text,
      password: passwordController.text,
    );
  }

  void _signInWithGoogle() {
    // This automatically gets FCM token and includes it in Google sign-in
    context.read<AuthCubit>().signInWithGoogleWithFCM(userType: 1);
  }
}
```

## 🔍 Debugging FCM

To debug FCM tokens, check the console output. The app will print:
- FCM initialization status
- Token generation success/failure
- Token refresh events
- Permission status

## 📝 Notes

1. **Automatic Handling**: Use extension methods for automatic FCM token handling
2. **Token Storage**: Tokens are automatically saved locally and refreshed when needed
3. **Error Handling**: All methods handle errors gracefully and continue without FCM if it fails
4. **Optional Parameter**: FCM token is optional in all auth methods, so the app works even if FCM fails

## 🚨 Important

Make sure to:
1. Add `google-services.json` to `android/app/` for Android
2. Add `GoogleService-Info.plist` to `ios/Runner/` for iOS
3. Configure Firebase project with your app's package name
4. Test on real devices (FCM doesn't work on emulators without Google Play Services)
