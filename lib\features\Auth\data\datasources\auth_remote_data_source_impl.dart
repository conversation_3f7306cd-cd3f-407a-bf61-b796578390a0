// features/Auth/data/datasources/auth_remote_data_source_impl.dart
import 'dart:developer';

import 'package:dio/dio.dart';
import '../../../../core/network/dio_helper.dart';
import '../../../../core/network/api_endpoints.dart';
import '../../../../core/error/exceptions.dart';
import '../models/user_model.dart';
import '../models/auth_request_models.dart';
import '../models/auth_response_models.dart';
import '../models/social_auth_models.dart';
import 'auth_remote_data_source.dart';

class AuthRemoteDataSourceImpl implements AuthRemoteDataSource {
  @override
  Future<UserModel> getUser(String token) async {
    try {
      final response = await DioHelper.getData(
        url: ApiEndpoints.user,
        token: token,
      );

      if (response.statusCode == 200) {
        final data = response.data;
        if (data['data'] != null) {
          return UserModel.fromMap(data['data']);
        } else {
          throw ServerException('Invalid response format', response.statusCode);
        }
      } else {
        throw ServerException(
          response.data['msg'] ?? 'Failed to get user',
          response.statusCode,
        );
      }
    } on DioException catch (e) {
      throw _handleDioException(e);
    } catch (e) {
      throw ServerException('Unexpected error: $e');
    }
  }

  @override
  Future<UserModel> checkToken(String token) async {
    try {
      final response = await DioHelper.getData(
        url: ApiEndpoints.checkToken,
        token: token,
      );

      if (response.statusCode == 200) {
        final data = response.data;
        if (data['data'] != null) {
          return UserModel.fromMap(data['data']);
        } else {
          throw ServerException('Invalid response format', response.statusCode);
        }
      } else {
        throw ServerException(
          response.data['msg'] ?? 'Token validation failed',
          response.statusCode,
        );
      }
    } on DioException catch (e) {
      throw _handleDioException(e);
    } catch (e) {
      throw ServerException('Unexpected error: $e');
    }
  }

  @override
  Future<void> register(RegisterRequest request) async {
    try {
      final data = request.toMap();

      // Add file if provided
      if (request.file != null) {
        data['file'] = await MultipartFile.fromFile(request.file!.path);
      }

      final response = await DioHelper.postDataWithFile(
        url: ApiEndpoints.register,
        data: data,
      );

      if (response.statusCode != 200) {
        throw ServerException(
          response.data['msg'] ?? 'Registration failed',
          response.statusCode,
        );
      }
    } on DioException catch (e) {
      throw _handleDioException(e);
    } catch (e) {
      throw ServerException('Unexpected error: $e');
    }
  }

  @override
  Future<AuthResponse> mobileCheck(MobileCheckRequest request) async {
    try {
      log('mobileCheck request: ${request.toMap()}', name: 'mobileCheck');
      final response = await DioHelper.postData(
        url: ApiEndpoints.mobileCheck,
        data: request.toMap(),
      );

      if (response.statusCode == 200) {
        return AuthResponse.fromMap(response.data);
      } else {
        throw ServerException(
          response.data['msg'] ?? 'Mobile verification failed',
          response.statusCode,
        );
      }
    } on DioException catch (e) {
      throw _handleDioException(e);
    } catch (e) {
      throw ServerException('Unexpected error: $e');
    }
  }

  @override
  Future<SimpleResponse> regenerateCode(RegenerateCodeRequest request) async {
    try {
      final response = await DioHelper.postData(
        url: ApiEndpoints.regenerateCode,
        data: request.toMap(),
      );

      if (response.statusCode == 200) {
        return SimpleResponse.fromMap(response.data);
      } else {
        throw ServerException(
          response.data['msg'] ?? 'Failed to regenerate code',
          response.statusCode,
        );
      }
    } on DioException catch (e) {
      throw _handleDioException(e);
    } catch (e) {
      throw ServerException('Unexpected error: $e');
    }
  }

  @override
  Future<AuthResponse> login(LoginRequest request) async {
    try {
      final response = await DioHelper.postData(
        url: ApiEndpoints.login,
        data: request.toMap(),
      );

      if (response.statusCode == 200) {
        return AuthResponse.fromMap(response.data);
      } else {
        throw ServerException(
          response.data['msg'] ?? 'Login failed',
          response.statusCode,
        );
      }
    } on DioException catch (e) {
      throw _handleDioException(e);
    } catch (e) {
      throw ServerException('Unexpected error: $e');
    }
  }

  @override
  Future<UserModel> updateUser(UpdateUserRequest request, String token) async {
    try {
      final data = request.toMap();

      // Add file if provided
      if (request.file != null) {
        data['file'] = await MultipartFile.fromFile(request.file!.path);
      }

      final response = await DioHelper.postDataWithFile(
        url: ApiEndpoints.userUpdate,
        data: data,
        token: token,
      );

      if (response.statusCode == 200) {
        final responseData = response.data;
        if (responseData['data'] != null) {
          return UserModel.fromMap(responseData['data']);
        } else {
          throw ServerException('Invalid response format', response.statusCode);
        }
      } else {
        throw ServerException(
          response.data['msg'] ?? 'Failed to update user',
          response.statusCode,
        );
      }
    } on DioException catch (e) {
      throw _handleDioException(e);
    } catch (e) {
      throw ServerException('Unexpected error: $e');
    }
  }

  @override
  Future<SimpleResponse> changePassword(
    ChangePasswordRequest request,
    String token,
  ) async {
    try {
      final response = await DioHelper.postData(
        url: ApiEndpoints.changePassword,
        data: request.toMap(),
        token: token,
      );

      if (response.statusCode == 200) {
        return SimpleResponse.fromMap(response.data);
      } else {
        throw ServerException(
          response.data['msg'] ?? 'Failed to change password',
          response.statusCode,
        );
      }
    } on DioException catch (e) {
      throw _handleDioException(e);
    } catch (e) {
      throw ServerException('Unexpected error: $e');
    }
  }

  @override
  Future<SimpleResponse> changeMobileNumber(
    ChangeMobileRequest request,
    String token,
  ) async {
    try {
      final response = await DioHelper.postData(
        url: ApiEndpoints.changeMobileNumber,
        data: request.toMap(),
        token: token,
      );

      if (response.statusCode == 200) {
        return SimpleResponse.fromMap(response.data);
      } else {
        throw ServerException(
          response.data['msg'] ?? 'Failed to change mobile number',
          response.statusCode,
        );
      }
    } on DioException catch (e) {
      throw _handleDioException(e);
    } catch (e) {
      throw ServerException('Unexpected error: $e');
    }
  }

  @override
  Future<LogoutResponse> logout(String token) async {
    try {
      final response = await DioHelper.postData(
        url: ApiEndpoints.logout,
        token: token,
      );

      if (response.statusCode == 200) {
        return LogoutResponse.fromMap(response.data);
      } else {
        throw ServerException(
          response.data['msg'] ?? 'Logout failed',
          response.statusCode,
        );
      }
    } on DioException catch (e) {
      throw _handleDioException(e);
    } catch (e) {
      throw ServerException('Unexpected error: $e');
    }
  }

  @override
  Future<SimpleResponse> sendResetCode(SendResetCodeRequest request) async {
    try {
      final response = await DioHelper.postData(
        url: ApiEndpoints.sendResetCode,
        data: request.toMap(),
      );

      if (response.statusCode == 200) {
        return SimpleResponse.fromMap(response.data);
      } else {
        throw ServerException(
          response.data['msg'] ?? 'Failed to send reset code',
          response.statusCode,
        );
      }
    } on DioException catch (e) {
      throw _handleDioException(e);
    } catch (e) {
      throw ServerException('Unexpected error: $e');
    }
  }

  @override
  Future<SimpleResponse> verifyResetCode(VerifyResetCodeRequest request) async {
    try {
      final response = await DioHelper.postData(
        url: ApiEndpoints.verifyResetCode,
        data: request.toMap(),
      );

      if (response.statusCode == 200) {
        return SimpleResponse.fromMap(response.data);
      } else {
        throw ServerException(
          response.data['msg'] ?? 'Failed to verify reset code',
          response.statusCode,
        );
      }
    } on DioException catch (e) {
      throw _handleDioException(e);
    } catch (e) {
      throw ServerException('Unexpected error: $e');
    }
  }

  @override
  Future<SimpleResponse> resetPassword(ResetPasswordRequest request) async {
    try {
      final response = await DioHelper.postData(
        url: ApiEndpoints.resetPassword,
        data: request.toMap(),
      );

      if (response.statusCode == 200) {
        return SimpleResponse.fromMap(response.data);
      } else {
        throw ServerException(
          response.data['msg'] ?? 'Failed to reset password',
          response.statusCode,
        );
      }
    } on DioException catch (e) {
      throw _handleDioException(e);
    } catch (e) {
      throw ServerException('Unexpected error: $e');
    }
  }

  @override
  Future<AuthResponse> socialAuth(SocialAuthRequest request) async {
    try {
      final response = await DioHelper.postData(
        url: ApiEndpoints.socialAuth,
        data: request.toMap(),
      );

      if (response.statusCode == 200) {
        return AuthResponse.fromMap(response.data);
      } else {
        throw ServerException(
          response.data['msg'] ?? 'Social authentication failed',
          response.statusCode,
        );
      }
    } on DioException catch (e) {
      throw _handleDioException(e);
    } catch (e) {
      throw ServerException('Unexpected error: $e');
    }
  }

  Exception _handleDioException(DioException e) {
    switch (e.type) {
      case DioExceptionType.connectionTimeout:
      case DioExceptionType.sendTimeout:
      case DioExceptionType.receiveTimeout:
        return NetworkException(
          'Connection timeout. Please check your internet connection.',
        );
      case DioExceptionType.connectionError:
        return NetworkException(
          'No internet connection. Please check your network.',
        );
      case DioExceptionType.badResponse:
        final statusCode = e.response?.statusCode;
        final message =
            e.response?.data?['msg'] ??
            e.response?.data?['message'] ??
            'Server error';

        if (statusCode == 401) {
          return UnauthorizedException(message);
        } else if (statusCode == 404) {
          return NotFoundException(message);
        } else {
          return ServerException(message, statusCode);
        }
      case DioExceptionType.cancel:
        return NetworkException('Request was cancelled');
      case DioExceptionType.unknown:
      default:
        return NetworkException('Network error: ${e.message}');
    }
  }
}
