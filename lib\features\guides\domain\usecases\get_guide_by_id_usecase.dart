// features/guides/domain/usecases/get_guide_by_id_usecase.dart
import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';
import 'package:tripooo_user/core/error/failures.dart';
import 'package:tripooo_user/core/usecases/usecase.dart';
import 'package:tripooo_user/features/guides/domain/entities/guide_entity.dart';
import 'package:tripooo_user/features/guides/domain/repositories/guides_repository.dart';

class GetGuideByIdUseCase implements UseCase<GuideEntity, GetGuideByIdParams> {
  final GuidesRepository repository;

  GetGuideByIdUseCase(this.repository);

  @override
  Future<Either<Failure, GuideEntity>> call(GetGuideByIdParams params) async {
    return await repository.getGuideById(params.id);
  }
}

class GetGuideByIdParams extends Equatable {
  final int id;

  const GetGuideByIdParams({required this.id});

  @override
  List<Object> get props => [id];
}
