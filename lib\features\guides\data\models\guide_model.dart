// features/guides/data/models/guide_model.dart
import 'package:tripooo_user/features/guides/domain/entities/guide_entity.dart';

class GuideModel extends GuideEntity {
  const GuideModel({
    required super.id,
    required super.name,
    required super.email,
    super.img,
    required super.mobile,
    required super.userType,
    required super.isActivate,
    required super.dayPrice,
    required super.hourPrice,
    required super.info,
    required super.experience,
    required super.interests,
    required super.toursType,
    required super.albums,
    super.countryId,
    super.fcmToken,
    required super.createdAt,
    required super.updatedAt,
  });

  factory GuideModel.fromJson(Map<String, dynamic> json) {
    return GuideModel(
      id: json['id'] ?? 0,
      name: json['name'] ?? '',
      email: json['email'] ?? '',
      img: json['img'],
      mobile: json['mobile'] ?? '',
      userType: json['user_type'] ?? 0,
      isActivate: json['is_activate'] ?? 0,
      dayPrice: json['day_price'] ?? 0,
      hourPrice: json['hour_price'] ?? 0,
      info: json['info'] ?? '',
      experience: json['experience'] ?? '',
      interests: List<String>.from(json['interests'] ?? []),
      toursType: List<String>.from(json['tours_type'] ?? []),
      albums: List<String>.from(json['albums'] ?? []),
      countryId: json['country_id'],
      fcmToken: json['fcm_token'],
      createdAt: json['created_at'] ?? '',
      updatedAt: json['updated_at'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'email': email,
      'img': img,
      'mobile': mobile,
      'user_type': userType,
      'is_activate': isActivate,
      'day_price': dayPrice,
      'hour_price': hourPrice,
      'info': info,
      'experience': experience,
      'interests': interests,
      'tours_type': toursType,
      'albums': albums,
      'country_id': countryId,
      'fcm_token': fcmToken,
      'created_at': createdAt,
      'updated_at': updatedAt,
    };
  }

  GuideEntity toEntity() {
    return GuideEntity(
      id: id,
      name: name,
      email: email,
      img: img,
      mobile: mobile,
      userType: userType,
      isActivate: isActivate,
      dayPrice: dayPrice,
      hourPrice: hourPrice,
      info: info,
      experience: experience,
      interests: interests,
      toursType: toursType,
      albums: albums,
      countryId: countryId,
      fcmToken: fcmToken,
      createdAt: createdAt,
      updatedAt: updatedAt,
    );
  }
}
