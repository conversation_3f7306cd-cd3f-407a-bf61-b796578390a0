// core/widgets/country_picker_dialog.dart
import 'package:flutter/material.dart';
import '../models/country_model.dart';

class CountryPickerDialog extends StatefulWidget {
  final Country? selectedCountry;
  final Function(Country) onCountrySelected;
  final String? searchHint;
  final String? title;

  const CountryPickerDialog({
    super.key,
    this.selectedCountry,
    required this.onCountrySelected,
    this.searchHint,
    this.title,
  });

  @override
  State<CountryPickerDialog> createState() => _CountryPickerDialogState();
}

class _CountryPickerDialogState extends State<CountryPickerDialog> {
  final TextEditingController _searchController = TextEditingController();
  List<Country> _filteredCountries = CountryData.countries;

  @override
  void initState() {
    super.initState();
    _searchController.addListener(_filterCountries);
  }

  void _filterCountries() {
    setState(() {
      _filteredCountries = CountryData.searchCountries(_searchController.text);
    });
  }

  @override
  Widget build(BuildContext context) {
    final isRTL = Directionality.of(context) == TextDirection.rtl;

    return Dialog(
      backgroundColor: Colors.white,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Container(
        width: MediaQuery.of(context).size.width * 0.9,
        height: MediaQuery.of(context).size.height * 0.7,
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            // Title
            Text(
              widget.title ?? (isRTL ? 'اختر الدولة' : 'Select Country'),
              style: const TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),

            // Search Field
            TextField(
              controller: _searchController,
              textAlign: isRTL ? TextAlign.right : TextAlign.left,
              decoration: InputDecoration(
                hintText:
                    widget.searchHint ??
                    (isRTL ? 'ابحث عن دولة...' : 'Search country...'),
                prefixIcon: const Icon(Icons.search),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
                contentPadding: const EdgeInsets.symmetric(
                  horizontal: 16,
                  vertical: 12,
                ),
              ),
            ),
            const SizedBox(height: 16),

            // Countries List
            Expanded(
              child: ListView.builder(
                itemCount: _filteredCountries.length,
                itemBuilder: (context, index) {
                  final country = _filteredCountries[index];
                  final isSelected =
                      widget.selectedCountry?.code == country.code;

                  return ListTile(
                    leading: Text(
                      country.flag,
                      style: const TextStyle(fontSize: 24),
                    ),
                    title: Text(
                      isRTL ? country.nameAr : country.name,
                      style: TextStyle(
                        fontWeight: isSelected
                            ? FontWeight.bold
                            : FontWeight.normal,
                      ),
                    ),
                    subtitle: Text(
                      country.dialCode,
                      style: TextStyle(
                        color: Colors.grey[600],
                        fontWeight: isSelected
                            ? FontWeight.bold
                            : FontWeight.normal,
                      ),
                    ),
                    trailing: isSelected
                        ? const Icon(Icons.check, color: Colors.green)
                        : null,
                    onTap: () {
                      widget.onCountrySelected(country);
                      Navigator.of(context).pop(country);
                    },
                    selected: isSelected,
                    selectedTileColor: Colors.blue.withValues(alpha: 0.1),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  );
                },
              ),
            ),

            // Close Button
            const SizedBox(height: 16),
            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: () => Navigator.of(context).pop(),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.grey[300],
                  foregroundColor: Colors.black,
                  padding: const EdgeInsets.symmetric(vertical: 12),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
                child: Text(isRTL ? 'إلغاء' : 'Cancel'),
              ),
            ),
          ],
        ),
      ),
    );
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }
}

// Helper function to show country picker
Future<Country?> showCountryPicker({
  required BuildContext context,
  Country? selectedCountry,
  String? searchHint,
  String? title,
}) {
  return showDialog<Country>(
    context: context,
    builder: (context) => CountryPickerDialog(
      selectedCountry: selectedCountry,
      onCountrySelected: (country) {
        // Don't call Navigator.pop here, it's handled in the dialog
      },
      searchHint: searchHint,
      title: title,
    ),
  );
}
