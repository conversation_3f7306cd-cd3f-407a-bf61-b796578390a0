// features/home/<USER>/widgets/featured_guides_section_widget.dart
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:tripooo_user/core/theme/theme.dart';
import 'package:tripooo_user/core/utils/responsive_extensions.dart';
import 'package:tripooo_user/core/utils/widget_extensions.dart';
import 'package:tripooo_user/features/onboarding/utils/responsive_helper.dart';
import 'package:tripooo_user/core/di/injection_container.dart' as di;
import '../../../guides/presentation/cubit/featured_guides_cubit.dart';
import '../../../guides/presentation/cubit/guides_state.dart';
import '../../../guides/domain/entities/guide_entity.dart';
import '../../../guides/presentation/pages/featured_guide_details_page.dart';

class FeaturedGuidesSectionWidget extends StatelessWidget {
  const FeaturedGuidesSectionWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) =>
          di.sl<FeaturedGuidesCubit>()..getFeaturedGuides(limit: 4),
      child:
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildSectionHeader(context),
              ResponsiveHelper.getSpacing(
                context,
                type: 'medium',
              ).verticalSpaceResponsive(context),
              _buildGuidesList(context),
            ],
          ).paddingSymmetric(
            horizontal: ResponsiveHelper.isTablet(context) ? 32 : 20,
            context: context,
          ),
    );
  }

  Widget _buildSectionHeader(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          'المرشدون المميزون',
          style: AppTextStyles.heading.copyWith(
            fontSize: ResponsiveHelper.isTablet(context) ? 20 : 18,
            fontWeight: FontWeight.bold,
          ),
        ),
        TextButton(
          onPressed: () {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text('سيتم إضافة صفحة جميع المرشدين قريباً'),
                backgroundColor: AppColors.info,
              ),
            );
          },
          child: Text(
            'عرض المزيد',
            style: TextStyle(
              color: AppColors.primary,
              fontSize: ResponsiveHelper.isTablet(context) ? 16 : 14,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildGuidesList(BuildContext context) {
    return BlocBuilder<FeaturedGuidesCubit, FeaturedGuidesState>(
      builder: (context, state) {
        if (state is FeaturedGuidesLoading) {
          return _buildLoadingState(context);
        } else if (state is FeaturedGuidesLoaded) {
          return _buildLoadedState(context, state.guides);
        } else if (state is FeaturedGuidesError) {
          return _buildErrorState(context, state.message);
        }
        return _buildEmptyState(context);
      },
    );
  }

  Widget _buildLoadingState(BuildContext context) {
    return SizedBox(
      height: 180.h(context),
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        itemCount: 3,
        itemBuilder: (context, index) => _buildShimmerCard(context),
      ),
    );
  }

  Widget _buildLoadedState(BuildContext context, List<GuideEntity> guides) {
    if (guides.isEmpty) {
      return _buildEmptyState(context);
    }

    return SizedBox(
      height: 180.h(context),
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        itemCount: guides.length,
        itemBuilder: (context, index) {
          final guide = guides[index];
          return _buildGuideCard(context, guide);
        },
      ),
    );
  }

  Widget _buildErrorState(BuildContext context, String message) {
    return SizedBox(
      height: 180.h(context),
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.error_outline, size: 48, color: AppColors.error),
            8.verticalSpace,
            Text(
              'حدث خطأ في تحميل المرشدين',
              style: AppTextStyles.bodyMedium.copyWith(color: AppColors.error),
            ),
            4.verticalSpace,
            Text(
              message,
              style: AppTextStyles.bodySmall.copyWith(
                color: AppColors.textSecondary,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildEmptyState(BuildContext context) {
    return SizedBox(
      height: 180.h(context),
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.person_off, size: 48, color: AppColors.textTertiary),
            8.verticalSpace,
            Text(
              'لا يوجد مرشدين متاحين حالياً',
              style: AppTextStyles.bodyMedium.copyWith(
                color: AppColors.textTertiary,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildShimmerCard(BuildContext context) {
    return Container(
      width: 120.w(context),
      margin: EdgeInsets.only(right: 16.w(context)),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12),
        color: Colors.grey.withOpacity(0.1),
      ),
      child: Column(
        children: [
          Expanded(
            flex: 3,
            child: Container(
              decoration: BoxDecoration(
                borderRadius: BorderRadius.vertical(top: Radius.circular(12)),
                color: Colors.grey.withOpacity(0.2),
              ),
            ),
          ),
          Expanded(
            flex: 2,
            child: Container(
              padding: EdgeInsets.all(8),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Container(
                    height: 12,
                    width: double.infinity,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(6),
                      color: Colors.grey.withOpacity(0.2),
                    ),
                  ),
                  4.verticalSpace,
                  Container(
                    height: 10,
                    width: 60,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(5),
                      color: Colors.grey.withOpacity(0.2),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildGuideCard(BuildContext context, GuideEntity guide) {
    final isTablet = ResponsiveHelper.isTablet(context);

    return Container(
      width: isTablet ? 140.w(context) : 120.w(context),
      margin: EdgeInsets.only(right: 16.w(context)),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 8,
            offset: Offset(0, 2),
          ),
        ],
      ),
      child: Material(
        child: InkWell(
          onTap: () {
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => FeaturedGuideDetailsPage(
                  guideId: guide.id,
                  guideName: guide.displayName,
                ),
              ),
            );
          },
          borderRadius: BorderRadius.circular(12),
          child: Column(
            children: [
              Expanded(
                flex: 3,
                child: Container(
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.vertical(
                      top: Radius.circular(12),
                    ),
                  ),
                  child: ClipRRect(
                    borderRadius: BorderRadius.vertical(
                      top: Radius.circular(12),
                    ),
                    child: guide.hasImage
                        ? Image.network(
                            guide.imageUrl,
                            fit: BoxFit.cover,
                            width: double.infinity,
                            errorBuilder: (context, error, stackTrace) =>
                                _buildPlaceholderAvatar(),
                          )
                        : _buildPlaceholderAvatar(),
                  ),
                ),
              ),
              Expanded(
                flex: 2,
                child: Padding(
                  padding: EdgeInsets.all(8),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Text(
                        guide.displayName,
                        style: AppTextStyles.bodyMedium.copyWith(
                          fontWeight: FontWeight.bold,
                          fontSize: 12,
                        ),
                        textAlign: TextAlign.center,
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                      4.verticalSpace,
                      Text(
                        guide.formattedDayPrice,
                        style: AppTextStyles.bodySmall.copyWith(
                          color: AppColors.primary,
                          fontSize: 10,
                          fontWeight: FontWeight.bold,
                        ),
                        textAlign: TextAlign.center,
                      ),
                      4.verticalSpace,
                      Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: List.generate(5, (index) {
                          return Icon(
                            index < guide.rating.floor()
                                ? Icons.star
                                : Icons.star_border,
                            color: Colors.amber,
                            size: 12,
                          );
                        }),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildPlaceholderAvatar() {
    return Container(
      width: double.infinity,
      height: double.infinity,
      color: AppColors.primary.withOpacity(0.1),
      child: Icon(
        Icons.person,
        size: 30,
        color: AppColors.primary.withOpacity(0.5),
      ),
    );
  }
}
