// features/Auth/domain/usecases/reset_password.dart
import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';
import '../../../../core/error/failures.dart';
import '../../../../core/usecases/usecase.dart';
import '../repositories/auth_repository.dart';

class ResetPassword implements UseCase<String, ResetPasswordParams> {
  final AuthRepository repository;

  const ResetPassword(this.repository);

  @override
  Future<Either<Failure, String>> call(ResetPasswordParams params) async {
    return await repository.resetPassword(
      mobile: params.mobile,
      code: params.code,
      password: params.password,
      passwordConfirmation: params.passwordConfirmation,
    );
  }
}

class ResetPasswordParams extends Equatable {
  final String mobile;
  final String code;
  final String password;
  final String passwordConfirmation;

  const ResetPasswordParams({
    required this.mobile,
    required this.code,
    required this.password,
    required this.passwordConfirmation,
  });

  @override
  List<Object> get props => [mobile, code, password, passwordConfirmation];
}
