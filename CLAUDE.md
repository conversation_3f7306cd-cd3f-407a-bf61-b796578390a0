# Tripooo User - Claude Development Guide

## Project Overview
This is a Flutter travel application connecting users with tour guides and destinations. The app follows Clean Architecture principles with BLoC state management.

## Architecture & Structure

### Clean Architecture Layers
```
lib/
├── core/              # Shared utilities, themes, widgets
├── features/          # Feature-based modules
│   └── [feature]/
│       ├── data/      # Data sources, models, repositories impl
│       ├── domain/    # Entities, repositories, use cases
│       └── presentation/ # UI, cubits/blocs, widgets
```

### Feature Structure Example
```
features/Auth/
├── data/
│   ├── datasources/   # Remote & local data sources
│   ├── models/        # Data models with fromJson/toJson
│   ├── repositories/  # Repository implementations
│   └── services/      # Specific services (auth, social)
├── domain/
│   ├── entities/      # Pure Dart business objects
│   ├── repositories/  # Abstract repository interfaces
│   └── usecases/      # Single responsibility business logic
└── presentation/
    ├── cubit/         # BLoC state management
    ├── pages/         # Screen widgets
    └── widgets/       # Feature-specific widgets
```

## Coding Standards

### File Headers
Always start files with path comments:
```dart
// features/Auth/domain/entities/user.dart
// core/widgets/custom_button.dart
```

### Import Organization
```dart
// Flutter/Dart imports
import 'dart:developer';
import 'package:flutter/material.dart';

// External packages
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:equatable/equatable.dart';

// Internal imports (relative paths)
import '../../domain/entities/user.dart';
import '../../../core/theme/theme.dart';
```

### Entity Classes
- Extend `Equatable` for value comparison
- Use `const` constructors
- Include `copyWith` method for immutability
- Add computed properties when needed
```dart
class User extends Equatable {
  final int id;
  final String name;
  
  const User({required this.id, required this.name});
  
  bool get isGuide => userType == 2;
  
  User copyWith({int? id, String? name}) {
    return User(
      id: id ?? this.id,
      name: name ?? this.name,
    );
  }
  
  @override
  List<Object?> get props => [id, name];
}
```

### BLoC/Cubit Pattern
- Use constructor injection for dependencies
- Prefix private fields with underscore
- Group related use cases logically
```dart
class AuthCubit extends Cubit<AuthState> {
  final LocalAuthService _localAuthService;
  final Login _login;
  final Register _register;
  
  AuthCubit({
    required LocalAuthService localAuthService,
    required Login login,
    required Register register,
  }) : _localAuthService = localAuthService,
       _login = login,
       _register = register,
       super(AuthInitial());
}
```

### Widgets
- Use enums for widget variants
- Include comprehensive styling options
- Support different sizes and states
```dart
enum ButtonType { elevated, outlined, text }
enum ButtonSize { small, medium, large }

class CustomButton extends StatelessWidget {
  final String? text;
  final Widget? child;
  final VoidCallback? onPressed;
  final ButtonType type;
  final ButtonSize size;
  final bool isLoading;
  final bool isFullWidth;
  
  const CustomButton({
    Key? key,
    this.text,
    this.child,
    this.onPressed,
    this.type = ButtonType.elevated,
    this.size = ButtonSize.medium,
    this.isLoading = false,
    this.isFullWidth = false,
  }) : super(key: key);
}
```

### Colors & Theming
- Define colors in `AppColors` class as static constants
- Group colors by purpose (primary, text, state, etc.)
- Include both light and dark variants
```dart
class AppColors {
  static const Color primary = Color(0xFF3D56B2);
  static const Color textPrimary = Color(0xFF000000);
  static const Color error = Color(0xFFFF3B30);
  static const Color facebook = Color(0xFF1877F2);
}
```

### Dependency Injection
- Use GetIt service locator
- Register dependencies by type (singletons, factories)
- Group registrations by feature
```dart
final sl = GetIt.instance;

void init() {
  // External dependencies
  sl.registerLazySingleton<Dio>(() => DioHelper.dio);
  
  // Feature - Auth
  sl.registerLazySingleton<AuthRepository>(() => AuthRepositoryImpl(
    remoteDataSource: sl(),
    localDataSource: sl(),
  ));
}
```

## Development Guidelines

### Adding New Features
1. Create feature directory structure: `data/`, `domain/`, `presentation/`
2. Define entities in domain layer first
3. Create repository interface in domain
4. Implement use cases for business logic
5. Create data models and repository implementation
6. Build presentation layer with cubit and UI
7. Register dependencies in injection container

### API Integration
- Use Dio for HTTP requests
- Create data sources for API endpoints
- Map API responses to domain entities via data models
- Handle errors consistently across the app

### State Management
- Use BLoC pattern with Cubits for simpler state
- Create separate states for loading, success, error
- Emit states based on use case results
- Handle errors gracefully in UI

### Localization
- Store translations in `assets/translations/`
- Support: English (en), Arabic (ar), French (fr), Hindi (hi), Turkish (tr)
- Use EasyLocalization package
- Test all supported languages

### Testing Commands
Since this is a Flutter project, use these commands:
- `flutter analyze` - Static analysis
- `flutter test` - Run unit tests
- `flutter build apk --debug` - Build debug APK

### Key Dependencies
- `flutter_bloc` - State management
- `get_it` - Dependency injection  
- `equatable` - Value equality
- `dartz` - Functional programming (Either type)
- `dio` - HTTP client
- `easy_localization` - Internationalization
- `go_router` - Navigation
- `shared_preferences` & `flutter_secure_storage` - Local storage

### File Naming
- Use snake_case for file names
- Feature folders use PascalCase (e.g., `Auth/`, `guides/`)
- Keep file names descriptive and specific

### Error Handling
- Use Either<Failure, Success> pattern from dartz
- Create specific failure types in core/error/
- Handle network, cache, and server failures consistently
- Provide user-friendly error messages

Remember to maintain consistency with the existing codebase patterns and follow Flutter best practices for performance and maintainability.