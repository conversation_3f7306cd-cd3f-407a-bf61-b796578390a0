name: tripooo_user
description: "A new Flutter project."
publish_to: 'none'
version: 0.1.0

environment:
  sdk: ^3.8.1

dependencies:
  flutter:
    sdk: flutter
  equatable: ^2.0.7
  flutter_bloc: ^9.1.1
  get_it: ^8.0.3
  easy_localization: ^3.0.3
  intl: ^0.20.2
  dartz: ^0.10.1
  shared_preferences: ^2.5.3
  flutter_secure_storage: ^9.2.4
  local_auth: ^2.1.6
  dio: ^5.4.0
  # Social Authentication
  google_sign_in: ^6.2.1
  flutter_facebook_auth: ^7.0.1
  crypto: ^3.0.3
  go_router: ^16.0.0
  google_fonts: ^6.2.1
  # Image handling
  cached_network_image: ^3.3.1
  flutter_svg: ^2.2.0
  clarity_flutter: ^1.2.0
  firebase_core: ^4.0.0
  firebase_messaging: ^16.0.0

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^5.0.0
  mocktail: ^1.0.4
  flutter_launcher_icons: ^0.13.1
  flutter_native_splash: ^2.4.0

flutter:
  uses-material-design: true
  assets:
    - assets/translations/
    - assets/images/
    - assets/icons/
flutter_assets:
  assets_path: assets/
  output_path: lib/constants/
  filename: app_images.dart

# App Icon Configuration
flutter_icons:
  android: "launcher_icon"
  ios: true
  image_path: "assets/images/logo.png"
  min_sdk_android: 21
  web:
    generate: true
    image_path: "assets/images/logo.png"
    background_color: "#2196F3"
    theme_color: "#2196F3"
  windows:
    generate: true
    image_path: "assets/images/logo.png"
    icon_size: 48

# Splash Screen Configuration
flutter_native_splash:
  color: "#FFFFFF"
  image: "assets/images/logo.png"
  color_dark: "#1A1A1A"
  image_dark: "assets/images/logo.png"

  android_12:
    image: "assets/images/logo.png"
    icon_background_color: "#FFFFFF"
    image_dark: "assets/images/logo.png"
    icon_background_color_dark: "#1A1A1A"

  web: false
  android: true
  ios: true
