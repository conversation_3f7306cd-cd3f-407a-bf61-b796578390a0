// core/widgets/custom_tab_bar.dart
import 'package:flutter/material.dart';
import 'package:tripooo_user/core/theme/theme.dart';

class CustomTabBar extends StatelessWidget {
  final List<CustomTab> tabs;
  final int selectedIndex;
  final Function(int)? onTabSelected;
  final Color? backgroundColor;
  final Color? selectedBackgroundColor;
  final Color? unselectedBackgroundColor;
  final Color? selectedTextColor;
  final Color? unselectedTextColor;
  final TextStyle? selectedTextStyle;
  final TextStyle? unselectedTextStyle;
  final BorderRadius? borderRadius;
  final EdgeInsetsGeometry? padding;
  final EdgeInsetsGeometry? tabPadding;
  final double? height;
  final bool isScrollable;
  final MainAxisAlignment mainAxisAlignment;

  const CustomTabBar({
    super.key,
    required this.tabs,
    required this.selectedIndex,
    this.onTabSelected,
    this.backgroundColor,
    this.selectedBackgroundColor,
    this.unselectedBackgroundColor,
    this.selectedTextColor,
    this.unselectedTextColor,
    this.selectedTextStyle,
    this.unselectedTextStyle,
    this.borderRadius,
    this.padding,
    this.tabPadding,
    this.height,
    this.isScrollable = false,
    this.mainAxisAlignment = MainAxisAlignment.spaceEvenly,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    Widget tabBar = Container(
      height: height,
      padding: padding ?? EdgeInsets.all(4),
      decoration: BoxDecoration(
        color: backgroundColor ?? Colors.grey[100],
        borderRadius: borderRadius ?? BorderRadius.circular(7),
      ),
      child: isScrollable
          ? SingleChildScrollView(
              scrollDirection: Axis.horizontal,
              child: Row(children: _buildTabs(theme)),
            )
          : Row(
              mainAxisAlignment: mainAxisAlignment,
              children: _buildTabs(theme),
            ),
    );

    return tabBar;
  }

  List<Widget> _buildTabs(ThemeData theme) {
    return tabs.asMap().entries.map((entry) {
      final index = entry.key;
      final tab = entry.value;
      final isSelected = index == selectedIndex;

      return Expanded(
        flex: isScrollable ? 0 : 1,
        child: GestureDetector(
          onTap: () => onTabSelected?.call(index),
          child: Container(
            padding:
                tabPadding ??
                const EdgeInsets.symmetric(vertical: 12, horizontal: 14),
            decoration: BoxDecoration(
              color: isSelected
                  ? (selectedBackgroundColor ?? Colors.white)
                  : (unselectedBackgroundColor ?? Colors.transparent),
              borderRadius: borderRadius ?? BorderRadius.circular(7),
            ),
            child: _buildTabContent(tab, isSelected, theme),
          ),
        ),
      );
    }).toList();
  }

  Widget _buildTabContent(CustomTab tab, bool isSelected, ThemeData theme) {
    final textColor = isSelected
        ? (selectedTextColor ?? Colors.white)
        : (unselectedTextColor ?? Colors.grey[600]);

    final textStyle = isSelected
        ? (selectedTextStyle ??
              AppTextStyles.bodySmall.copyWith(
                color: Colors.black,
                fontWeight: FontWeight.w600,
              ))
        : (unselectedTextStyle ??
              AppTextStyles.bodySmall.copyWith(
                fontWeight: FontWeight.w600,
                color: Colors.grey[600],
              ));

    if (tab.child != null) {
      return tab.child!;
    }

    return Row(
      mainAxisSize: MainAxisSize.min,
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        if (tab.icon != null) ...[
          Icon(tab.icon, color: textColor, size: 20),
          if (tab.text != null) const SizedBox(width: 8),
        ],
        if (tab.text != null)
          Text(tab.text!, style: textStyle, textAlign: TextAlign.center),
      ],
    );
  }
}

class CustomTab {
  final String? text;
  final IconData? icon;
  final Widget? child;
  final VoidCallback? onTap;

  const CustomTab({this.text, this.icon, this.child, this.onTap})
    : assert(
        text != null || icon != null || child != null,
        'Either text, icon, or child must be provided',
      );
}
