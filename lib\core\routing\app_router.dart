// core/routing/app_router.dart
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import '../middleware/auth_guard.dart';
import '../../features/Auth/presentation/pages/onboarding.dart';
import '../../features/Auth/presentation/pages/language_selection_screen.dart';
import '../../features/Auth/presentation/pages/welcome_screen.dart';
import '../../features/Auth/presentation/pages/login_screen.dart';
import '../../features/Auth/presentation/pages/register_screen.dart';
import '../../features/Auth/presentation/pages/mobile_verification_screen.dart';
import '../../features/Auth/presentation/pages/forgot_password_screen.dart';
import '../../features/Auth/presentation/pages/change_password_screen.dart';
import '../../features/Auth/presentation/pages/profile_screen.dart';
import '../../features/home/<USER>/pages/home_screen.dart';
import '../../features/more/presentation/pages/more_screen.dart';
import '../../features/settings/presentation/pages/language_settings_screen.dart';
import '../../features/wallet/presentation/pages/wallet_home_screen.dart';
import '../../features/wallet/presentation/pages/add_balance_screen.dart';
import '../../features/wallet/presentation/pages/payment_method_screen.dart';
import '../../features/wallet/presentation/pages/topup_success_screen.dart';

class AppRoutes {
  static const String onboarding = '/onboarding';
  static const String languageSelection = '/language-selection';
  static const String welcome = '/welcome';
  static const String login = '/login';
  static const String register = '/register';
  static const String mobileVerification = '/mobile-verification';
  static const String forgotPassword = '/forgot-password';
  static const String changePassword = '/change-password';
  static const String profile = '/profile';
  static const String home = '/home';
  static const String more = '/more';
  static const String languageSettings = '/language-settings';
  static const String imageExample = '/image-example';
  // Wallet
  static const String walletHome = '/wallet';
  static const String walletAddBalance = '/wallet/add-balance';
  static const String walletPaymentMethod = '/wallet/payment-method';
  static const String walletTopupSuccess = '/wallet/topup-success';
}

class AppRouter {
  static final GoRouter _router = GoRouter(
    initialLocation: AppRoutes.home,
    redirect: AuthGuard.redirect,
    routes: [
      GoRoute(
        path: AppRoutes.onboarding,
        name: 'onboarding',
        builder: (context, state) => const OnboardingScreen(),
      ),
      GoRoute(
        path: AppRoutes.languageSelection,
        name: 'language-selection',
        builder: (context, state) => const LanguageSelectionScreen(),
      ),
      GoRoute(
        path: AppRoutes.welcome,
        name: 'welcome',
        builder: (context, state) => const WelcomeScreen(),
      ),
      GoRoute(
        path: AppRoutes.login,
        name: 'login',
        builder: (context, state) => const LoginScreen(),
      ),
      GoRoute(
        path: AppRoutes.register,
        name: 'register',
        builder: (context, state) => const RegisterScreen(),
      ),
      GoRoute(
        path: AppRoutes.mobileVerification,
        name: 'mobile-verification',
        builder: (context, state) {
          final mobile = state.extra is String ? state.extra as String : '';
          return MobileVerificationScreen(mobile: mobile);
        },
      ),
      GoRoute(
        path: AppRoutes.forgotPassword,
        name: 'forgot-password',
        builder: (context, state) => const ForgotPasswordScreen(),
      ),
      GoRoute(
        path: AppRoutes.changePassword,
        name: 'change-password',
        builder: (context, state) => const ChangePasswordScreen(),
      ),
      GoRoute(
        path: AppRoutes.profile,
        name: 'profile',
        builder: (context, state) => const ProfileScreen(),
      ),
      GoRoute(
        path: AppRoutes.home,
        name: 'home',
        builder: (context, state) => const HomeScreen(),
      ),
      GoRoute(
        path: AppRoutes.more,
        name: 'more',
        builder: (context, state) => const MoreScreen(),
      ),
      GoRoute(
        path: AppRoutes.languageSettings,
        name: 'language-settings',
        builder: (context, state) => const LanguageSettingsScreen(),
      ),
    ],
    errorBuilder: (context, state) => Scaffold(
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(Icons.error_outline, size: 64, color: Colors.red),
            const SizedBox(height: 16),
            Text(
              'Page not found: ${state.uri}',
              style: const TextStyle(fontSize: 18),
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: () => context.go(AppRoutes.home),
              child: const Text('Go to Home'),
            ),
          ],
        ),
      ),
    ),
  );

  static GoRouter get router => _router;
}

// Extension for easier navigation
extension AppRouterExtension on BuildContext {
  void goToOnboarding() => go(AppRoutes.onboarding);
  void goToLanguageSelection() => go(AppRoutes.languageSelection);
  void goToWelcome() => go(AppRoutes.welcome);
  void goToLogin() => go(AppRoutes.login);
  void goToRegister() => go(AppRoutes.register);
  void goToMobileVerification(String mobile) =>
      go(AppRoutes.mobileVerification, extra: mobile);
  void goToForgotPassword() => go(AppRoutes.forgotPassword);
  void goToChangePassword() => go(AppRoutes.changePassword);
  void goToProfile() => go(AppRoutes.profile);
  void goToHome() => go(AppRoutes.home);
  void goToMore() => go(AppRoutes.more);
  void goToLanguageSettings() => push(AppRoutes.languageSettings);
  void goBack() => pop();
}
