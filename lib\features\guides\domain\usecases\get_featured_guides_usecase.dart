// features/guides/domain/usecases/get_featured_guides_usecase.dart
import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';
import 'package:tripooo_user/core/error/failures.dart';
import 'package:tripooo_user/core/usecases/usecase.dart';
import 'package:tripooo_user/features/guides/domain/entities/guide_entity.dart';
import 'package:tripooo_user/features/guides/domain/repositories/guides_repository.dart';

class GetFeaturedGuidesUseCase implements UseCase<List<GuideEntity>, GetFeaturedGuidesParams> {
  final GuidesRepository repository;

  GetFeaturedGuidesUseCase(this.repository);

  @override
  Future<Either<Failure, List<GuideEntity>>> call(GetFeaturedGuidesParams params) async {
    return await repository.getFeaturedGuides(limit: params.limit);
  }
}

class GetFeaturedGuidesParams extends Equatable {
  final int limit;

  const GetFeaturedGuidesParams({
    this.limit = 4,
  });

  @override
  List<Object> get props => [limit];
}
