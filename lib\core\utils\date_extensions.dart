// utils/date_extensions.dart
import 'package:intl/intl.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';

extension DateTimeExtensions on DateTime {
  String formatDate([Locale? locale]) =>
      DateFormat('dd/MM/yyyy', locale?.languageCode).format(this);

  String formatTime([Locale? locale]) =>
      DateFormat('HH:mm', locale?.languageCode).format(this);

  String formatDateTime([Locale? locale]) =>
      DateFormat('dd/MM/yyyy HH:mm', locale?.languageCode).format(this);

  bool get isToday {
    final now = DateTime.now();
    return year == now.year && month == now.month && day == now.day;
  }

  bool get isYesterday {
    final yesterday = DateTime.now().subtract(const Duration(days: 1));
    return year == yesterday.year &&
        month == yesterday.month &&
        day == yesterday.day;
  }

  String timeAgo([Locale? locale]) {
    final now = DateTime.now();
    final difference = now.difference(this);

    if (difference.inDays > 0) {
      return '${difference.inDays} ${'days_ago'.tr()}';
    }
    if (difference.inHours > 0) {
      return '${difference.inHours} ${'hours_ago'.tr()}';
    }
    if (difference.inMinutes > 0) {
      return '${difference.inMinutes} ${'minutes_ago'.tr()}';
    }
    return 'just_now'.tr();
  }
}
