// features/destinations/presentation/pages/destination_details_page.dart
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:tripooo_user/core/theme/theme.dart';
import 'package:tripooo_user/core/utils/responsive_extensions.dart';
import 'package:tripooo_user/core/utils/widget_extensions.dart';
import 'package:tripooo_user/features/onboarding/utils/responsive_helper.dart';
import 'package:tripooo_user/core/di/injection_container.dart' as di;
import '../cubit/destinations_cubit.dart';
import '../../domain/entities/destination_entity.dart';

class DestinationDetailsPage extends StatelessWidget {
  final int destinationId;
  final String? destinationName;

  const DestinationDetailsPage({
    super.key,
    required this.destinationId,
    this.destinationName,
  });

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) =>
          di.sl<DestinationsCubit>()..getDestinationDetails(destinationId),
      child: Scaffold(
        backgroundColor: AppColors.background,
        body: BlocBuilder<DestinationsCubit, DestinationsState>(
          builder: (context, state) {
            if (state is DestinationsLoading) {
              return _buildLoadingState(context);
            } else if (state is DestinationDetailsLoaded) {
              return _buildDetailsContent(context, state.destination);
            } else if (state is DestinationsError) {
              return _buildErrorState(context, state.message);
            }
            return _buildInitialState(context);
          },
        ),
      ),
    );
  }

  Widget _buildLoadingState(BuildContext context) {
    return CustomScrollView(
      slivers: [
        _buildAppBar(context, destinationName ?? 'تفاصيل الوجهة'),
        SliverFillRemaining(
          child: Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                CircularProgressIndicator(color: AppColors.primary),
                16.verticalSpace,
                Text(
                  'جاري تحميل تفاصيل الوجهة...',
                  style: AppTextStyles.bodyMedium.copyWith(
                    color: AppColors.textSecondary,
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildErrorState(BuildContext context, String message) {
    return CustomScrollView(
      slivers: [
        _buildAppBar(context, destinationName ?? 'تفاصيل الوجهة'),
        SliverFillRemaining(
          child: Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(Icons.error_outline, size: 64, color: AppColors.error),
                16.verticalSpace,
                Text(
                  'حدث خطأ في تحميل التفاصيل',
                  style: AppTextStyles.heading.copyWith(color: AppColors.error),
                ),
                8.verticalSpace,
                Text(
                  message,
                  style: AppTextStyles.bodyMedium.copyWith(
                    color: AppColors.textSecondary,
                  ),
                  textAlign: TextAlign.center,
                ),
                24.verticalSpace,
                ElevatedButton(
                  onPressed: () {
                    context.read<DestinationsCubit>().getDestinationDetails(
                      destinationId,
                    );
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppColors.primary,
                    foregroundColor: AppColors.white,
                  ),
                  child: Text('إعادة المحاولة'),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildInitialState(BuildContext context) {
    return CustomScrollView(
      slivers: [
        _buildAppBar(context, destinationName ?? 'تفاصيل الوجهة'),
        SliverFillRemaining(
          child: Center(
            child: Text(
              'لا توجد بيانات متاحة',
              style: AppTextStyles.bodyMedium.copyWith(
                color: AppColors.textSecondary,
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildDetailsContent(
    BuildContext context,
    DestinationEntity destination,
  ) {
    return CustomScrollView(
      slivers: [
        _buildAppBar(context, destination.displayName),
        _buildImageSection(context, destination),
        _buildContentSection(context, destination),
      ],
    );
  }

  Widget _buildAppBar(BuildContext context, String title) {
    return SliverAppBar(
      expandedHeight: 0,
      floating: true,
      pinned: true,
      backgroundColor: AppColors.primary,
      foregroundColor: AppColors.white,
      title: Text(
        title,
        style: AppTextStyles.heading.copyWith(
          color: AppColors.white,
          fontSize: ResponsiveHelper.isTablet(context) ? 20 : 18,
        ),
      ),
      leading: IconButton(
        icon: Icon(Icons.arrow_back),
        onPressed: () => Navigator.of(context).pop(),
      ),
      actions: [
        IconButton(
          icon: Icon(Icons.favorite_border),
          onPressed: () {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text('تم إضافة الوجهة للمفضلة'),
                backgroundColor: AppColors.success,
              ),
            );
          },
        ),
        IconButton(
          icon: Icon(Icons.share),
          onPressed: () {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text('تم مشاركة الوجهة'),
                backgroundColor: AppColors.info,
              ),
            );
          },
        ),
      ],
    );
  }

  Widget _buildImageSection(
    BuildContext context,
    DestinationEntity destination,
  ) {
    return SliverToBoxAdapter(
      child: SizedBox(
        height: 250.h(context),
        child: Stack(
          children: [
            // Main Image
            SizedBox(
              width: double.infinity,
              height: double.infinity,
              child: destination.hasImage
                  ? Image.network(
                      destination.imageUrl,
                      fit: BoxFit.cover,
                      errorBuilder: (context, error, stackTrace) =>
                          _buildPlaceholderImage(),
                    )
                  : _buildPlaceholderImage(),
            ),

            // Gradient Overlay
            Container(
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [Colors.transparent, Colors.black.withOpacity(0.3)],
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                ),
              ),
            ),

            // Price Badge
            Positioned(
              top: 16,
              right: 16,
              child: Container(
                padding: EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                decoration: BoxDecoration(
                  color: AppColors.success,
                  borderRadius: BorderRadius.circular(
                    AppDimensions.radiusSmall,
                  ),
                ),
                child: Text(
                  destination.formattedPrice,
                  style: AppTextStyles.bodyMedium.copyWith(
                    color: AppColors.white,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ),

            // Status Badge
            Positioned(
              top: 16,
              left: 16,
              child: Container(
                padding: EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                decoration: BoxDecoration(
                  color: destination.isActive
                      ? AppColors.success
                      : AppColors.error,
                  borderRadius: BorderRadius.circular(
                    AppDimensions.radiusSmall,
                  ),
                ),
                child: Text(
                  destination.isActive ? 'متاح' : 'غير متاح',
                  style: AppTextStyles.bodySmall.copyWith(
                    color: AppColors.white,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildContentSection(
    BuildContext context,
    DestinationEntity destination,
  ) {
    return SliverToBoxAdapter(
      child:
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Basic Info
              _buildBasicInfo(context, destination),

              // Description
              _buildDescription(context, destination),

              // Location
              _buildLocation(context, destination),

              // Action Buttons
              _buildActionButtons(context, destination),

              // Bottom Spacing
              ResponsiveHelper.getSpacing(
                context,
                type: 'xlarge',
              ).verticalSpaceResponsive(context),
            ],
          ).paddingSymmetric(
            horizontal: ResponsiveHelper.isTablet(context) ? 32 : 20,
            vertical: ResponsiveHelper.isTablet(context) ? 24 : 16,
            context: context,
          ),
    );
  }

  Widget _buildPlaceholderImage() {
    return Container(
      width: double.infinity,
      height: double.infinity,
      color: AppColors.primary.withOpacity(0.1),
      child: Icon(
        Icons.location_on,
        size: 64,
        color: AppColors.primary.withOpacity(0.5),
      ),
    );
  }

  Widget _buildBasicInfo(BuildContext context, DestinationEntity destination) {
    return Container(
      padding: EdgeInsets.all(20),
      margin: EdgeInsets.only(bottom: 16),
      decoration: BoxDecoration(
        color: AppColors.white,
        borderRadius: BorderRadius.circular(AppDimensions.radiusMedium),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 8,
            offset: Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            destination.displayName,
            style: AppTextStyles.heading.copyWith(
              fontSize: ResponsiveHelper.isTablet(context) ? 24 : 20,
              fontWeight: FontWeight.bold,
            ),
          ),
          8.verticalSpace,
          Row(
            children: [
              Icon(Icons.location_on, size: 20, color: AppColors.primary),
              8.horizontalSpace,
              Expanded(
                child: Text(
                  destination.displayLocation,
                  style: AppTextStyles.bodyMedium.copyWith(
                    color: AppColors.textSecondary,
                  ),
                ),
              ),
            ],
          ),
          12.verticalSpace,
          Row(
            children: [
              Icon(Icons.attach_money, size: 20, color: AppColors.success),
              8.horizontalSpace,
              Text(
                destination.formattedPrice,
                style: AppTextStyles.bodyLarge.copyWith(
                  color: AppColors.success,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildDescription(
    BuildContext context,
    DestinationEntity destination,
  ) {
    return Container(
      padding: EdgeInsets.all(20),
      margin: EdgeInsets.only(bottom: 16),
      decoration: BoxDecoration(
        color: AppColors.white,
        borderRadius: BorderRadius.circular(AppDimensions.radiusMedium),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 8,
            offset: Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'وصف الوجهة',
            style: AppTextStyles.heading.copyWith(
              fontSize: ResponsiveHelper.isTablet(context) ? 18 : 16,
              fontWeight: FontWeight.bold,
            ),
          ),
          12.verticalSpace,
          Text(
            destination.displayInfo,
            style: AppTextStyles.bodyMedium.copyWith(height: 1.6),
          ),
        ],
      ),
    );
  }

  Widget _buildLocation(BuildContext context, DestinationEntity destination) {
    return Container(
      padding: EdgeInsets.all(20),
      margin: EdgeInsets.only(bottom: 16),
      decoration: BoxDecoration(
        color: AppColors.white,
        borderRadius: BorderRadius.circular(AppDimensions.radiusMedium),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 8,
            offset: Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'الموقع',
            style: AppTextStyles.heading.copyWith(
              fontSize: ResponsiveHelper.isTablet(context) ? 18 : 16,
              fontWeight: FontWeight.bold,
            ),
          ),
          12.verticalSpace,
          Row(
            children: [
              Icon(Icons.map, size: 24, color: AppColors.primary),
              12.horizontalSpace,
              Expanded(
                child: Text(
                  destination.displayLocation,
                  style: AppTextStyles.bodyMedium,
                ),
              ),
            ],
          ),
          16.verticalSpace,
          SizedBox(
            width: double.infinity,
            child: ElevatedButton.icon(
              onPressed: () {
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text('سيتم فتح الخريطة قريباً'),
                    backgroundColor: AppColors.info,
                  ),
                );
              },
              icon: Icon(Icons.map),
              label: Text('عرض على الخريطة'),
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.primary,
                foregroundColor: AppColors.white,
                padding: EdgeInsets.symmetric(vertical: 12),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildActionButtons(
    BuildContext context,
    DestinationEntity destination,
  ) {
    return Container(
      padding: EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: AppColors.white,
        borderRadius: BorderRadius.circular(AppDimensions.radiusMedium),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 8,
            offset: Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          SizedBox(
            width: double.infinity,
            child: ElevatedButton(
              onPressed: destination.isActive
                  ? () {
                      ScaffoldMessenger.of(context).showSnackBar(
                        SnackBar(
                          content: Text('سيتم إضافة نظام الحجز قريباً'),
                          backgroundColor: AppColors.success,
                        ),
                      );
                    }
                  : null,
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.success,
                foregroundColor: AppColors.white,
                padding: EdgeInsets.symmetric(vertical: 16),
                disabledBackgroundColor: Colors.grey,
              ),
              child: Text(
                destination.isActive ? 'احجز الآن' : 'غير متاح للحجز',
                style: AppTextStyles.bodyLarge.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ),
          12.verticalSpace,
          Row(
            children: [
              Expanded(
                child: OutlinedButton.icon(
                  onPressed: () {
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                        content: Text('تم الاتصال بخدمة العملاء'),
                        backgroundColor: AppColors.info,
                      ),
                    );
                  },
                  icon: Icon(Icons.phone),
                  label: Text('اتصل بنا'),
                  style: OutlinedButton.styleFrom(
                    foregroundColor: AppColors.primary,
                    side: BorderSide(color: AppColors.primary),
                    padding: EdgeInsets.symmetric(vertical: 12),
                  ),
                ),
              ),
              12.horizontalSpace,
              Expanded(
                child: OutlinedButton.icon(
                  onPressed: () {
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                        content: Text('تم مشاركة الوجهة'),
                        backgroundColor: AppColors.info,
                      ),
                    );
                  },
                  icon: Icon(Icons.share),
                  label: Text('مشاركة'),
                  style: OutlinedButton.styleFrom(
                    foregroundColor: AppColors.primary,
                    side: BorderSide(color: AppColors.primary),
                    padding: EdgeInsets.symmetric(vertical: 12),
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
