// features/guides/presentation/cubit/guide_details_cubit.dart
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:equatable/equatable.dart';
import '../../domain/entities/guide_details_entity.dart';
import '../../domain/usecases/get_guide_details.dart';

part 'guide_details_state.dart';

class GuideDetailsCubit extends Cubit<GuideDetailsState> {
  final GetGuideDetails getGuideDetailsUseCase;

  GuideDetailsCubit({required this.getGuideDetailsUseCase})
    : super(GuideDetailsInitial());

  Future<void> loadGuideDetails(int guideId) async {
    emit(GuideDetailsLoading());

    final result = await getGuideDetailsUseCase(
      GetGuideDetailsParams(guideId: guideId),
    );

    result.fold(
      (failure) => emit(
        GuideDetailsError(
          message: failure.message,
          statusCode: failure.statusCode,
        ),
      ),
      (guideDetails) => emit(GuideDetailsLoaded(guideDetails: guideDetails)),
    );
  }

  Future<void> refreshGuideDetails(int guideId) async {
    await loadGuideDetails(guideId);
  }

  void resetState() {
    emit(GuideDetailsInitial());
  }
}
