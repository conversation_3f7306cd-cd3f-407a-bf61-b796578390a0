// core/utils/constants.dart

class AppConstants {
  // Storage keys
  static const String tokenKey = 'auth_token';
  static const String userKey = 'user_data';
  static const String isLoggedInKey = 'is_logged_in';
  static const String rememberMeKey = 'remember_me';
  static const String fcmTokenKey = 'fcm_token';
  
  // Error messages
  static const String networkErrorMessage = 'Please check your internet connection';
  static const String serverErrorMessage = 'Something went wrong. Please try again';
  static const String unauthorizedErrorMessage = 'Session expired. Please login again';
  static const String validationErrorMessage = 'Please check your input';
  static const String notFoundErrorMessage = 'Resource not found';
  static const String unknownErrorMessage = 'An unexpected error occurred';
  
  // Success messages
  static const String loginSuccessMessage = 'Login successful';
  static const String registerSuccessMessage = 'Registration successful';
  static const String logoutSuccessMessage = 'Logout successful';
  static const String passwordChangedMessage = 'Password changed successfully';
  static const String profileUpdatedMessage = 'Profile updated successfully';
  static const String codeVerifiedMessage = 'Code verified successfully';
  static const String resetCodeSentMessage = 'Reset code sent successfully';
  static const String passwordResetMessage = 'Password reset successfully';
  
  // Validation patterns
  static const String phonePattern = r'^\+[1-9]\d{1,14}$';
  static const String emailPattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$';
  
  // User types
  static const int userTypeClient = 1;
  static const int userTypeGuide = 2;
  
  // Language IDs
  static const int languageEnglish = 8;
  static const int languageArabic = 9;
}
