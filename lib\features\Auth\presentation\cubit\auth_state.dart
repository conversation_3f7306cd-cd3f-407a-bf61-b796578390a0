// features/Auth/presentation/cubit/auth_state.dart
part of 'auth_cubit.dart';

abstract class AuthState extends Equatable {
  const AuthState();

  @override
  List<Object> get props => [];
}

class AuthInitial extends AuthState {}

class AuthLoading extends AuthState {}

// Biometric states
class AuthBiometricAvailable extends AuthState {
  final List<BiometricType> availableBiometrics;

  const AuthBiometricAvailable(this.availableBiometrics);

  @override
  List<Object> get props => [availableBiometrics];
}

class AuthBiometricUnavailable extends AuthState {}

class AuthBiometricSuccess extends AuthState {}

// Authentication states
class AuthLoginSuccess extends AuthState {
  final User user;

  const AuthLoginSuccess(this.user);

  @override
  List<Object> get props => [user];
}

class AuthRegisterSuccess extends AuthState {}

class AuthLogoutSuccess extends AuthState {}

class AuthNavigateToHome extends AuthState {}

class AuthUserLoaded extends AuthState {
  final User user;

  const AuthUserLoaded(this.user);

  @override
  List<Object> get props => [user];
}

class AuthTokenValid extends AuthState {
  final User user;

  const AuthTokenValid(this.user);

  @override
  List<Object> get props => [user];
}

class AuthMobileVerified extends AuthState {
  final User user;

  const AuthMobileVerified(this.user);

  @override
  List<Object> get props => [user];
}

class AuthCodeSent extends AuthState {
  final String message;

  const AuthCodeSent(this.message);

  @override
  List<Object> get props => [message];
}

class AuthUserUpdated extends AuthState {
  final User user;

  const AuthUserUpdated(this.user);

  @override
  List<Object> get props => [user];
}

class AuthPasswordChanged extends AuthState {
  final String message;

  const AuthPasswordChanged(this.message);

  @override
  List<Object> get props => [message];
}

class AuthMobileChanged extends AuthState {
  final String message;

  const AuthMobileChanged(this.message);

  @override
  List<Object> get props => [message];
}

class AuthResetCodeSent extends AuthState {
  final String message;

  const AuthResetCodeSent(this.message);

  @override
  List<Object> get props => [message];
}

class AuthResetCodeVerified extends AuthState {
  final String message;

  const AuthResetCodeVerified(this.message);

  @override
  List<Object> get props => [message];
}

class AuthPasswordReset extends AuthState {
  final String message;

  const AuthPasswordReset(this.message);

  @override
  List<Object> get props => [message];
}

// Social Authentication states
class AuthSocialLoginSuccess extends AuthState {
  final User user;
  final SocialProvider provider;

  const AuthSocialLoginSuccess(this.user, this.provider);

  @override
  List<Object> get props => [user, provider];
}

class AuthError extends AuthState {
  final String message;

  const AuthError(this.message);

  @override
  List<Object> get props => [message];
}
