// core/models/country_model.dart
class Country {
  final String name;
  final String nameAr;
  final String code;
  final String dialCode;
  final String flag;

  const Country({
    required this.name,
    required this.nameAr,
    required this.code,
    required this.dialCode,
    required this.flag,
  });

  @override
  String toString() => '$flag $dialCode';
}

class PhoneValidationRule {
  final String countryCode;
  final String hintText;
  final int minLength;
  final int maxLength;
  final String pattern;
  final String errorMessage;
  final String errorMessageAr;

  const PhoneValidationRule({
    required this.countryCode,
    required this.hintText,
    required this.minLength,
    required this.maxLength,
    required this.pattern,
    required this.errorMessage,
    required this.errorMessageAr,
  });
}

class CountryData {
  static const Map<String, PhoneValidationRule> phoneRules = {
    'SA': PhoneValidationRule(
      countryCode: 'SA',
      hintText: '5xxxxxxxx',
      minLength: 9,
      maxLength: 9,
      pattern: r'^[0-9]{8}$',
      errorMessage:
          'Please enter a valid Saudi phone number (9 digits starting with 5)',
      errorMessageAr: 'يرجى إدخال رقم هاتف سعودي صحيح (9 أرقام تبدأ بـ 5)',
    ),
    'AE': PhoneValidationRule(
      countryCode: 'AE',
      hintText: '50xxxxxxx',
      minLength: 9,
      maxLength: 9,
      pattern: r'^5[0-9]{8}$',
      errorMessage:
          'Please enter a valid UAE phone number (9 digits starting with 5)',
      errorMessageAr: 'يرجى إدخال رقم هاتف إماراتي صحيح (9 أرقام تبدأ بـ 5)',
    ),
    'EG': PhoneValidationRule(
      countryCode: 'EG',
      hintText: '10xxxxxxxx',
      minLength: 10,
      maxLength: 11,
      pattern: r'^1[0-9]{9,10}$',
      errorMessage:
          'Please enter a valid Egyptian phone number (10-11 digits starting with 1)',
      errorMessageAr: 'يرجى إدخال رقم هاتف مصري صحيح (10-11 رقم تبدأ بـ 1)',
    ),
    'KW': PhoneValidationRule(
      countryCode: 'KW',
      hintText: '9xxxxxxx',
      minLength: 8,
      maxLength: 8,
      pattern: r'^[569][0-9]{7}$',
      errorMessage:
          'Please enter a valid Kuwaiti phone number (8 digits starting with 5, 6, or 9)',
      errorMessageAr:
          'يرجى إدخال رقم هاتف كويتي صحيح (8 أرقام تبدأ بـ 5 أو 6 أو 9)',
    ),
    'QA': PhoneValidationRule(
      countryCode: 'QA',
      hintText: '3xxxxxxx',
      minLength: 8,
      maxLength: 8,
      pattern: r'^[3567][0-9]{7}$',
      errorMessage:
          'Please enter a valid Qatari phone number (8 digits starting with 3, 5, 6, or 7)',
      errorMessageAr:
          'يرجى إدخال رقم هاتف قطري صحيح (8 أرقام تبدأ بـ 3 أو 5 أو 6 أو 7)',
    ),
    'US': PhoneValidationRule(
      countryCode: 'US',
      hintText: '(*************',
      minLength: 10,
      maxLength: 10,
      pattern: r'^[2-9][0-9]{9}$',
      errorMessage: 'Please enter a valid US phone number (10 digits)',
      errorMessageAr: 'يرجى إدخال رقم هاتف أمريكي صحيح (10 أرقام)',
    ),
    'GB': PhoneValidationRule(
      countryCode: 'GB',
      hintText: '7xxxxxxxxx',
      minLength: 10,
      maxLength: 11,
      pattern: r'^[67][0-9]{9,10}$',
      errorMessage:
          'Please enter a valid UK phone number (10-11 digits starting with 6 or 7)',
      errorMessageAr:
          'يرجى إدخال رقم هاتف بريطاني صحيح (10-11 رقم تبدأ بـ 6 أو 7)',
    ),
  };

  static const List<Country> countries = [
    // Arab Countries
    Country(
      name: 'Saudi Arabia',
      nameAr: 'السعودية',
      code: 'SA',
      dialCode: '+966',
      flag: '🇸🇦',
    ),
    Country(
      name: 'United Arab Emirates',
      nameAr: 'الإمارات',
      code: 'AE',
      dialCode: '+971',
      flag: '🇦🇪',
    ),
    Country(
      name: 'Egypt',
      nameAr: 'مصر',
      code: 'EG',
      dialCode: '+20',
      flag: '🇪🇬',
    ),
    Country(
      name: 'Kuwait',
      nameAr: 'الكويت',
      code: 'KW',
      dialCode: '+965',
      flag: '🇰🇼',
    ),
    Country(
      name: 'Qatar',
      nameAr: 'قطر',
      code: 'QA',
      dialCode: '+974',
      flag: '🇶🇦',
    ),
    Country(
      name: 'Bahrain',
      nameAr: 'البحرين',
      code: 'BH',
      dialCode: '+973',
      flag: '🇧🇭',
    ),
    Country(
      name: 'Oman',
      nameAr: 'عمان',
      code: 'OM',
      dialCode: '+968',
      flag: '🇴🇲',
    ),
    Country(
      name: 'Jordan',
      nameAr: 'الأردن',
      code: 'JO',
      dialCode: '+962',
      flag: '🇯🇴',
    ),
    Country(
      name: 'Lebanon',
      nameAr: 'لبنان',
      code: 'LB',
      dialCode: '+961',
      flag: '🇱🇧',
    ),
    Country(
      name: 'Syria',
      nameAr: 'سوريا',
      code: 'SY',
      dialCode: '+963',
      flag: '🇸🇾',
    ),
    Country(
      name: 'Iraq',
      nameAr: 'العراق',
      code: 'IQ',
      dialCode: '+964',
      flag: '🇮🇶',
    ),
    Country(
      name: 'Palestine',
      nameAr: 'فلسطين',
      code: 'PS',
      dialCode: '+970',
      flag: '🇵🇸',
    ),
    Country(
      name: 'Morocco',
      nameAr: 'المغرب',
      code: 'MA',
      dialCode: '+212',
      flag: '🇲🇦',
    ),
    Country(
      name: 'Algeria',
      nameAr: 'الجزائر',
      code: 'DZ',
      dialCode: '+213',
      flag: '🇩🇿',
    ),
    Country(
      name: 'Tunisia',
      nameAr: 'تونس',
      code: 'TN',
      dialCode: '+216',
      flag: '🇹🇳',
    ),
    Country(
      name: 'Libya',
      nameAr: 'ليبيا',
      code: 'LY',
      dialCode: '+218',
      flag: '🇱🇾',
    ),
    Country(
      name: 'Sudan',
      nameAr: 'السودان',
      code: 'SD',
      dialCode: '+249',
      flag: '🇸🇩',
    ),
    Country(
      name: 'Yemen',
      nameAr: 'اليمن',
      code: 'YE',
      dialCode: '+967',
      flag: '🇾🇪',
    ),

    // Popular International Countries
    Country(
      name: 'United States',
      nameAr: 'الولايات المتحدة',
      code: 'US',
      dialCode: '+1',
      flag: '🇺🇸',
    ),
    Country(
      name: 'United Kingdom',
      nameAr: 'المملكة المتحدة',
      code: 'GB',
      dialCode: '+44',
      flag: '🇬🇧',
    ),
    Country(
      name: 'Germany',
      nameAr: 'ألمانيا',
      code: 'DE',
      dialCode: '+49',
      flag: '🇩🇪',
    ),
    Country(
      name: 'France',
      nameAr: 'فرنسا',
      code: 'FR',
      dialCode: '+33',
      flag: '🇫🇷',
    ),
    Country(
      name: 'Italy',
      nameAr: 'إيطاليا',
      code: 'IT',
      dialCode: '+39',
      flag: '🇮🇹',
    ),
    Country(
      name: 'Spain',
      nameAr: 'إسبانيا',
      code: 'ES',
      dialCode: '+34',
      flag: '🇪🇸',
    ),
    Country(
      name: 'Canada',
      nameAr: 'كندا',
      code: 'CA',
      dialCode: '+1',
      flag: '🇨🇦',
    ),
    Country(
      name: 'Australia',
      nameAr: 'أستراليا',
      code: 'AU',
      dialCode: '+61',
      flag: '🇦🇺',
    ),
    Country(
      name: 'Japan',
      nameAr: 'اليابان',
      code: 'JP',
      dialCode: '+81',
      flag: '🇯🇵',
    ),
    Country(
      name: 'South Korea',
      nameAr: 'كوريا الجنوبية',
      code: 'KR',
      dialCode: '+82',
      flag: '🇰🇷',
    ),
    Country(
      name: 'China',
      nameAr: 'الصين',
      code: 'CN',
      dialCode: '+86',
      flag: '🇨🇳',
    ),
    Country(
      name: 'India',
      nameAr: 'الهند',
      code: 'IN',
      dialCode: '+91',
      flag: '🇮🇳',
    ),
    Country(
      name: 'Pakistan',
      nameAr: 'باكستان',
      code: 'PK',
      dialCode: '+92',
      flag: '🇵🇰',
    ),
    Country(
      name: 'Bangladesh',
      nameAr: 'بنغلاديش',
      code: 'BD',
      dialCode: '+880',
      flag: '🇧🇩',
    ),
    Country(
      name: 'Turkey',
      nameAr: 'تركيا',
      code: 'TR',
      dialCode: '+90',
      flag: '🇹🇷',
    ),
    Country(
      name: 'Iran',
      nameAr: 'إيران',
      code: 'IR',
      dialCode: '+98',
      flag: '🇮🇷',
    ),
    Country(
      name: 'Afghanistan',
      nameAr: 'أفغانستان',
      code: 'AF',
      dialCode: '+93',
      flag: '🇦🇫',
    ),
    Country(
      name: 'Russia',
      nameAr: 'روسيا',
      code: 'RU',
      dialCode: '+7',
      flag: '🇷🇺',
    ),
    Country(
      name: 'Brazil',
      nameAr: 'البرازيل',
      code: 'BR',
      dialCode: '+55',
      flag: '🇧🇷',
    ),
    Country(
      name: 'Mexico',
      nameAr: 'المكسيك',
      code: 'MX',
      dialCode: '+52',
      flag: '🇲🇽',
    ),
    Country(
      name: 'Argentina',
      nameAr: 'الأرجنتين',
      code: 'AR',
      dialCode: '+54',
      flag: '🇦🇷',
    ),
    Country(
      name: 'South Africa',
      nameAr: 'جنوب أفريقيا',
      code: 'ZA',
      dialCode: '+27',
      flag: '🇿🇦',
    ),
    Country(
      name: 'Nigeria',
      nameAr: 'نيجيريا',
      code: 'NG',
      dialCode: '+234',
      flag: '🇳🇬',
    ),
    Country(
      name: 'Kenya',
      nameAr: 'كينيا',
      code: 'KE',
      dialCode: '+254',
      flag: '🇰🇪',
    ),
    Country(
      name: 'Ethiopia',
      nameAr: 'إثيوبيا',
      code: 'ET',
      dialCode: '+251',
      flag: '🇪🇹',
    ),
    Country(
      name: 'Ghana',
      nameAr: 'غانا',
      code: 'GH',
      dialCode: '+233',
      flag: '🇬🇭',
    ),
    Country(
      name: 'Indonesia',
      nameAr: 'إندونيسيا',
      code: 'ID',
      dialCode: '+62',
      flag: '🇮🇩',
    ),
    Country(
      name: 'Malaysia',
      nameAr: 'ماليزيا',
      code: 'MY',
      dialCode: '+60',
      flag: '🇲🇾',
    ),
    Country(
      name: 'Thailand',
      nameAr: 'تايلاند',
      code: 'TH',
      dialCode: '+66',
      flag: '🇹🇭',
    ),
    Country(
      name: 'Philippines',
      nameAr: 'الفلبين',
      code: 'PH',
      dialCode: '+63',
      flag: '🇵🇭',
    ),
    Country(
      name: 'Vietnam',
      nameAr: 'فيتنام',
      code: 'VN',
      dialCode: '+84',
      flag: '🇻🇳',
    ),
    Country(
      name: 'Singapore',
      nameAr: 'سنغافورة',
      code: 'SG',
      dialCode: '+65',
      flag: '🇸🇬',
    ),
    Country(
      name: 'Netherlands',
      nameAr: 'هولندا',
      code: 'NL',
      dialCode: '+31',
      flag: '🇳🇱',
    ),
    Country(
      name: 'Belgium',
      nameAr: 'بلجيكا',
      code: 'BE',
      dialCode: '+32',
      flag: '🇧🇪',
    ),
    Country(
      name: 'Switzerland',
      nameAr: 'سويسرا',
      code: 'CH',
      dialCode: '+41',
      flag: '🇨🇭',
    ),
    Country(
      name: 'Austria',
      nameAr: 'النمسا',
      code: 'AT',
      dialCode: '+43',
      flag: '🇦🇹',
    ),
    Country(
      name: 'Sweden',
      nameAr: 'السويد',
      code: 'SE',
      dialCode: '+46',
      flag: '🇸🇪',
    ),
    Country(
      name: 'Norway',
      nameAr: 'النرويج',
      code: 'NO',
      dialCode: '+47',
      flag: '🇳🇴',
    ),
    Country(
      name: 'Denmark',
      nameAr: 'الدنمارك',
      code: 'DK',
      dialCode: '+45',
      flag: '🇩🇰',
    ),
    Country(
      name: 'Finland',
      nameAr: 'فنلندا',
      code: 'FI',
      dialCode: '+358',
      flag: '🇫🇮',
    ),
    Country(
      name: 'Poland',
      nameAr: 'بولندا',
      code: 'PL',
      dialCode: '+48',
      flag: '🇵🇱',
    ),
    Country(
      name: 'Czech Republic',
      nameAr: 'التشيك',
      code: 'CZ',
      dialCode: '+420',
      flag: '🇨🇿',
    ),
    Country(
      name: 'Hungary',
      nameAr: 'المجر',
      code: 'HU',
      dialCode: '+36',
      flag: '🇭🇺',
    ),
    Country(
      name: 'Romania',
      nameAr: 'رومانيا',
      code: 'RO',
      dialCode: '+40',
      flag: '🇷🇴',
    ),
    Country(
      name: 'Greece',
      nameAr: 'اليونان',
      code: 'GR',
      dialCode: '+30',
      flag: '🇬🇷',
    ),
    Country(
      name: 'Portugal',
      nameAr: 'البرتغال',
      code: 'PT',
      dialCode: '+351',
      flag: '🇵🇹',
    ),
    Country(
      name: 'Ireland',
      nameAr: 'أيرلندا',
      code: 'IE',
      dialCode: '+353',
      flag: '🇮🇪',
    ),
    Country(
      name: 'New Zealand',
      nameAr: 'نيوزيلندا',
      code: 'NZ',
      dialCode: '+64',
      flag: '🇳🇿',
    ),
  ];

  static Country getDefaultCountry() {
    return countries.first; // Saudi Arabia as default
  }

  static Country? findByDialCode(String dialCode) {
    try {
      return countries.firstWhere((country) => country.dialCode == dialCode);
    } catch (e) {
      return null;
    }
  }

  static Country? findByCode(String code) {
    try {
      return countries.firstWhere((country) => country.code == code);
    } catch (e) {
      return null;
    }
  }

  static List<Country> searchCountries(String query) {
    if (query.isEmpty) return countries;

    final lowerQuery = query.toLowerCase();
    return countries.where((country) {
      return country.name.toLowerCase().contains(lowerQuery) ||
          country.nameAr.contains(query) ||
          country.dialCode.contains(query) ||
          country.code.toLowerCase().contains(lowerQuery);
    }).toList();
  }

  static PhoneValidationRule? getPhoneRule(String countryCode) {
    return phoneRules[countryCode];
  }

  static PhoneValidationRule getDefaultPhoneRule() {
    return phoneRules['SA'] ??
        const PhoneValidationRule(
          countryCode: 'DEFAULT',
          hintText: 'xxxxxxxxx',
          minLength: 8,
          maxLength: 15,
          pattern: r'^[0-9]{8,15}$',
          errorMessage: 'Please enter a valid phone number',
          errorMessageAr: 'يرجى إدخال رقم هاتف صحيح',
        );
  }
}
