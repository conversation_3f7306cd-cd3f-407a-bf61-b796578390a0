# App Router Documentation

This document explains how to use the centralized routing system in the Tripooo User app.

## Overview

The app uses GoRouter for navigation with a centralized configuration in `app_router.dart`. This provides:
- Type-safe navigation
- Centralized route management
- Easy-to-use extension methods
- Route constants for maintainability

## File Structure

```
lib/core/routing/
├── app_router.dart     # Main router configuration
└── README.md          # This documentation
```

## Usage

### 1. Basic Navigation

Use the extension methods for easy navigation:

```dart
import 'package:tripooo_user/core/routing/app_router.dart';

// Navigate to different screens
context.goToLogin();
context.goToRegister();
context.goToHome();
context.goToOnboarding();

// Navigate with parameters
context.goToMobileVerification('**********');
```

### 2. Route Constants

Use the `AppRoutes` class for route constants:

```dart
// Instead of hardcoding strings
context.go('/login');

// Use constants
context.go(AppRoutes.login);
```

### 3. Available Routes

| Route | Path | Description |
|-------|------|-------------|
| `AppRoutes.onboarding` | `/onboarding` | App onboarding screens |
| `AppRoutes.login` | `/login` | User login screen |
| `AppRoutes.register` | `/register` | User registration screen |
| `AppRoutes.mobileVerification` | `/mobile-verification` | Mobile number verification |
| `AppRoutes.home` | `/home` | Main app home screen |

### 4. Extension Methods

| Method | Description |
|--------|-------------|
| `context.goToOnboarding()` | Navigate to onboarding |
| `context.goToLogin()` | Navigate to login screen |
| `context.goToRegister()` | Navigate to register screen |
| `context.goToMobileVerification(mobile)` | Navigate to verification with mobile number |
| `context.goToHome()` | Navigate to home screen |

## Adding New Routes

1. **Add route constant** in `AppRoutes` class:
```dart
class AppRoutes {
  // ... existing routes
  static const String newScreen = '/new-screen';
}
```

2. **Add route configuration** in `AppRouter._router`:
```dart
GoRoute(
  path: AppRoutes.newScreen,
  name: 'new-screen',
  builder: (context, state) => const NewScreen(),
),
```

3. **Add extension method** (optional):
```dart
extension AppRouterExtension on BuildContext {
  // ... existing methods
  void goToNewScreen() => go(AppRoutes.newScreen);
}
```

## Error Handling

The router includes a custom error page that:
- Shows a user-friendly error message
- Displays the attempted route
- Provides a button to return to onboarding

## Best Practices

1. **Always use route constants** instead of hardcoded strings
2. **Use extension methods** for common navigation patterns
3. **Pass parameters** using the `extra` parameter for complex data
4. **Keep route names** consistent with the path (kebab-case)
5. **Document new routes** in this README when adding them

## Example Implementation

```dart
// In a widget
class MyWidget extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return ElevatedButton(
      onPressed: () {
        // Good: Using extension method
        context.goToLogin();
        
        // Also good: Using route constant
        context.go(AppRoutes.login);
        
        // Avoid: Hardcoded string
        // context.go('/login');
      },
      child: Text('Go to Login'),
    );
  }
}
```

## Integration with Main App

The router is integrated in `main.dart`:

```dart
MaterialApp.router(
  routerConfig: AppRouter.router,
  // ... other configuration
)
```

This setup ensures all navigation throughout the app uses the centralized router configuration.
