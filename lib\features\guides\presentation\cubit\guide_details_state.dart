// features/guides/presentation/cubit/guide_details_state.dart
part of 'guide_details_cubit.dart';

abstract class GuideDetailsState extends Equatable {
  const GuideDetailsState();

  @override
  List<Object?> get props => [];
}

class GuideDetailsInitial extends GuideDetailsState {}

class GuideDetailsLoading extends GuideDetailsState {}

class GuideDetailsLoaded extends GuideDetailsState {
  final GuideDetailsEntity guideDetails;

  const GuideDetailsLoaded({required this.guideDetails});

  @override
  List<Object?> get props => [guideDetails];
}

class GuideDetailsError extends GuideDetailsState {
  final String message;
  final int statusCode;

  const GuideDetailsError({
    required this.message,
    required this.statusCode,
  });

  @override
  List<Object?> get props => [message, statusCode];
}
