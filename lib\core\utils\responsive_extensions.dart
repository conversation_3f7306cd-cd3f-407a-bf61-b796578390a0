// core/utils/responsive_extensions.dart
import 'package:flutter/material.dart';

extension ScreenSize on BuildContext {
  double get screenWidth => MediaQuery.sizeOf(this).width;
  double get screenHeight => MediaQuery.sizeOf(this).height;
}

extension ResponsiveNum on num {
  double w(BuildContext context) => this * context.screenWidth / 375;
  double h(BuildContext context) => this * context.screenHeight / 812;
}
