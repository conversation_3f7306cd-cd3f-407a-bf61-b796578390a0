# 🔐 Social Authentication Setup Guide

This guide will help you set up Google, Facebook, and Apple Sign-In for your Flutter app.

## 📋 Prerequisites

Make sure you have the following packages in your `pubspec.yaml`:

```yaml
dependencies:
  google_sign_in: ^6.2.1
  flutter_facebook_auth: ^7.0.1
  sign_in_with_apple: ^6.1.1
  crypto: ^3.0.3
```

## 🔧 Platform Setup

### 🟦 Google Sign-In Setup

#### Android Setup:
1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Create a new project or select existing one
3. Enable Google Sign-In API
4. Create OAuth 2.0 credentials:
   - Application type: Android
   - Package name: `com.example.tripooo_user` (your app's package name)
   - SHA-1 certificate fingerprint (get from: `keytool -list -v -keystore ~/.android/debug.keystore -alias androiddebugkey -storepass android -keypass android`)

5. Add to `android/app/build.gradle`:
```gradle
android {
    defaultConfig {
        // Add this line
        multiDexEnabled true
    }
}
```

#### iOS Setup:
1. In Google Cloud Console, create iOS OAuth 2.0 credentials
2. Download `GoogleService-Info.plist`
3. Add it to `ios/Runner/` in Xcode
4. Add to `ios/Runner/Info.plist`:
```xml
<key>CFBundleURLTypes</key>
<array>
    <dict>
        <key>CFBundleURLName</key>
        <string>REVERSED_CLIENT_ID</string>
        <key>CFBundleURLSchemes</key>
        <array>
            <string>YOUR_REVERSED_CLIENT_ID</string>
        </array>
    </dict>
</array>
```

### 🟦 Facebook Sign-In Setup

#### Create Facebook App:
1. Go to [Facebook Developers](https://developers.facebook.com/)
2. Create a new app
3. Add Facebook Login product
4. Get your App ID and App Secret

#### Android Setup:
1. Add to `android/app/src/main/res/values/strings.xml`:
```xml
<string name="facebook_app_id">YOUR_FACEBOOK_APP_ID</string>
<string name="fb_login_protocol_scheme">fbYOUR_FACEBOOK_APP_ID</string>
```

2. Add to `android/app/src/main/AndroidManifest.xml`:
```xml
<application>
    <meta-data 
        android:name="com.facebook.sdk.ApplicationId" 
        android:value="@string/facebook_app_id"/>
    
    <activity 
        android:name="com.facebook.FacebookActivity"
        android:configChanges="keyboard|keyboardHidden|screenLayout|screenSize|orientation"
        android:label="@string/app_name" />
    
    <activity
        android:name="com.facebook.CustomTabActivity"
        android:exported="true">
        <intent-filter>
            <action android:name="android.intent.action.VIEW" />
            <category android:name="android.intent.category.DEFAULT" />
            <category android:name="android.intent.category.BROWSABLE" />
            <data android:scheme="@string/fb_login_protocol_scheme" />
        </intent-filter>
    </activity>
</application>
```

#### iOS Setup:
1. Add to `ios/Runner/Info.plist`:
```xml
<key>CFBundleURLTypes</key>
<array>
    <dict>
        <key>CFBundleURLName</key>
        <string>facebook</string>
        <key>CFBundleURLSchemes</key>
        <array>
            <string>fbYOUR_FACEBOOK_APP_ID</string>
        </array>
    </dict>
</array>

<key>FacebookAppID</key>
<string>YOUR_FACEBOOK_APP_ID</string>
<key>FacebookDisplayName</key>
<string>Tripooo</string>
```

### 🟦 Apple Sign-In Setup (iOS Only)

#### Xcode Setup:
1. Open `ios/Runner.xcworkspace` in Xcode
2. Select your target
3. Go to "Signing & Capabilities"
4. Add "Sign in with Apple" capability

#### Apple Developer Setup:
1. Go to [Apple Developer Console](https://developer.apple.com/)
2. Create an App ID with Sign in with Apple capability
3. Create a Service ID for web authentication (if needed)

## 🚀 Usage Examples

### Basic Usage in Your App:

```dart
// Sign in with Google
context.read<AuthCubit>().signInWithGoogle(
  userType: 1, // 1 for client, 2 for guide
  fcmToken: 'your_fcm_token', // Optional
);

// Sign in with Facebook
context.read<AuthCubit>().signInWithFacebook(
  userType: 1,
  fcmToken: 'your_fcm_token',
);

// Sign in with Apple (iOS only)
context.read<AuthCubit>().signInWithApple(
  userType: 1,
  fcmToken: 'your_fcm_token',
);
```

### Using the Social Auth Buttons Widget:

```dart
SocialAuthButtons(
  userType: 1, // Client
  fcmToken: 'your_fcm_token', // Optional
)
```

### Handling Auth States:

```dart
BlocListener<AuthCubit, AuthState>(
  listener: (context, state) {
    if (state is AuthSocialLoginSuccess) {
      // Handle successful social login
      print('Signed in with ${state.provider.name}');
      print('User: ${state.user.name}');
      // Navigate to home screen
    } else if (state is AuthError) {
      // Handle error
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text(state.message)),
      );
    }
  },
  child: YourWidget(),
)
```

## 🔧 Backend API Integration

Your backend should handle the social authentication endpoint:

```
POST /api/auth/social-login
```

**Request Body:**
```json
{
  "provider": "google|facebook|apple",
  "access_token": "user_access_token",
  "id_token": "id_token_if_available",
  "email": "<EMAIL>",
  "name": "User Name",
  "profile_picture": "https://...",
  "fcm_token": "firebase_token",
  "user_type": 1
}
```

**Response:**
```json
{
  "status": 200,
  "msg": "success",
  "data": {
    "id": 1,
    "name": "User Name",
    "email": "<EMAIL>",
    "token": "jwt_token",
    // ... other user fields
  }
}
```

## 🎨 Customization

### Custom Social Auth Button:

```dart
SocialAuthButton(
  provider: SocialProvider.google,
  onPressed: () {
    // Custom logic
  },
  isLoading: false,
)
```

### Custom Styling:

You can customize the appearance by modifying the `SocialAuthButtons` widget or creating your own buttons using the social auth service directly.

## 🐛 Troubleshooting

### Common Issues:

1. **Google Sign-In not working on Android:**
   - Make sure SHA-1 fingerprint is correct
   - Check package name matches

2. **Facebook Login fails:**
   - Verify App ID is correct
   - Check if app is in development mode

3. **Apple Sign-In not available:**
   - Only works on iOS 13+ and macOS 10.15+
   - Make sure capability is added in Xcode

### Debug Tips:

```dart
// Enable debug logging
import 'package:flutter/foundation.dart';

if (kDebugMode) {
  print('Social auth debug info...');
}
```

## 📱 Testing

### Test Accounts:
- Create test accounts for each platform
- Use development/sandbox modes during testing
- Test on both Android and iOS devices

### Production Checklist:
- [ ] All OAuth credentials are production-ready
- [ ] App is published on respective stores
- [ ] Privacy policy includes social login data usage
- [ ] Terms of service updated

## 🔒 Security Considerations

1. **Token Validation:** Always validate tokens on your backend
2. **User Data:** Only request necessary permissions
3. **Privacy:** Inform users about data collection
4. **Logout:** Implement proper logout from all providers

## 📚 Additional Resources

- [Google Sign-In Documentation](https://developers.google.com/identity/sign-in/android)
- [Facebook Login Documentation](https://developers.facebook.com/docs/facebook-login/)
- [Apple Sign-In Documentation](https://developer.apple.com/sign-in-with-apple/)
