// features/Auth/presentation/pages/social_auth_example.dart
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../cubit/auth_cubit.dart';
import '../widgets/social_auth_buttons.dart';
import '../../data/models/social_auth_models.dart';

class SocialAuthExampleScreen extends StatelessWidget {
  const SocialAuthExampleScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        elevation: 0,
        title: Text(
          'Social Authentication',
          style: TextStyle(color: Colors.black),
        ),
        leading: IconButton(
          icon: Icon(Icons.arrow_back, color: Colors.black),
          onPressed: () => Navigator.pop(context),
        ),
      ),
      body: BlocListener<AuthCubit, AuthState>(
        listener: (context, state) {
          if (state is AuthSocialLoginSuccess) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(
                  'Successfully signed in with ${state.provider.name.toUpperCase()}!',
                ),
                backgroundColor: Colors.green,
              ),
            );
            // Navigate to home screen
            // Navigator.pushReplacementNamed(context, '/home');
          } else if (state is AuthError) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(state.message),
                backgroundColor: Colors.red,
              ),
            );
          }
        },
        child: SafeArea(
          child: Padding(
            padding: const EdgeInsets.all(24.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Title
                Text(
                  'Choose your preferred sign-in method',
                  style: TextStyle(
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                    color: Colors.black,
                  ),
                ),

                SizedBox(height: 8),

                // Subtitle
                Text(
                  'Sign in quickly and securely with your social accounts',
                  style: TextStyle(fontSize: 16, color: Colors.grey[600]),
                ),

                SizedBox(height: 32),

                // Social auth buttons
                SocialAuthButtons(
                  userType: 1, // Client
                  fcmToken: null, // Add FCM token if available
                ),

                SizedBox(height: 32),

                // Individual buttons with custom handling
                Text(
                  'Or use individual buttons:',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.w600,
                    color: Colors.black87,
                  ),
                ),

                SizedBox(height: 16),

                BlocBuilder<AuthCubit, AuthState>(
                  builder: (context, state) {
                    final isLoading = state is AuthLoading;

                    return Column(
                      children: [
                        // Google Sign In
                        SocialAuthButton(
                          provider: SocialProvider.google,
                          isLoading: isLoading,
                          onPressed: () {
                            context.read<AuthCubit>().signInWithGoogle(
                              userType: 1, // Client
                              fcmToken: null, // Add FCM token if available
                            );
                          },
                        ),

                        SizedBox(height: 12),

                        // Facebook Sign In
                        SocialAuthButton(
                          provider: SocialProvider.facebook,
                          isLoading: isLoading,
                          onPressed: () {
                            context.read<AuthCubit>().signInWithFacebook(
                              userType: 1, // Client
                              fcmToken: null, // Add FCM token if available
                            );
                          },
                        ),

                        SizedBox(height: 12),

                        // Apple Sign In (iOS only)
                        if (Theme.of(context).platform == TargetPlatform.iOS)
                          SocialAuthButton(
                            provider: SocialProvider.apple,
                            isLoading: isLoading,
                            onPressed: () {
                              context.read<AuthCubit>().signInWithApple(
                                userType: 1, // Client
                                fcmToken: null, // Add FCM token if available
                              );
                            },
                          ),
                      ],
                    );
                  },
                ),

                Spacer(),

                // Info text
                Container(
                  padding: EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: Colors.blue[50],
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(color: Colors.blue[200]!),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Icon(
                            Icons.info_outline,
                            color: Colors.blue[700],
                            size: 20,
                          ),
                          SizedBox(width: 8),
                          Text(
                            'How it works:',
                            style: TextStyle(
                              fontWeight: FontWeight.w600,
                              color: Colors.blue[700],
                            ),
                          ),
                        ],
                      ),
                      SizedBox(height: 8),
                      Text(
                        '• Choose your preferred social platform\n'
                        '• Sign in with your existing account\n'
                        '• Your account will be created automatically\n'
                        '• Start using the app immediately',
                        style: TextStyle(
                          fontSize: 14,
                          color: Colors.blue[700],
                          height: 1.4,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
