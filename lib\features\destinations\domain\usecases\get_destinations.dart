// features/destinations/domain/usecases/get_destinations.dart
import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';
import '../../../../core/error/failures.dart';
import '../../../../core/usecases/usecase.dart';
import '../entities/destination_entity.dart';
import '../repositories/destinations_repository.dart';

class GetDestinations implements UseCase<List<DestinationEntity>, GetDestinationsParams> {
  final DestinationsRepository repository;

  GetDestinations(this.repository);

  @override
  Future<Either<Failure, List<DestinationEntity>>> call(GetDestinationsParams params) async {
    return await repository.getDestinations(
      offset: params.offset,
      limit: params.limit,
      countryId: params.countryId,
      priceFrom: params.priceFrom,
      priceTo: params.priceTo,
    );
  }
}

class GetDestinationsParams extends Equatable {
  final int offset;
  final int limit;
  final int? countryId;
  final int? priceFrom;
  final int? priceTo;

  const GetDestinationsParams({
    this.offset = 0,
    this.limit = 10,
    this.countryId,
    this.priceFrom,
    this.priceTo,
  });

  @override
  List<Object?> get props => [offset, limit, countryId, priceFrom, priceTo];
}
