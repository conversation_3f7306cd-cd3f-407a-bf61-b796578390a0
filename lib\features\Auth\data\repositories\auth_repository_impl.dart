// features/Auth/data/repositories/auth_repository_impl.dart
import 'dart:io';
import 'package:dartz/dartz.dart';
import '../../../../core/error/exceptions.dart';
import '../../../../core/error/failures.dart';
import '../../../../core/utils/typedef.dart';
import '../../domain/entities/user.dart';
import '../../domain/repositories/auth_repository.dart';
import '../datasources/auth_remote_data_source.dart';
import '../datasources/auth_local_data_source.dart';
import '../models/auth_request_models.dart';
import '../models/social_auth_models.dart';
import '../models/user_model.dart';

class AuthRepositoryImpl implements AuthRepository {
  final AuthRemoteDataSource _remoteDataSource;
  final AuthLocalDataSource _localDataSource;

  const AuthRepositoryImpl({
    required AuthRemoteDataSource remoteDataSource,
    required AuthLocalDataSource localDataSource,
  }) : _remoteDataSource = remoteDataSource,
       _localDataSource = localDataSource;

  @override
  ResultFuture<User> getUser() async {
    try {
      final token = await _localDataSource.getToken();
      if (token == null) {
        return Left(UnauthorizedFailure('No authentication token found'));
      }

      final user = await _remoteDataSource.getUser(token);
      await _localDataSource.saveUser(user);
      return Right(user);
    } on ServerException catch (e) {
      return Left(ServerFailure(e.message));
    } on NetworkException catch (e) {
      return Left(NetworkFailure(e.message));
    } on UnauthorizedException catch (e) {
      await _localDataSource.clearAll();
      return Left(UnauthorizedFailure(e.message));
    } on CacheException catch (e) {
      return Left(CacheFailure(e.message));
    } catch (e) {
      return Left(UnknownFailure('Unexpected error: $e'));
    }
  }

  @override
  ResultFuture<User> checkToken() async {
    try {
      final token = await _localDataSource.getToken();
      if (token == null) {
        return Left(UnauthorizedFailure('No authentication token found'));
      }

      final user = await _remoteDataSource.checkToken(token);
      await _localDataSource.saveUser(user);
      return Right(user);
    } on ServerException catch (e) {
      return Left(ServerFailure(e.message));
    } on NetworkException catch (e) {
      return Left(NetworkFailure(e.message));
    } on UnauthorizedException catch (e) {
      await _localDataSource.clearAll();
      return Left(UnauthorizedFailure(e.message));
    } on CacheException catch (e) {
      return Left(CacheFailure(e.message));
    } catch (e) {
      return Left(UnknownFailure('Unexpected error: $e'));
    }
  }

  @override
  ResultVoid register({
    required String name,
    required String email,
    required String mobile,
    required String password,
    required String passwordConfirmation,
    required int userType,
    File? file,
    String? fcmToken,
    double? dayPrice,
    double? hourPrice,
    String? info,
    String? experience,
    List<String>? interests,
    List<String>? toursType,
    List<int>? languages,
    String? branchName,
    String? branchEmail,
    List<String>? branchPayments,
  }) async {
    try {
      final request = RegisterRequest(
        name: name,
        email: email,
        mobile: mobile,
        password: password,
        passwordConfirmation: passwordConfirmation,
        userType: userType,
        file: file,
        fcmToken: fcmToken,
        dayPrice: dayPrice,
        hourPrice: hourPrice,
        info: info,
        experience: experience,
        interests: interests,
        toursType: toursType,
        languages: languages,
        branchName: branchName,
        branchEmail: branchEmail,
        branchPayments: branchPayments,
      );

      await _remoteDataSource.register(request);
      return const Right(null);
    } on ServerException catch (e) {
      return Left(ServerFailure(e.message));
    } on NetworkException catch (e) {
      return Left(NetworkFailure(e.message));
    } on ValidationException catch (e) {
      return Left(ValidationFailure(e.message));
    } catch (e) {
      return Left(UnknownFailure('Unexpected error: $e'));
    }
  }

  @override
  ResultFuture<User> mobileCheck({
    required String code,
    required String mobile,
  }) async {
    try {
      final request = MobileCheckRequest(code: code, mobile: mobile);
      final response = await _remoteDataSource.mobileCheck(request);

      if (response.user != null && response.token != null) {
        await saveUserData(response.user!, response.token!);
        return Right(response.user!);
      } else {
        return Left(ServerFailure('Invalid response from server'));
      }
    } on ServerException catch (e) {
      return Left(ServerFailure(e.message));
    } on NetworkException catch (e) {
      return Left(NetworkFailure(e.message));
    } on ValidationException catch (e) {
      return Left(ValidationFailure(e.message));
    } catch (e) {
      return Left(UnknownFailure('Unexpected error: $e'));
    }
  }

  @override
  ResultFuture<String> regenerateCode({required String mobile}) async {
    try {
      final request = RegenerateCodeRequest(mobile: mobile);
      final response = await _remoteDataSource.regenerateCode(request);
      return Right(response.message);
    } on ServerException catch (e) {
      return Left(ServerFailure(e.message));
    } on NetworkException catch (e) {
      return Left(NetworkFailure(e.message));
    } catch (e) {
      return Left(UnknownFailure('Unexpected error: $e'));
    }
  }

  @override
  ResultFuture<User> login({
    required String mobile,
    required String password,
    String? fcmToken,
  }) async {
    try {
      final request = LoginRequest(
        mobile: mobile,
        password: password,
        fcmToken: fcmToken,
      );

      final response = await _remoteDataSource.login(request);

      if (response.user != null && response.token != null) {
        await saveUserData(response.user!, response.token!);
        return Right(response.user!);
      } else {
        return Left(ServerFailure('Invalid response from server'));
      }
    } on ServerException catch (e) {
      return Left(ServerFailure(e.message));
    } on NetworkException catch (e) {
      return Left(NetworkFailure(e.message));
    } on UnauthorizedException catch (e) {
      return Left(UnauthorizedFailure(e.message));
    } catch (e) {
      return Left(UnknownFailure('Unexpected error: $e'));
    }
  }

  @override
  ResultFuture<User> updateUser({
    String? name,
    String? email,
    File? file,
    double? dayPrice,
    double? hourPrice,
    String? info,
    String? experience,
    List<String>? interests,
    List<String>? toursType,
    List<int>? languages,
  }) async {
    try {
      final token = await _localDataSource.getToken();
      if (token == null) {
        return Left(UnauthorizedFailure('No authentication token found'));
      }

      final request = UpdateUserRequest(
        name: name,
        email: email,
        file: file,
        dayPrice: dayPrice,
        hourPrice: hourPrice,
        info: info,
        experience: experience,
        interests: interests,
        toursType: toursType,
        languages: languages,
      );

      final user = await _remoteDataSource.updateUser(request, token);
      await _localDataSource.saveUser(user);
      return Right(user);
    } on ServerException catch (e) {
      return Left(ServerFailure(e.message));
    } on NetworkException catch (e) {
      return Left(NetworkFailure(e.message));
    } on UnauthorizedException catch (e) {
      await _localDataSource.clearAll();
      return Left(UnauthorizedFailure(e.message));
    } on CacheException catch (e) {
      return Left(CacheFailure(e.message));
    } catch (e) {
      return Left(UnknownFailure('Unexpected error: $e'));
    }
  }

  @override
  ResultFuture<String> changePassword({
    required String newPassword,
    required String newPasswordConfirmation,
    required String oldPassword,
  }) async {
    try {
      final token = await _localDataSource.getToken();
      if (token == null) {
        return Left(UnauthorizedFailure('No authentication token found'));
      }

      final request = ChangePasswordRequest(
        newPassword: newPassword,
        newPasswordConfirmation: newPasswordConfirmation,
        oldPassword: oldPassword,
      );

      final response = await _remoteDataSource.changePassword(request, token);
      return Right(response.message);
    } on ServerException catch (e) {
      return Left(ServerFailure(e.message));
    } on NetworkException catch (e) {
      return Left(NetworkFailure(e.message));
    } on UnauthorizedException catch (e) {
      await _localDataSource.clearAll();
      return Left(UnauthorizedFailure(e.message));
    } catch (e) {
      return Left(UnknownFailure('Unexpected error: $e'));
    }
  }

  @override
  ResultFuture<String> changeMobileNumber({required String mobile}) async {
    try {
      final token = await _localDataSource.getToken();
      if (token == null) {
        return Left(UnauthorizedFailure('No authentication token found'));
      }

      final request = ChangeMobileRequest(mobile: mobile);
      final response = await _remoteDataSource.changeMobileNumber(
        request,
        token,
      );
      return Right(response.message);
    } on ServerException catch (e) {
      return Left(ServerFailure(e.message));
    } on NetworkException catch (e) {
      return Left(NetworkFailure(e.message));
    } on UnauthorizedException catch (e) {
      await _localDataSource.clearAll();
      return Left(UnauthorizedFailure(e.message));
    } catch (e) {
      return Left(UnknownFailure('Unexpected error: $e'));
    }
  }

  @override
  ResultVoid logout() async {
    try {
      final token = await _localDataSource.getToken();
      if (token != null) {
        await _remoteDataSource.logout(token);
      }
      await _localDataSource.clearAll();
      return const Right(null);
    } on ServerException catch (e) {
      // Even if server logout fails, clear local data
      await _localDataSource.clearAll();
      return Left(ServerFailure(e.message));
    } on NetworkException catch (e) {
      // Even if network fails, clear local data
      await _localDataSource.clearAll();
      return Left(NetworkFailure(e.message));
    } on CacheException catch (e) {
      return Left(CacheFailure(e.message));
    } catch (e) {
      await _localDataSource.clearAll();
      return Left(UnknownFailure('Unexpected error: $e'));
    }
  }

  @override
  ResultFuture<String> sendResetCode({required String mobile}) async {
    try {
      final request = SendResetCodeRequest(mobile: mobile);
      final response = await _remoteDataSource.sendResetCode(request);
      return Right(response.message);
    } on ServerException catch (e) {
      return Left(ServerFailure(e.message));
    } on NetworkException catch (e) {
      return Left(NetworkFailure(e.message));
    } catch (e) {
      return Left(UnknownFailure('Unexpected error: $e'));
    }
  }

  @override
  ResultFuture<String> verifyResetCode({
    required String mobile,
    required String code,
  }) async {
    try {
      final request = VerifyResetCodeRequest(mobile: mobile, code: code);
      final response = await _remoteDataSource.verifyResetCode(request);
      return Right(response.message);
    } on ServerException catch (e) {
      return Left(ServerFailure(e.message));
    } on NetworkException catch (e) {
      return Left(NetworkFailure(e.message));
    } catch (e) {
      return Left(UnknownFailure('Unexpected error: $e'));
    }
  }

  @override
  ResultFuture<String> resetPassword({
    required String mobile,
    required String code,
    required String password,
    required String passwordConfirmation,
  }) async {
    try {
      final request = ResetPasswordRequest(
        mobile: mobile,
        code: code,
        password: password,
        passwordConfirmation: passwordConfirmation,
      );

      final response = await _remoteDataSource.resetPassword(request);
      return Right(response.message);
    } on ServerException catch (e) {
      return Left(ServerFailure(e.message));
    } on NetworkException catch (e) {
      return Left(NetworkFailure(e.message));
    } catch (e) {
      return Left(UnknownFailure('Unexpected error: $e'));
    }
  }

  @override
  ResultFuture<User> socialAuth({
    required SocialProvider provider,
    required String accessToken,
    String? idToken,
    String? email,
    String? name,
    String? profilePicture,
    String? fcmToken,
    int userType = 1,
  }) async {
    try {
      final request = SocialAuthRequest(
        provider: provider,
        accessToken: accessToken,
        idToken: idToken,
        email: email,
        name: name,
        profilePicture: profilePicture,
        fcmToken: fcmToken,
        userType: userType,
      );

      final response = await _remoteDataSource.socialAuth(request);

      if (response.user != null && response.token != null) {
        await saveUserData(response.user!, response.token!);
        return Right(response.user!);
      } else {
        return Left(ServerFailure('Invalid response from server'));
      }
    } on ServerException catch (e) {
      return Left(ServerFailure(e.message));
    } on NetworkException catch (e) {
      return Left(NetworkFailure(e.message));
    } on UnauthorizedException catch (e) {
      return Left(UnauthorizedFailure(e.message));
    } catch (e) {
      return Left(UnknownFailure('Unexpected error: $e'));
    }
  }

  @override
  bool isLoggedIn() {
    try {
      // This is a synchronous check, so we'll use a simple approach
      // In a real app, you might want to make this async
      return true; // Placeholder - implement based on your needs
    } catch (e) {
      return false;
    }
  }

  @override
  String? getToken() {
    try {
      // This is a synchronous method, but our storage is async
      // You might want to cache the token in memory for sync access
      return null; // Placeholder - implement based on your needs
    } catch (e) {
      return null;
    }
  }

  @override
  User? getCurrentUser() {
    try {
      // This is a synchronous method, but our storage is async
      // You might want to cache the user in memory for sync access
      return null; // Placeholder - implement based on your needs
    } catch (e) {
      return null;
    }
  }

  @override
  ResultVoid saveUserData(User user, String token) async {
    try {
      final userModel = user is UserModel
          ? user
          : UserModel(
              id: user.id,
              name: user.name,
              fatherName: user.fatherName,
              email: user.email,
              img: user.img,
              mobile: user.mobile,
              mobileNumber: user.mobileNumber,
              userType: user.userType,
              gender: user.gender,
              dayPrice: user.dayPrice,
              hourPrice: user.hourPrice,
              info: user.info,
              experience: user.experience,
              interests: user.interests,
              toursType: user.toursType,
              languages: user.languages,
              fcmToken: user.fcmToken,
              city: user.city,
              birthPlace: user.birthPlace,
              qualification: user.qualification,
              createdAt: user.createdAt,
              updatedAt: user.updatedAt,
              token: token,
            );

      await _localDataSource.saveToken(token);
      await _localDataSource.saveUser(userModel);
      await _localDataSource.saveLoginState(true);

      return const Right(null);
    } on CacheException catch (e) {
      return Left(CacheFailure(e.message));
    } catch (e) {
      return Left(UnknownFailure('Failed to save user data: $e'));
    }
  }

  @override
  ResultVoid clearUserData() async {
    try {
      await _localDataSource.clearAll();
      return const Right(null);
    } on CacheException catch (e) {
      return Left(CacheFailure(e.message));
    } catch (e) {
      return Left(UnknownFailure('Failed to clear user data: $e'));
    }
  }
}
