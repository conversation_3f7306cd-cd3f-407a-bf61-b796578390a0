// lib/features/onboarding/models/onboarding_model.dart

class OnboardingModel {
  final String image;
  final String title;
  final String description;
  final String? buttonText;

  const OnboardingModel({
    required this.image,
    required this.title,
    required this.description,
    this.buttonText,
  });
}

class OnboardingData {
  static const List<OnboardingModel> pages = [
    OnboardingModel(
      image: 'assets/images/onboarding/onboarding_1.png',
      title: 'Explore the world with us',
      description: 'Discover unique tourist destinations and plan your trip easily in one place.',
      buttonText: 'Next',
    ),
    OnboardingModel(
      image: 'assets/images/onboarding/onboarding_2.png',
      title: 'Plan your perfect trip',
      description: 'Create detailed itineraries and book accommodations with just a few taps.',
      buttonText: 'Next',
    ),
    OnboardingModel(
      image: 'assets/images/onboarding/onboarding_3.png',
      title: 'Start your adventure',
      description: 'Join thousands of travelers and make unforgettable memories around the world.',
      buttonText: 'Get Started',
    ),
  ];
}
