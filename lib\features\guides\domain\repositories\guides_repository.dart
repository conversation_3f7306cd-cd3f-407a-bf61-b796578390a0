// features/guides/domain/repositories/guides_repository.dart
import 'package:dartz/dartz.dart';
import 'package:tripooo_user/core/error/failures.dart';
import 'package:tripooo_user/features/guides/domain/entities/guide_entity.dart';

abstract class GuidesRepository {
  /// Get all guides with optional filters
  Future<Either<Failure, List<GuideEntity>>> getGuides({
    int? countryId,
    int? dayPriceFrom,
    int? dayPriceTo,
    int? hourPriceFrom,
    int? hourPriceTo,
    int page = 1,
    int limit = 10,
  });

  /// Get featured guides for home screen
  Future<Either<Failure, List<GuideEntity>>> getFeaturedGuides({
    int limit = 4,
  });

  /// Get guide details by ID
  Future<Either<Failure, GuideEntity>> getGuideById(int id);

  /// Search guides by name or info
  Future<Either<Failure, List<GuideEntity>>> searchGuides({
    required String query,
    int page = 1,
    int limit = 10,
  });

  /// Get guides by interests
  Future<Either<Failure, List<GuideEntity>>> getGuidesByInterests({
    required List<String> interests,
    int page = 1,
    int limit = 10,
  });

  /// Get guides by tour types
  Future<Either<Failure, List<GuideEntity>>> getGuidesByTourTypes({
    required List<String> tourTypes,
    int page = 1,
    int limit = 10,
  });
}
