// features/settings/presentation/pages/language_settings_screen.dart
import 'package:flutter/material.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:go_router/go_router.dart';
import 'package:tripooo_user/core/theme/theme.dart';
import 'package:tripooo_user/core/services/language_service.dart';
import 'package:tripooo_user/core/routing/app_router.dart';
import 'package:tripooo_user/features/onboarding/utils/responsive_helper.dart';

class LanguageSettingsScreen extends StatefulWidget {
  const LanguageSettingsScreen({super.key});

  @override
  State<LanguageSettingsScreen> createState() => _LanguageSettingsScreenState();
}

class _LanguageSettingsScreenState extends State<LanguageSettingsScreen> {
  String selectedLanguage = 'ar';

  final List<LanguageOption> languages = [
    LanguageOption(
      code: 'ar',
      name: 'العربية',
      englishName: 'Arabic',
      flag: '🇸🇦',
    ),
    LanguageOption(
      code: 'en',
      name: 'English',
      englishName: 'English',
      flag: '🇺🇸',
    ),
    LanguageOption(
      code: 'fr',
      name: 'Français',
      englishName: 'French',
      flag: '🇫🇷',
    ),
    LanguageOption(
      code: 'tr',
      name: 'Türkçe',
      englishName: 'Turkish',
      flag: '🇹🇷',
    ),
    LanguageOption(
      code: 'hi',
      name: 'हिन्दी',
      englishName: 'Hindi',
      flag: '🇮🇳',
    ),
  ];

  @override
  void initState() {
    super.initState();
    // Will be set in didChangeDependencies
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    selectedLanguage = context.locale.languageCode;
  }

  @override
  Widget build(BuildContext context) {
    final isTablet = ResponsiveHelper.isTablet(context);

    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: AppBar(
        backgroundColor: AppColors.background,
        elevation: 0,
        leading: IconButton(
          icon: Icon(Icons.arrow_back, color: AppColors.textPrimary),
          onPressed: () => context.goBack(),
        ),
        title: Text(
          'language'.tr(),
          style: AppTextStyles.heading.copyWith(
            fontSize: isTablet ? 24 : 20,
            color: AppColors.textPrimary,
          ),
        ),
        centerTitle: true,
      ),
      body: Column(
        children: [
          // Header Info
          Container(
            width: double.infinity,
            padding: EdgeInsets.all(isTablet ? 32 : 24),
            child: Column(
              children: [
                Icon(
                  Icons.language,
                  size: isTablet ? 80 : 64,
                  color: AppColors.primary,
                ),
                SizedBox(height: isTablet ? 20 : 16),
                Text(
                  'choose_language'.tr(),
                  style: AppTextStyles.heading.copyWith(
                    fontSize: isTablet ? 28 : 24,
                    color: AppColors.textPrimary,
                  ),
                  textAlign: TextAlign.center,
                ),
                SizedBox(height: isTablet ? 12 : 8),
                Text(
                  'choose_language_subtitle'.tr(),
                  style: AppTextStyles.subtitle.copyWith(
                    fontSize: isTablet ? 18 : 16,
                    color: AppColors.textSecondary,
                  ),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          ),

          // Language Options
          Expanded(
            child: ListView.builder(
              padding: EdgeInsets.symmetric(horizontal: isTablet ? 32 : 16),
              itemCount: languages.length,
              itemBuilder: (context, index) {
                final language = languages[index];
                final isSelected = selectedLanguage == language.code;
                return _buildLanguageOption(language, isSelected, isTablet);
              },
            ),
          ),

          // Apply Button
          Container(
            padding: EdgeInsets.all(isTablet ? 32 : 24),
            child: SizedBox(
              width: double.infinity,
              height: isTablet ? 60 : 56,
              child: ElevatedButton(
                onPressed: () async {
                  await _changeLanguage();
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.primary,
                  foregroundColor: AppColors.white,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(
                      AppDimensions.radiusMedium,
                    ),
                  ),
                  elevation: 0,
                ),
                child: Text(
                  'apply_changes'.tr(),
                  style: AppTextStyles.buttonText.copyWith(
                    fontSize: isTablet ? 20 : 18,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildLanguageOption(
    LanguageOption language,
    bool isSelected,
    bool isTablet,
  ) {
    return Card(
      margin: EdgeInsets.only(bottom: isTablet ? 16 : 12),
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(AppDimensions.radiusMedium),
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: () {
            setState(() {
              selectedLanguage = language.code;
            });
          },
          borderRadius: BorderRadius.circular(AppDimensions.radiusMedium),
          child: Container(
            padding: EdgeInsets.all(isTablet ? 24 : 16),
            decoration: BoxDecoration(
              color: isSelected
                  ? AppColors.primary.withValues(alpha: 0.1)
                  : Colors.transparent,
              borderRadius: BorderRadius.circular(AppDimensions.radiusMedium),
              border: Border.all(
                color: isSelected ? AppColors.primary : Colors.transparent,
                width: 2,
              ),
            ),
            child: Row(
              children: [
                Text(
                  language.flag,
                  style: TextStyle(fontSize: isTablet ? 32 : 28),
                ),
                SizedBox(width: isTablet ? 20 : 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        language.name,
                        style: AppTextStyles.bodyMedium.copyWith(
                          fontSize: isTablet ? 20 : 18,
                          fontWeight: FontWeight.w600,
                          color: isSelected
                              ? AppColors.primary
                              : AppColors.textPrimary,
                        ),
                      ),
                      SizedBox(height: 4),
                      Text(
                        language.englishName,
                        style: AppTextStyles.bodySmall.copyWith(
                          fontSize: isTablet ? 16 : 14,
                          color: AppColors.textSecondary,
                        ),
                      ),
                    ],
                  ),
                ),
                if (isSelected)
                  Container(
                    padding: EdgeInsets.all(isTablet ? 8 : 6),
                    decoration: BoxDecoration(
                      color: AppColors.primary,
                      shape: BoxShape.circle,
                    ),
                    child: Icon(
                      Icons.check,
                      color: AppColors.white,
                      size: isTablet ? 24 : 20,
                    ),
                  ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Future<void> _changeLanguage() async {
    try {
      // Show loading
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) =>
            Center(child: CircularProgressIndicator(color: AppColors.primary)),
      );

      // Change language
      await LanguageService.changeLanguage(context, selectedLanguage);

      // Small delay to ensure language change is applied
      await Future.delayed(const Duration(milliseconds: 300));

      // Close loading and go back to More screen
      if (mounted) {
        Navigator.pop(context); // Close loading dialog

        // Go back to More screen using GoRouter
        context.go(AppRoutes.more);

        // Show success message after navigation
        Future.delayed(const Duration(milliseconds: 500), () {
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text('language_changed_successfully'.tr()),
                backgroundColor: AppColors.success,
                duration: const Duration(seconds: 2),
              ),
            );
          }
        });
      }
    } catch (e) {
      // Close loading
      if (mounted) {
        Navigator.pop(context);

        // Show error message
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('error_changing_language'.tr()),
            backgroundColor: AppColors.error,
            duration: const Duration(seconds: 2),
          ),
        );
      }
    }
  }
}

class LanguageOption {
  final String code;
  final String name;
  final String englishName;
  final String flag;

  LanguageOption({
    required this.code,
    required this.name,
    required this.englishName,
    required this.flag,
  });
}
