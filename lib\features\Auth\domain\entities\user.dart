// features/Auth/domain/entities/user.dart
import 'package:equatable/equatable.dart';
import 'package:tripooo_user/features/Auth/data/models/language_model.dart';

class User extends Equatable {
  final int id;
  final String name;
  final String? fatherName;
  final String email;
  final String? img;
  final String mobile;
  final String? mobileNumber;
  final int userType; // 1 for client, 2 for guide
  final String? gender;
  final double? dayPrice;
  final double? hourPrice;
  final String? info;
  final String? experience;
  final List<String>? interests;
  final List<String>? toursType;
  final List<LanguageModel>? languages;
  final String? fcmToken;
  final String? city;
  final String? birthPlace;
  final String? qualification;
  final DateTime? createdAt;
  final DateTime? updatedAt;
  final String? token;

  const User({
    required this.id,
    required this.name,
    this.fatherName,
    required this.email,
    this.img,
    required this.mobile,
    this.mobileNumber,
    required this.userType,
    this.gender,
    this.dayPrice,
    this.hourPrice,
    this.info,
    this.experience,
    this.interests,
    this.toursType,
    this.languages,
    this.fcmToken,
    this.city,
    this.birthPlace,
    this.qualification,
    this.createdAt,
    this.updatedAt,
    this.token,
  });

  bool get isClient => userType == 1;
  bool get isGuide => userType == 2;

  User copyWith({
    int? id,
    String? name,
    String? fatherName,
    String? email,
    String? img,
    String? mobile,
    String? mobileNumber,
    int? userType,
    String? gender,
    double? dayPrice,
    double? hourPrice,
    String? info,
    String? experience,
    List<String>? interests,
    List<String>? toursType,
    List<LanguageModel>? languages,
    String? fcmToken,
    String? city,
    String? birthPlace,
    String? qualification,
    DateTime? createdAt,
    DateTime? updatedAt,
    String? token,
  }) {
    return User(
      id: id ?? this.id,
      name: name ?? this.name,
      fatherName: fatherName ?? this.fatherName,
      email: email ?? this.email,
      img: img ?? this.img,
      mobile: mobile ?? this.mobile,
      mobileNumber: mobileNumber ?? this.mobileNumber,
      userType: userType ?? this.userType,
      gender: gender ?? this.gender,
      dayPrice: dayPrice ?? this.dayPrice,
      hourPrice: hourPrice ?? this.hourPrice,
      info: info ?? this.info,
      experience: experience ?? this.experience,
      interests: interests ?? this.interests,
      toursType: toursType ?? this.toursType,
      languages: languages ?? this.languages,
      fcmToken: fcmToken ?? this.fcmToken,
      city: city ?? this.city,
      birthPlace: birthPlace ?? this.birthPlace,
      qualification: qualification ?? this.qualification,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      token: token ?? this.token,
    );
  }

  @override
  List<Object?> get props => [
    id,
    name,
    fatherName,
    email,
    img,
    mobile,
    mobileNumber,
    userType,
    gender,
    dayPrice,
    hourPrice,
    info,
    experience,
    interests,
    toursType,
    languages,
    fcmToken,
    city,
    birthPlace,
    qualification,
    createdAt,
    updatedAt,
    token,
  ];
}
