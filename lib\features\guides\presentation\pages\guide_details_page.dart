// features/guides/presentation/pages/guide_details_page.dart
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:tripooo_user/core/theme/theme.dart';

import 'package:tripooo_user/core/di/injection_container.dart' as di;
import 'package:tripooo_user/features/guides/presentation/cubit/guide_details_cubit.dart';
import 'package:tripooo_user/features/guides/domain/entities/guide_details_entity.dart';

class GuideDetailsPage extends StatelessWidget {
  final int guideId;
  final String? guideName;

  const GuideDetailsPage({super.key, required this.guideId, this.guideName});

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) =>
          di.sl<GuideDetailsCubit>()..loadGuideDetails(guideId),
      child: Scaffold(
        backgroundColor: AppColors.background,
        body: BlocBuilder<GuideDetailsCubit, GuideDetailsState>(
          builder: (context, state) {
            if (state is GuideDetailsLoading) {
              return _buildLoadingState();
            } else if (state is GuideDetailsLoaded) {
              return _buildGuideDetails(context, state.guideDetails);
            } else if (state is GuideDetailsError) {
              return _buildErrorState(context, state.message);
            }

            return _buildLoadingState();
          },
        ),
      ),
    );
  }

  Widget _buildLoadingState() {
    return Scaffold(
      appBar: AppBar(backgroundColor: AppColors.primary, elevation: 0),
      body: Center(
        child: CircularProgressIndicator(
          valueColor: AlwaysStoppedAnimation<Color>(AppColors.primary),
        ),
      ),
    );
  }

  Widget _buildGuideDetails(BuildContext context, GuideDetailsEntity guide) {
    return CustomScrollView(
      slivers: [
        // App Bar with Image
        SliverAppBar(
          expandedHeight: 300,
          pinned: true,
          backgroundColor: AppColors.primary,
          flexibleSpace: FlexibleSpaceBar(
            background: Stack(
              fit: StackFit.expand,
              children: [
                guide.hasImage
                    ? Image.network(
                        guide.imageUrl,
                        fit: BoxFit.cover,
                        errorBuilder: (context, error, stackTrace) {
                          return Container(
                            color: Colors.grey[300],
                            child: Icon(
                              Icons.person,
                              size: 100,
                              color: Colors.grey[600],
                            ),
                          );
                        },
                      )
                    : Container(
                        color: Colors.grey[300],
                        child: Icon(
                          Icons.person,
                          size: 100,
                          color: Colors.grey[600],
                        ),
                      ),
                // Gradient overlay
                Container(
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      begin: Alignment.topCenter,
                      end: Alignment.bottomCenter,
                      colors: [
                        Colors.transparent,
                        Colors.black.withValues(alpha: 0.7),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
          actions: [
            IconButton(
              icon: Icon(Icons.share, color: Colors.white),
              onPressed: () {
                // TODO: Share guide
              },
            ),
            IconButton(
              icon: Icon(Icons.favorite_border, color: Colors.white),
              onPressed: () {
                // TODO: Add to favorites
              },
            ),
          ],
        ),

        // Guide Details
        SliverToBoxAdapter(
          child: Padding(
            padding: EdgeInsets.all(20),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Guide Name and Rating
                Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            guide.displayName,
                            style: AppTextStyles.heading.copyWith(
                              fontSize: 24,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          SizedBox(height: 8),
                          Row(
                            children: [
                              Row(
                                children: List.generate(5, (index) {
                                  return Icon(
                                    index < guide.rating.floor()
                                        ? Icons.star
                                        : Icons.star_border,
                                    color: Colors.amber,
                                    size: 20,
                                  );
                                }),
                              ),
                              SizedBox(width: 8),
                              Text(
                                '(${guide.rating.toStringAsFixed(1)})',
                                style: AppTextStyles.bodyMedium.copyWith(
                                  color: Colors.grey[600],
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                    Container(
                      padding: EdgeInsets.symmetric(
                        horizontal: 12,
                        vertical: 6,
                      ),
                      decoration: BoxDecoration(
                        color: guide.isActive ? Colors.green : Colors.red,
                        borderRadius: BorderRadius.circular(20),
                      ),
                      child: Text(
                        guide.isActive ? 'متاح' : 'غير متاح',
                        style: AppTextStyles.bodySmall.copyWith(
                          color: Colors.white,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ),
                  ],
                ),

                SizedBox(height: 24),

                // Description
                _buildSection(
                  title: 'نبذة عن المرشد',
                  content: guide.displayInfo,
                ),

                SizedBox(height: 20),

                // Experience
                _buildSection(
                  title: 'الخبرة',
                  content: guide.displayExperience,
                ),

                SizedBox(height: 20),

                // Interests
                if (guide.displayInterests.isNotEmpty) ...[
                  _buildChipsSection(
                    title: 'الاهتمامات',
                    items: guide.displayInterests,
                    color: Colors.blue,
                  ),
                  SizedBox(height: 20),
                ],

                // Tour Types
                if (guide.displayToursType.isNotEmpty) ...[
                  _buildChipsSection(
                    title: 'أنواع الرحلات',
                    items: guide.displayToursType,
                    color: Colors.green,
                  ),
                  SizedBox(height: 20),
                ],

                // Pricing
                _buildPricingSection(guide),

                SizedBox(height: 30),

                // Contact Buttons
                Row(
                  children: [
                    Expanded(
                      child: ElevatedButton.icon(
                        onPressed: () {
                          // TODO: Call guide
                        },
                        icon: Icon(Icons.phone),
                        label: Text('اتصال'),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: AppColors.primary,
                          padding: EdgeInsets.symmetric(vertical: 12),
                        ),
                      ),
                    ),
                    SizedBox(width: 16),
                    Expanded(
                      child: OutlinedButton.icon(
                        onPressed: () {
                          // TODO: Send message
                        },
                        icon: Icon(Icons.message),
                        label: Text('رسالة'),
                        style: OutlinedButton.styleFrom(
                          foregroundColor: AppColors.primary,
                          side: BorderSide(color: AppColors.primary),
                          padding: EdgeInsets.symmetric(vertical: 12),
                        ),
                      ),
                    ),
                  ],
                ),

                SizedBox(height: 20),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildSection({required String title, required String content}) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: AppTextStyles.heading.copyWith(
            fontSize: 18,
            fontWeight: FontWeight.bold,
          ),
        ),
        SizedBox(height: 8),
        Text(
          content,
          style: AppTextStyles.bodyMedium.copyWith(
            color: Colors.grey[700],
            height: 1.5,
          ),
        ),
      ],
    );
  }

  Widget _buildChipsSection({
    required String title,
    required List<String> items,
    required Color color,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: AppTextStyles.heading.copyWith(
            fontSize: 18,
            fontWeight: FontWeight.bold,
          ),
        ),
        SizedBox(height: 8),
        Wrap(
          spacing: 8,
          runSpacing: 8,
          children: items.map((item) {
            return Chip(
              label: Text(
                item,
                style: AppTextStyles.bodySmall.copyWith(color: color),
              ),
              backgroundColor: color.withValues(alpha: 0.1),
              side: BorderSide(color: color.withValues(alpha: 0.3)),
            );
          }).toList(),
        ),
      ],
    );
  }

  Widget _buildPricingSection(GuideDetailsEntity guide) {
    return Container(
      padding: EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.grey[50],
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey[200]!),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'الأسعار',
            style: AppTextStyles.heading.copyWith(
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          SizedBox(height: 12),
          Row(
            children: [
              Expanded(
                child: Column(
                  children: [
                    Icon(Icons.access_time, color: AppColors.primary, size: 24),
                    SizedBox(height: 4),
                    Text(
                      'سعر الساعة',
                      style: AppTextStyles.bodySmall.copyWith(
                        color: Colors.grey[600],
                      ),
                    ),
                    SizedBox(height: 4),
                    Text(
                      guide.formattedHourPrice,
                      style: AppTextStyles.bodyMedium.copyWith(
                        fontWeight: FontWeight.bold,
                        color: AppColors.primary,
                      ),
                    ),
                  ],
                ),
              ),
              Container(width: 1, height: 60, color: Colors.grey[300]),
              Expanded(
                child: Column(
                  children: [
                    Icon(
                      Icons.calendar_today,
                      color: AppColors.primary,
                      size: 24,
                    ),
                    SizedBox(height: 4),
                    Text(
                      'سعر اليوم',
                      style: AppTextStyles.bodySmall.copyWith(
                        color: Colors.grey[600],
                      ),
                    ),
                    SizedBox(height: 4),
                    Text(
                      guide.formattedDayPrice,
                      style: AppTextStyles.bodyMedium.copyWith(
                        fontWeight: FontWeight.bold,
                        color: AppColors.primary,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildErrorState(BuildContext context, String message) {
    return Scaffold(
      appBar: AppBar(
        backgroundColor: AppColors.primary,
        elevation: 0,
        title: Text('تفاصيل المرشد'),
      ),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.error_outline, size: 64, color: Colors.red[400]),
            SizedBox(height: 16),
            Text(
              'حدث خطأ في تحميل تفاصيل المرشد',
              style: AppTextStyles.bodyMedium.copyWith(color: Colors.grey[600]),
            ),
            SizedBox(height: 8),
            Text(
              message,
              style: AppTextStyles.bodySmall.copyWith(color: Colors.grey[500]),
              textAlign: TextAlign.center,
            ),
            SizedBox(height: 16),
            ElevatedButton(
              onPressed: () {
                context.read<GuideDetailsCubit>().loadGuideDetails(guideId);
              },
              child: Text('إعادة المحاولة'),
            ),
          ],
        ),
      ),
    );
  }
}
