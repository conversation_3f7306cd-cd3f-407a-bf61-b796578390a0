// lib/features/onboarding/utils/responsive_helper.dart
import 'package:flutter/material.dart';

class ResponsiveHelper {
  static bool isTablet(BuildContext context) {
    return MediaQuery.of(context).size.width > 600;
  }

  static bool isLandscape(BuildContext context) {
    final size = MediaQuery.of(context).size;
    return size.width > size.height;
  }

  static bool isSmallScreen(BuildContext context) {
    return MediaQuery.of(context).size.height < 700;
  }

  static double getImageHeight(BuildContext context) {
    final screenHeight = MediaQuery.of(context).size.height;
    final isTablet = ResponsiveHelper.isTablet(context);
    final isLandscape = ResponsiveHelper.isLandscape(context);
    
    if (isLandscape) {
      return screenHeight * 0.5;
    } else if (isTablet) {
      return screenHeight * 0.65;
    } else {
      return screenHeight * 0.6;
    }
  }

  static double getContentHeight(BuildContext context) {
    final screenHeight = MediaQuery.of(context).size.height;
    final isTablet = ResponsiveHelper.isTablet(context);
    final isLandscape = ResponsiveHelper.isLandscape(context);
    
    if (isLandscape) {
      return screenHeight * 0.5;
    } else if (isTablet) {
      return screenHeight * 0.35;
    } else {
      return screenHeight * 0.4;
    }
  }

  static EdgeInsets getContentPadding(BuildContext context) {
    final isTablet = ResponsiveHelper.isTablet(context);
    
    return EdgeInsets.symmetric(
      horizontal: isTablet ? 48.0 : 24.0,
      vertical: isTablet ? 32.0 : 24.0,
    );
  }

  static double getTitleFontSize(BuildContext context) {
    final isTablet = ResponsiveHelper.isTablet(context);
    return isTablet ? 36.0 : 28.0;
  }

  static double getDescriptionFontSize(BuildContext context) {
    final isTablet = ResponsiveHelper.isTablet(context);
    return isTablet ? 18.0 : 16.0;
  }

  static double getButtonHeight(BuildContext context) {
    final isTablet = ResponsiveHelper.isTablet(context);
    return isTablet ? 60.0 : 50.0;
  }

  static double getIndicatorSize(BuildContext context) {
    final isTablet = ResponsiveHelper.isTablet(context);
    return isTablet ? 12.0 : 8.0;
  }

  static double getSpacing(BuildContext context, {required String type}) {
    final isTablet = ResponsiveHelper.isTablet(context);
    
    switch (type) {
      case 'small':
        return isTablet ? 12.0 : 8.0;
      case 'medium':
        return isTablet ? 20.0 : 16.0;
      case 'large':
        return isTablet ? 32.0 : 24.0;
      case 'xlarge':
        return isTablet ? 48.0 : 32.0;
      default:
        return isTablet ? 20.0 : 16.0;
    }
  }
}
