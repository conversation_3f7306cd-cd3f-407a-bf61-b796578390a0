// features/more/presentation/widgets/more_footer.dart
import 'package:flutter/material.dart';
import 'package:easy_localization/easy_localization.dart' hide TextDirection;
import 'package:tripooo_user/core/theme/theme.dart';
import 'package:tripooo_user/features/more/more_constants.dart';
import 'package:tripooo_user/features/onboarding/utils/responsive_helper.dart';

class MoreFooter extends StatelessWidget {
  const MoreFooter({super.key});

  @override
  Widget build(BuildContext context) {
    final isTablet = ResponsiveHelper.isTablet(context);

    return Directionality(
      textDirection: TextDirection.ltr,
      child: Padding(
        padding: EdgeInsets.all(
          isTablet ? MoreConstants.paddingTablet : MoreConstants.paddingMobile,
        ),
        child: RichText(
          text: TextSpan(
            children: [
              TextSpan(
                text: 'Powered By : ',
                style: AppTextStyles.bodyMedium.copyWith(
                  color: Colors.black,
                  fontWeight: FontWeight.w700,
                ),
              ),
              TextSpan(
                text: 'باقة التقنيه',
                style: AppTextStyles.bodyMedium.copyWith(
                  color: MoreConstants.footerColor,
                  fontWeight: FontWeight.w700,
                  fontStyle: FontStyle.normal,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
