// core/widgets/responsive_image.dart
import 'package:flutter/material.dart';
import '../utils/responsive_extensions.dart';

class ResponsiveImage extends StatelessWidget {
  final String asset;
  final double width;
  final double height;
  const ResponsiveImage({
    required this.asset,
    required this.width,
    required this.height,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Image.asset(
      asset,
      width: width.w(context),
      height: height.h(context),
      fit: BoxFit.contain,
    );
  }
}
