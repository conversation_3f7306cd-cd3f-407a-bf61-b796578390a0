// features/Auth/domain/usecases/change_password.dart
import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';
import '../../../../core/error/failures.dart';
import '../../../../core/usecases/usecase.dart';
import '../repositories/auth_repository.dart';

class ChangePassword implements UseCase<String, ChangePasswordParams> {
  final AuthRepository repository;

  const ChangePassword(this.repository);

  @override
  Future<Either<Failure, String>> call(ChangePasswordParams params) async {
    return await repository.changePassword(
      newPassword: params.newPassword,
      newPasswordConfirmation: params.newPasswordConfirmation,
      oldPassword: params.oldPassword,
    );
  }
}

class ChangePasswordParams extends Equatable {
  final String newPassword;
  final String newPasswordConfirmation;
  final String oldPassword;

  const ChangePasswordParams({
    required this.newPassword,
    required this.newPasswordConfirmation,
    required this.oldPassword,
  });

  @override
  List<Object> get props => [newPassword, newPasswordConfirmation, oldPassword];
}
