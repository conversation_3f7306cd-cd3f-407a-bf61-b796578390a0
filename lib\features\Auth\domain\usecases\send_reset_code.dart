// features/Auth/domain/usecases/send_reset_code.dart
import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';
import '../../../../core/error/failures.dart';
import '../../../../core/usecases/usecase.dart';
import '../repositories/auth_repository.dart';

class SendResetCode implements UseCase<String, SendResetCodeParams> {
  final AuthRepository repository;

  const SendResetCode(this.repository);

  @override
  Future<Either<Failure, String>> call(SendResetCodeParams params) async {
    return await repository.sendResetCode(mobile: params.mobile);
  }
}

class SendResetCodeParams extends Equatable {
  final String mobile;

  const SendResetCodeParams({required this.mobile});

  @override
  List<Object> get props => [mobile];
}
