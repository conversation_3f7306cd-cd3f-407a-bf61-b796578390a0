// core/network/dio_helper.dart
import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import 'api_endpoints.dart';

class DioHelper {
  static late Dio _dio;

  static Dio get dio => _dio;

  static void init() {
    _dio = Dio(
      BaseOptions(
        baseUrl: ApiEndpoints.baseUrl,
        receiveDataWhenStatusError: true,
        connectTimeout: const Duration(seconds: 30),
        receiveTimeout: const Duration(seconds: 30),
      ),
    );

    // Add interceptors
    _dio.interceptors.add(_CustomInterceptor());

    if (kDebugMode) {
      _dio.interceptors.add(
        LogInterceptor(
          requestBody: true,
          responseBody: true,
          requestHeader: true,
          responseHeader: true,
          logPrint: (obj) => debugPrint('🌐 DIO LOG: $obj'),
        ),
      );
    }
  }

  // GET request
  static Future<Response> getData({
    required String url,
    Map<String, dynamic>? query,
    String? token,
  }) async {
    _dio.options.headers = token != null
        ? ApiEndpoints.authHeaders(token)
        : ApiEndpoints.defaultHeaders;

    return await _dio.get(url, queryParameters: query);
  }

  // POST request with FormData
  static Future<Response> postData({
    required String url,
    Map<String, dynamic>? data,
    Map<String, dynamic>? query,
    String? token,
  }) async {
    _dio.options.headers = token != null
        ? ApiEndpoints.authHeaders(token)
        : ApiEndpoints.defaultHeaders;

    FormData? formData;
    if (data != null) {
      formData = FormData.fromMap(data);
    }

    return await _dio.post(url, data: formData, queryParameters: query);
  }

  // POST request with file upload
  static Future<Response> postDataWithFile({
    required String url,
    required Map<String, dynamic> data,
    String? token,
  }) async {
    _dio.options.headers = token != null
        ? ApiEndpoints.authHeaders(token)
        : ApiEndpoints.defaultHeaders;

    FormData formData = FormData.fromMap(data);

    return await _dio.post(url, data: formData);
  }

  // PUT request
  static Future<Response> putData({
    required String url,
    Map<String, dynamic>? data,
    Map<String, dynamic>? query,
    String? token,
  }) async {
    _dio.options.headers = token != null
        ? ApiEndpoints.authHeaders(token)
        : ApiEndpoints.defaultHeaders;

    return await _dio.put(url, data: data, queryParameters: query);
  }

  // DELETE request
  static Future<Response> deleteData({
    required String url,
    Map<String, dynamic>? data,
    Map<String, dynamic>? query,
    String? token,
  }) async {
    _dio.options.headers = token != null
        ? ApiEndpoints.authHeaders(token)
        : ApiEndpoints.defaultHeaders;

    return await _dio.delete(url, data: data, queryParameters: query);
  }
}

class _CustomInterceptor extends Interceptor {
  @override
  void onRequest(RequestOptions options, RequestInterceptorHandler handler) {
    debugPrint('📤 REQUEST: ${options.method} ${options.uri}');
    debugPrint('📤 HEADERS: ${options.headers}');
    debugPrint('📤 DATA: ${options.data}');
    super.onRequest(options, handler);
  }

  @override
  void onResponse(Response response, ResponseInterceptorHandler handler) {
    debugPrint(
      '📥 RESPONSE: ${response.statusCode} ${response.requestOptions.uri}',
    );
    debugPrint('📥 DATA: ${response.data}');
    super.onResponse(response, handler);
  }

  @override
  void onError(DioException err, ErrorInterceptorHandler handler) {
    debugPrint(
      '❌ ERROR: ${err.response?.statusCode} ${err.requestOptions.uri}',
    );
    debugPrint('❌ MESSAGE: ${err.message}');
    debugPrint('❌ DATA: ${err.response?.data}');
    super.onError(err, handler);
  }
}
