// features/guides/presentation/pages/guides_list_page.dart
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:tripooo_user/core/theme/theme.dart';
import 'package:tripooo_user/core/widgets/widgets.dart';
import 'package:tripooo_user/core/di/injection_container.dart' as di;
import 'package:tripooo_user/features/guides/presentation/cubit/guides_cubit.dart';
import 'package:tripooo_user/features/guides/presentation/cubit/guides_state.dart';

class GuidesListPage extends StatelessWidget {
  const GuidesListPage({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => di.sl<GuidesCubit>()..getGuides(),
      child: Scaffold(
        backgroundColor: AppColors.background,
        appBar: AppBar(
          title: Text(
            'المرشدين السياحيين',
            style: AppTextStyles.heading.copyWith(
              color: Colors.white,
              fontSize: 20,
            ),
          ),
          backgroundColor: AppColors.primary,
          elevation: 0,
          centerTitle: true,
          actions: [
            IconButton(
              icon: Icon(Icons.search, color: Colors.white),
              onPressed: () {
                // TODO: Navigate to search page
              },
            ),
            IconButton(
              icon: Icon(Icons.filter_list, color: Colors.white),
              onPressed: () {
                // TODO: Show filter dialog
              },
            ),
          ],
        ),
        body: BlocBuilder<GuidesCubit, GuidesState>(
          builder: (context, state) {
            if (state is GuidesLoading) {
              return Center(
                child: CircularProgressIndicator(
                  valueColor: AlwaysStoppedAnimation<Color>(AppColors.primary),
                ),
              );
            } else if (state is GuidesLoaded) {
              if (state.guides.isEmpty) {
                return _buildEmptyState();
              }
              return _buildGuidesList(context, state);
            } else if (state is GuidesError) {
              return _buildErrorState(context, state.message);
            }
            
            return _buildEmptyState();
          },
        ),
      ),
    );
  }

  Widget _buildGuidesList(BuildContext context, GuidesLoaded state) {
    return RefreshIndicator(
      onRefresh: () async {
        context.read<GuidesCubit>().refreshGuides();
      },
      child: ListView.builder(
        padding: EdgeInsets.all(16),
        itemCount: state.guides.length + (state.hasReachedMax ? 0 : 1),
        itemBuilder: (context, index) {
          if (index >= state.guides.length) {
            // Load more indicator
            context.read<GuidesCubit>().getGuides();
            return Center(
              child: Padding(
                padding: EdgeInsets.all(16),
                child: CircularProgressIndicator(
                  valueColor: AlwaysStoppedAnimation<Color>(AppColors.primary),
                ),
              ),
            );
          }
          
          final guide = state.guides[index];
          return Card(
            margin: EdgeInsets.only(bottom: 16),
            elevation: 2,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
            child: InkWell(
              borderRadius: BorderRadius.circular(12),
              onTap: () {
                // TODO: Navigate to guide details
              },
              child: Padding(
                padding: EdgeInsets.all(16),
                child: Row(
                  children: [
                    // Guide Avatar
                    Container(
                      width: 60,
                      height: 60,
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(8),
                        color: Colors.grey[300],
                      ),
                      child: ClipRRect(
                        borderRadius: BorderRadius.circular(8),
                        child: guide.hasImage
                            ? Image.network(
                                guide.imageUrl,
                                fit: BoxFit.cover,
                                errorBuilder: (context, error, stackTrace) {
                                  return Icon(
                                    Icons.person,
                                    color: Colors.grey[600],
                                    size: 30,
                                  );
                                },
                              )
                            : Icon(
                                Icons.person,
                                color: Colors.grey[600],
                                size: 30,
                              ),
                      ),
                    ),
                    SizedBox(width: 16),
                    // Guide Info
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            guide.displayName,
                            style: AppTextStyles.heading.copyWith(
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                            ),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                          SizedBox(height: 4),
                          Text(
                            guide.displayInfo,
                            style: AppTextStyles.bodySmall.copyWith(
                              color: Colors.grey[600],
                              fontSize: 12,
                            ),
                            maxLines: 2,
                            overflow: TextOverflow.ellipsis,
                          ),
                          SizedBox(height: 8),
                          Row(
                            children: [
                              Icon(
                                Icons.star,
                                color: Colors.amber,
                                size: 16,
                              ),
                              SizedBox(width: 4),
                              Text(
                                guide.rating.toStringAsFixed(1),
                                style: AppTextStyles.bodySmall.copyWith(
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                              Spacer(),
                              Text(
                                guide.formattedDayPrice,
                                style: AppTextStyles.bodySmall.copyWith(
                                  color: AppColors.primary,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.person_search,
            size: 64,
            color: Colors.grey[400],
          ),
          SizedBox(height: 16),
          Text(
            'لا توجد مرشدين متاحين حالياً',
            style: AppTextStyles.bodyMedium.copyWith(
              color: Colors.grey[600],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildErrorState(BuildContext context, String message) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.error_outline,
            size: 64,
            color: Colors.red[400],
          ),
          SizedBox(height: 16),
          Text(
            'حدث خطأ في تحميل المرشدين',
            style: AppTextStyles.bodyMedium.copyWith(
              color: Colors.grey[600],
            ),
          ),
          SizedBox(height: 8),
          Text(
            message,
            style: AppTextStyles.bodySmall.copyWith(
              color: Colors.grey[500],
            ),
            textAlign: TextAlign.center,
          ),
          SizedBox(height: 16),
          ElevatedButton(
            onPressed: () {
              context.read<GuidesCubit>().getGuides(refresh: true);
            },
            child: Text('إعادة المحاولة'),
          ),
        ],
      ),
    );
  }
}
