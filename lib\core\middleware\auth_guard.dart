// core/middleware/auth_guard.dart
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:tripooo_user/core/routing/app_router.dart';
import '../utils/constants.dart';

class AuthGuard {
  static const _secureStorage = FlutterSecureStorage();

  static Future<String?> redirect(
    BuildContext context,
    GoRouterState state,
  ) async {
    final prefs = await SharedPreferences.getInstance();
    final token = await _secureStorage.read(key: AppConstants.tokenKey);
    final isLoggedIn = prefs.getBool(AppConstants.isLoggedInKey) ?? false;

    final currentPath = state.fullPath ?? state.path ?? '/';
    final isAuthRoute = _isAuthRoute(currentPath);
    final isProtectedRoute = _isProtectedRoute(currentPath);

    // If user is not logged in and trying to access protected route
    if (!isLoggedIn && isProtectedRoute) {
      return AppRoutes.login;
    }

    // If user is logged in and trying to access auth routes
    if (isLoggedIn && isAuthRoute) {
      return AppRoutes.home;
    }

    // If token exists but user is not marked as logged in, verify token
    if (token != null && !isLoggedIn) {
      // TODO: Implement token verification with backend
      // For now, assume token is valid if it exists
      await prefs.setBool(AppConstants.isLoggedInKey, true);
      return AppRoutes.home;
    }

    return null; // No redirect needed
  }

  static bool _isAuthRoute(String location) {
    final authRoutes = [
      AppRoutes.login,
      AppRoutes.register,
      AppRoutes.forgotPassword,
      AppRoutes.mobileVerification,
      AppRoutes.onboarding,
      AppRoutes.welcome,
      AppRoutes.languageSelection,
    ];

    return authRoutes.any((route) => location.startsWith(route));
  }

  static bool _isProtectedRoute(String location) {
    final protectedRoutes = [
      // Removed AppRoutes.home to support Guest Mode
      AppRoutes.profile,
      AppRoutes.changePassword,
    ];

    return protectedRoutes.any((route) => location.startsWith(route));
  }

  static Future<bool> isUserLoggedIn() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getBool(AppConstants.isLoggedInKey) ?? false;
  }

  static Future<String?> getToken() async {
    return await _secureStorage.read(key: AppConstants.tokenKey);
  }

  static Future<void> clearAuthData() async {
    final prefs = await SharedPreferences.getInstance();
    await _secureStorage.delete(key: AppConstants.tokenKey);
    await prefs.remove(AppConstants.userKey);
    await prefs.setBool(AppConstants.isLoggedInKey, false);
  }
}

// Extension to check auth status in widgets
extension AuthContext on BuildContext {
  Future<bool> get isLoggedIn => AuthGuard.isUserLoggedIn();
  Future<String?> get authToken => AuthGuard.getToken();

  Future<void> logout() async {
    await AuthGuard.clearAuthData();
    if (mounted) {
      goToLogin();
    }
  }
}
