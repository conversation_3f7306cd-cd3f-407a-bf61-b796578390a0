// features/guides/data/models/working_day_model.dart
import '../../domain/entities/working_day_entity.dart';

class WorkingDayModel extends WorkingDayEntity {
  const WorkingDayModel({
    required super.id,
    required super.createdAt,
    required super.updatedAt,
    required super.day,
    required super.start,
    required super.end,
    super.dayStatus,
    required super.guideId,
    super.deletedAt,
    required super.isActivate,
  });

  factory WorkingDayModel.fromJson(Map<String, dynamic> json) {
    return WorkingDayModel(
      id: json['id'] ?? 0,
      createdAt: json['created_at'] ?? '',
      updatedAt: json['updated_at'] ?? '',
      day: json['day'] ?? '',
      start: json['start'] ?? '',
      end: json['end'] ?? '',
      dayStatus: json['day_status'],
      guideId: json['guide_id']?.toString() ?? '',
      deletedAt: json['deleted_at'],
      isActivate: json['is_activate'] ?? 0,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'created_at': createdAt,
      'updated_at': updatedAt,
      'day': day,
      'start': start,
      'end': end,
      'day_status': dayStatus,
      'guide_id': guideId,
      'deleted_at': deletedAt,
      'is_activate': isActivate,
    };
  }
}
