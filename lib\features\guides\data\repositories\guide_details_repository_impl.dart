// features/guides/data/repositories/guide_details_repository_impl.dart
import 'package:dartz/dartz.dart';
import '../../../../core/error/exceptions.dart';
import '../../../../core/error/failures.dart';
import '../../../../core/network/network_info.dart';
import '../../domain/entities/guide_details_entity.dart';
import '../../domain/repositories/guide_details_repository.dart';
import '../datasources/guide_details_remote_data_source.dart';

class GuideDetailsRepositoryImpl implements GuideDetailsRepository {
  final GuideDetailsRemoteDataSource remoteDataSource;
  final NetworkInfo networkInfo;

  GuideDetailsRepositoryImpl({
    required this.remoteDataSource,
    required this.networkInfo,
  });

  @override
  Future<Either<Failure, GuideDetailsEntity>> getGuideDetails(
    int guideId,
  ) async {
    if (await networkInfo.isConnected) {
      try {
        final guideDetails = await remoteDataSource.getGuideDetails(guideId);
        return Right(guideDetails);
      } on ServerException catch (e) {
        return Left(ServerFailure(e.message, e.statusCode ?? 500));
      } catch (e) {
        return Left(ServerFailure('Unexpected error occurred', 500));
      }
    } else {
      return Left(NetworkFailure('No internet connection', 0));
    }
  }
}
