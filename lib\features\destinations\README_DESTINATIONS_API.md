# Destinations API Documentation

## 📋 Overview

This documentation covers the implementation of the Destinations API endpoints in the Tripooo User app.

## 🔗 API Endpoints

### 1. Get Destinations List
```
GET /api/destinations/{offset}/{limit}?country_id={id}&price_from={min}&price_to={max}
```

### 2. Get Destination Details
```
GET /api/destinations/details/{destinationId}
```

### Response Structure
```json
{
    "status": 200,
    "msg": "success",
    "data": [
        {
            "id": 3,
            "created_at": "2025-07-14T01:08:13.000000Z",
            "updated_at": "2025-07-14T01:08:13.000000Z",
            "img": "admin/assets/images/destinations/175245169338371.png",
            "albums": [""],
            "day_price": 235,
            "map_location": "Ipsa et aspernatur",
            "country_id": 2,
            "deleted_at": null,
            "is_activate": 1,
            "name": "<PERSON> Baker",
            "info": "Facilis totam ut ut",
            "location": "Amet ut voluptatem",
            "translations": [...]
        }
    ]
}
```

## 🏗️ Architecture

### 📁 Domain Layer
- **DestinationEntity**: Core destination business object
- **DestinationTranslationEntity**: Translation support
- **DestinationsRepository**: Abstract interface
- **GetDestinations**: Use case for fetching destinations list
- **GetDestinationDetails**: Use case for fetching destination details

### 📁 Data Layer
- **DestinationModel**: Data transfer object
- **DestinationTranslationModel**: Translation model
- **DestinationsRemoteDataSource**: API communication
- **DestinationsRepositoryImpl**: Repository implementation

### 📁 Presentation Layer
- **DestinationsCubit**: State management
- **DestinationsSectionWidget**: Home screen widget

## 🎯 Usage Examples

### 1. Using in Home Screen

The `DestinationsSectionWidget` is already integrated in the home screen:

```dart
const DestinationsSectionWidget(), // In HomeScreen
```

### 2. Manual Usage with Cubit

```dart
class DestinationsPage extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => di.sl<DestinationsCubit>()..loadDestinations(),
      child: Scaffold(
        body: BlocBuilder<DestinationsCubit, DestinationsState>(
          builder: (context, state) {
            if (state is DestinationsLoading) {
              return CircularProgressIndicator();
            } else if (state is DestinationsLoaded) {
              return ListView.builder(
                itemCount: state.destinations.length,
                itemBuilder: (context, index) {
                  final destination = state.destinations[index];
                  return ListTile(
                    title: Text(destination.displayName),
                    subtitle: Text(destination.displayLocation),
                    trailing: Text(destination.formattedPrice),
                  );
                },
              );
            } else if (state is DestinationsError) {
              return Text('Error: ${state.message}');
            }
            return Container();
          },
        ),
      ),
    );
  }
}
```

### 3. Filtering Destinations

```dart
// Load destinations with filters
context.read<DestinationsCubit>().loadDestinations(
  countryId: 1,
  priceFrom: 100,
  priceTo: 500,
  limit: 10,
);

// Refresh destinations
context.read<DestinationsCubit>().refreshDestinations(
  countryId: 2,
);

// Load more destinations (pagination)
context.read<DestinationsCubit>().loadMoreDestinations();
```

### 4. Accessing Destination Properties

```dart
// Basic info
final name = destination.displayName;
final info = destination.displayInfo;
final location = destination.displayLocation;

// Pricing
final price = destination.formattedPrice; // "235 ريال/يوم"

// Status
final isActive = destination.isActive;

// Images
final hasImage = destination.hasImage;
final imageUrl = destination.imageUrl;
final albumUrls = destination.albumUrls;

// Translations
final arabicName = destination.getTranslatedName('ar');
final englishInfo = destination.getTranslatedInfo('en');
final frenchLocation = destination.getTranslatedLocation('fr');
```

## 🎨 Responsive Design

The widget includes full responsive support:

```dart
// Responsive card width
width: isTablet ? 180.w(context) : 160.w(context)

// Responsive spacing
ResponsiveHelper.getSpacing(context, type: 'medium').verticalSpaceResponsive(context)

// Responsive padding
.paddingSymmetric(
  horizontal: ResponsiveHelper.isTablet(context) ? 32 : 20,
  context: context,
)
```

## 🔧 Dependency Injection

Already configured in `injection_container.dart`:

```dart
// Destinations Data Sources
sl.registerLazySingleton<DestinationsRemoteDataSource>(
  () => DestinationsRemoteDataSourceImpl(dio: sl()),
);

// Destinations Repositories
sl.registerLazySingleton<DestinationsRepository>(
  () => DestinationsRepositoryImpl(remoteDataSource: sl(), networkInfo: sl()),
);

// Destinations Use Cases
sl.registerLazySingleton(() => GetDestinations(sl()));
sl.registerLazySingleton(() => GetDestinationDetails(sl()));

// Destinations Cubits
sl.registerFactory(() => DestinationsCubit(
  getDestinationsUseCase: sl(),
  getDestinationDetailsUseCase: sl(),
));
```

## 🚀 Features

- ✅ **Dynamic API Integration**: Real-time data from server
- ✅ **Responsive Design**: Optimized for all screen sizes
- ✅ **Loading States**: Shimmer loading animation
- ✅ **Error Handling**: Comprehensive error states
- ✅ **Empty States**: User-friendly empty state
- ✅ **Image Handling**: Network images with fallbacks
- ✅ **Translations**: Multi-language support
- ✅ **Filtering**: Country and price filtering
- ✅ **Pagination**: Load more functionality
- ✅ **Interactive**: Tap handling with feedback

## 🔧 Troubleshooting

### Common Issues

#### 1. GetIt Registration Error
```
Error: GetIt: Object/factory with type Dio is not registered
```
**Solution**: Make sure `Dio` is registered in `injection_container.dart`:
```dart
sl.registerLazySingleton<Dio>(() => DioHelper.dio);
```

#### 2. Network Connection Issues
- Check if the API endpoint is accessible
- Verify internet connection
- Check if the server is running

#### 3. API Response Format
- Ensure the API returns data in the expected format
- Check the `status` field in the response
- Verify the `data` array structure

### Testing the API

You can test the API using the test file:
```bash
dart lib/test_destinations_api.dart
```

Or use the test page in the app:
```dart
Navigator.push(
  context,
  MaterialPageRoute(
    builder: (context) => DestinationsTestPage(),
  ),
);
```

## 📱 Widget States

### Loading State
- Shows 3 shimmer cards
- Animated loading effect
- Responsive sizing

### Loaded State
- Displays actual destination cards
- Horizontal scrolling list
- Interactive tap handling

### Error State
- Error icon and message
- User-friendly error text
- Retry functionality available

### Empty State
- Empty icon and message
- Informative text
- Clean design

## 🎯 Integration

The `DestinationsSectionWidget` is automatically integrated in the home screen and will:

1. **Auto-load** destinations when the widget is created
2. **Display** up to 5 destinations in a horizontal list
3. **Handle** all loading, error, and empty states
4. **Provide** responsive design for all devices
5. **Show** interactive cards with destination info

## 🔄 Future Enhancements

- [ ] Add destination favorites functionality
- [ ] Implement destination search
- [ ] Add destination categories/filters
- [ ] Include destination reviews and ratings
- [ ] Add booking integration
- [ ] Implement offline caching
- [ ] Add map integration for locations
