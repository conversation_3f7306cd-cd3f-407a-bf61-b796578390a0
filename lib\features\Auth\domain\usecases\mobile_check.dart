// features/Auth/domain/usecases/mobile_check.dart
import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';
import '../../../../core/error/failures.dart';
import '../../../../core/usecases/usecase.dart';
import '../entities/user.dart';
import '../repositories/auth_repository.dart';

class MobileCheck implements UseCase<User, MobileCheckParams> {
  final AuthRepository repository;

  const MobileCheck(this.repository);

  @override
  Future<Either<Failure, User>> call(MobileCheckParams params) async {
    return await repository.mobileCheck(
      code: params.code,
      mobile: params.mobile,
    );
  }
}

class MobileCheckParams extends Equatable {
  final String code;
  final String mobile;

  const MobileCheckParams({
    required this.code,
    required this.mobile,
  });

  @override
  List<Object> get props => [code, mobile];
}
