import 'package:flutter/material.dart';
import 'responsive_extensions.dart';

extension WidgetExtensions on Widget {
  Widget paddingAll(double padding, BuildContext context) => Padding(
    padding: EdgeInsets.all(padding.w(context)),
    child: this,
  );
  
  Widget paddingSymmetric({double? horizontal, double? vertical, required BuildContext context}) => Padding(
    padding: EdgeInsets.symmetric(
      horizontal: horizontal?.w(context) ?? 0,
      vertical: vertical?.h(context) ?? 0,
    ),
    child: this,
  );
  
  Widget center() => Center(child: this);
  
  Widget expanded({int flex = 1}) => Expanded(flex: flex, child: this);
  
  Widget onTap(VoidCallback onTap) => GestureDetector(
    onTap: onTap,
    child: this,
  );
}

extension SizedBoxExtensions on num {
  Widget get verticalSpace => SizedBox(height: toDouble());
  Widget get horizontalSpace => SizedBox(width: toDouble());
  
  Widget verticalSpaceResponsive(BuildContext context) => SizedBox(height: h(context));
  Widget horizontalSpaceResponsive(BuildContext context) => SizedBox(width: w(context));
}