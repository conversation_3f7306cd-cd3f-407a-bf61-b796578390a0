// core/widgets/custom_checkbox.dart
import 'package:flutter/material.dart';
import 'package:flutter/gestures.dart';

class CustomCheckbox extends StatelessWidget {
  final bool value;
  final ValueChanged<bool?>? onChanged;
  final String? title;
  final Widget? titleWidget;
  final List<TextSpan>? richText;
  final Color? activeColor;
  final Color? checkColor;
  final Color? focusColor;
  final Color? hoverColor;
  final MaterialTapTargetSize? materialTapTargetSize;
  final VisualDensity? visualDensity;
  final OutlinedBorder? shape;
  final BorderSide? side;
  final bool tristate;
  final bool autofocus;
  final bool isError;
  final MainAxisAlignment mainAxisAlignment;
  final CrossAxisAlignment crossAxisAlignment;
  final TextStyle? titleStyle;
  final EdgeInsetsGeometry? contentPadding;
  final double? spacing;

  const CustomCheckbox({
    super.key,
    required this.value,
    this.onChanged,
    this.title,
    this.titleWidget,
    this.richText,
    this.activeColor,
    this.checkColor,
    this.focusColor,
    this.hoverColor,
    this.materialTapTargetSize,
    this.visualDensity,
    this.shape,
    this.side,
    this.tristate = false,
    this.autofocus = false,
    this.isError = false,
    this.mainAxisAlignment = MainAxisAlignment.start,
    this.crossAxisAlignment = CrossAxisAlignment.center,
    this.titleStyle,
    this.contentPadding,
    this.spacing = 8.0,
  }) : assert(
         title != null || titleWidget != null || richText != null,
         'Either title, titleWidget, or richText must be provided',
       );

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    Widget checkbox = Checkbox(
      value: value,
      onChanged: onChanged,
      activeColor: activeColor ?? (isError ? Colors.red : Colors.blue),
      checkColor: checkColor,
      focusColor: focusColor,
      hoverColor: hoverColor,
      materialTapTargetSize: materialTapTargetSize,
      visualDensity: visualDensity,
      shape: shape,
      side: side ?? (isError ? const BorderSide(color: Colors.red) : null),
      tristate: tristate,
      autofocus: autofocus,
    );

    Widget titleContent;
    if (titleWidget != null) {
      titleContent = titleWidget!;
    } else if (richText != null) {
      titleContent = RichText(
        text: TextSpan(
          style: titleStyle ?? theme.textTheme.bodyMedium,
          children: richText!,
        ),
      );
    } else {
      titleContent = Text(
        title!,
        style: titleStyle ?? theme.textTheme.bodyMedium,
      );
    }

    return Padding(
      padding: contentPadding ?? EdgeInsets.zero,
      child: Row(
        mainAxisAlignment: mainAxisAlignment,
        crossAxisAlignment: crossAxisAlignment,
        children: [
          checkbox,
          if (spacing != null) SizedBox(width: spacing),
          Expanded(child: titleContent),
        ],
      ),
    );
  }
}

// Helper class for creating clickable text spans
class ClickableTextSpan extends TextSpan {
  ClickableTextSpan({
    required String text,
    required VoidCallback onTap,
    TextStyle? style,
  }) : super(
         text: text,
         style:
             style ??
             const TextStyle(
               color: Colors.blue,
               decoration: TextDecoration.underline,
             ),
         recognizer: TapGestureRecognizer()..onTap = onTap,
       );
}
