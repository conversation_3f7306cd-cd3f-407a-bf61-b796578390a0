// features/guides/presentation/cubit/guides_state.dart
import 'package:equatable/equatable.dart';
import 'package:tripooo_user/features/guides/domain/entities/guide_entity.dart';

abstract class GuidesState extends Equatable {
  const GuidesState();

  @override
  List<Object?> get props => [];
}

class GuidesInitial extends GuidesState {}

class GuidesLoading extends GuidesState {}

class GuidesLoaded extends GuidesState {
  final List<GuideEntity> guides;
  final bool hasReachedMax;
  final int currentPage;

  const GuidesLoaded({
    required this.guides,
    this.hasReachedMax = false,
    this.currentPage = 1,
  });

  GuidesLoaded copyWith({
    List<GuideEntity>? guides,
    bool? hasReachedMax,
    int? currentPage,
  }) {
    return GuidesLoaded(
      guides: guides ?? this.guides,
      hasReachedMax: hasReachedMax ?? this.hasReachedMax,
      currentPage: currentPage ?? this.currentPage,
    );
  }

  @override
  List<Object?> get props => [guides, hasReachedMax, currentPage];
}

class GuidesError extends GuidesState {
  final String message;

  const GuidesError({required this.message});

  @override
  List<Object?> get props => [message];
}

// Featured Guides States
abstract class FeaturedGuidesState extends Equatable {
  const FeaturedGuidesState();

  @override
  List<Object?> get props => [];
}

class FeaturedGuidesInitial extends FeaturedGuidesState {}

class FeaturedGuidesLoading extends FeaturedGuidesState {}

class FeaturedGuidesLoaded extends FeaturedGuidesState {
  final List<GuideEntity> guides;

  const FeaturedGuidesLoaded({required this.guides});

  @override
  List<Object?> get props => [guides];
}

class FeaturedGuidesError extends FeaturedGuidesState {
  final String message;

  const FeaturedGuidesError({required this.message});

  @override
  List<Object?> get props => [message];
}

// Guide Details States
abstract class GuideDetailsState extends Equatable {
  const GuideDetailsState();

  @override
  List<Object?> get props => [];
}

class GuideDetailsInitial extends GuideDetailsState {}

class GuideDetailsLoading extends GuideDetailsState {}

class GuideDetailsLoaded extends GuideDetailsState {
  final GuideEntity guide;

  const GuideDetailsLoaded({required this.guide});

  @override
  List<Object?> get props => [guide];
}

class GuideDetailsError extends GuideDetailsState {
  final String message;

  const GuideDetailsError({required this.message});

  @override
  List<Object?> get props => [message];
}

// State for single guide details (used by FeaturedGuidesCubit)
class FeaturedGuideDetailsLoaded extends FeaturedGuidesState {
  final GuideEntity guide;

  const FeaturedGuideDetailsLoaded({required this.guide});

  @override
  List<Object?> get props => [guide];
}
