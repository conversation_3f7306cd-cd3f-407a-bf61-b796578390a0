// core/theme/app_colors.dart
import 'package:flutter/material.dart';

class AppColors {
  // Primary Colors
  static const Color primary = Color(0xFF3D56B2); // Blue
  static const Color primaryLight = Color(0xFF4DA3FF);
  static const Color primaryDark = Color(0xFF0056CC);

  // Secondary Colors
  static const Color secondary = Color(
    0xFF34C759,
  ); // Green (for phone country code)
  static const Color secondaryLight = Color(0xFF5ED670);
  static const Color secondaryDark = Color(0xFF28A745);

  // Background Colors
  static const Color background = Color(0xFFFFFFFF); // White
  static const Color surface = Color(0xFFFFFFFF); // White
  static const Color surfaceVariant = Color(
    0xFFF5F5F5,
  ); // Light gray for tabs background
  static const Color darkBackground = Color(0xFF121212);
  static const Color darkSurface = Color(0xFF1E1E1E);

  // Text Colors
  static const Color textPrimary = Color(
    0xFF000000,
  ); // Black - as specified in design
  static const Color textSecondary = Color(0xFF666666); // Gray
  static const Color textTertiary = Color(0xFF999999); // Light gray
  static const Color textDisabled = Color(0xFFCCCCCC);

  // White
  static const Color white = Color(0xFFFFFFFF);

  // Border Colors
  static const Color borderColor = Color(0xFFE0E0E0); // Light gray for borders
  static const Color borderColorFocused = primary;

  // State Colors
  static const Color error = Color(0xFFFF3B30); // Red
  static const Color warning = Color(0xFFFF9500); // Orange
  static const Color success = Color(0xFF34C759); // Green
  static const Color info = Color(0xFF007AFF); // Blue

  // Hint and Placeholder Colors
  static const Color hintText = Color(0xFF999999);
  static const Color placeholderText = Color(0xFFCCCCCC);

  // Divider
  static const Color divider = Color(0xFFE0E0E0);

  // Shadow
  static const Color shadow = Color(0x1A000000); // 10% black

  // Overlay
  static const Color overlay = Color(0x80000000); // 50% black

  // Gradient Colors
  static const List<Color> primaryGradient = [
    Color(0xFF007AFF),
    Color(0xFF4DA3FF),
  ];

  static const List<Color> secondaryGradient = [
    Color(0xFF34C759),
    Color(0xFF5ED670),
  ];

  // Social Media Colors
  static const Color facebook = Color(0xFF1877F2);
  static const Color google = Color(0xFFDB4437);
  static const Color apple = Color(0xFF000000);

  // Status Colors
  static const Color online = Color(0xFF34C759);
  static const Color offline = Color(0xFF999999);
  static const Color away = Color(0xFFFF9500);
  static const Color busy = Color(0xFFFF3B30);

  // Rating Colors
  static const Color ratingGold = Color(0xFFFFD700);
  static const Color ratingSilver = Color(0xFFC0C0C0);
  static const Color ratingBronze = Color(0xFFCD7F32);
}
