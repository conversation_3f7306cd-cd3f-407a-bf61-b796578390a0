// features/Auth/data/models/auth_request_models.dart
import 'dart:io';
import '../../../../core/utils/typedef.dart';

class LoginRequest {
  final String mobile;
  final String password;
  final String? fcmToken;

  const LoginRequest({
    required this.mobile,
    required this.password,
    this.fcmToken,
  });

  DataMap toMap() {
    return {
      'mobile': mobile,
      'password': password,
      if (fcmToken != null) 'fcm_token': fcmToken,
    };
  }
}

class RegisterRequest {
  final String name;
  final String email;
  final String mobile;
  final String password;
  final String passwordConfirmation;
  final int userType; // 1 for client, 2 for guide
  final File? file;
  final String? fcmToken;
  
  // Guide-specific fields
  final double? dayPrice;
  final double? hourPrice;
  final String? info;
  final String? experience;
  final List<String>? interests;
  final List<String>? toursType;
  final List<int>? languages;
  
  // Branch fields (optional)
  final String? branchName;
  final String? branchEmail;
  final List<String>? branchPayments;

  const RegisterRequest({
    required this.name,
    required this.email,
    required this.mobile,
    required this.password,
    required this.passwordConfirmation,
    required this.userType,
    this.file,
    this.fcmToken,
    this.dayPrice,
    this.hourPrice,
    this.info,
    this.experience,
    this.interests,
    this.toursType,
    this.languages,
    this.branchName,
    this.branchEmail,
    this.branchPayments,
  });

  DataMap toMap() {
    final map = <String, dynamic>{
      'name': name,
      'email': email,
      'mobile': mobile,
      'password': password,
      'password_confirmation': passwordConfirmation,
      'user_type': userType,
      if (fcmToken != null) 'fcm_token': fcmToken,
    };

    // Add guide-specific fields if user is a guide
    if (userType == 2) {
      if (dayPrice != null) map['day_price'] = dayPrice;
      if (hourPrice != null) map['hour_price'] = hourPrice;
      if (info != null) map['info'] = info;
      if (experience != null) map['experience'] = experience;
      if (interests != null) {
        for (int i = 0; i < interests!.length; i++) {
          map['interests[$i]'] = interests![i];
        }
      }
      if (toursType != null) {
        for (int i = 0; i < toursType!.length; i++) {
          map['tours_type[$i]'] = toursType![i];
        }
      }
      if (languages != null) {
        for (int i = 0; i < languages!.length; i++) {
          map['languages[$i]'] = languages![i];
        }
      }
    }

    // Add branch fields if provided
    if (branchName != null) map['branch[name]'] = branchName;
    if (branchEmail != null) map['branch[email]'] = branchEmail;
    if (branchPayments != null) {
      for (int i = 0; i < branchPayments!.length; i++) {
        map['branch[payments][$i]'] = branchPayments![i];
      }
    }

    return map;
  }
}

class MobileCheckRequest {
  final String code;
  final String mobile;

  const MobileCheckRequest({
    required this.code,
    required this.mobile,
  });

  DataMap toMap() {
    return {
      'code': code,
      'mobile': mobile,
    };
  }
}

class RegenerateCodeRequest {
  final String mobile;

  const RegenerateCodeRequest({required this.mobile});

  DataMap toMap() {
    return {'mobile': mobile};
  }
}

class UpdateUserRequest {
  final String? name;
  final String? email;
  final File? file;
  final double? dayPrice;
  final double? hourPrice;
  final String? info;
  final String? experience;
  final List<String>? interests;
  final List<String>? toursType;
  final List<int>? languages;

  const UpdateUserRequest({
    this.name,
    this.email,
    this.file,
    this.dayPrice,
    this.hourPrice,
    this.info,
    this.experience,
    this.interests,
    this.toursType,
    this.languages,
  });

  DataMap toMap() {
    final map = <String, dynamic>{};
    
    if (name != null) map['name'] = name;
    if (email != null) map['email'] = email;
    if (dayPrice != null) map['day_price'] = dayPrice;
    if (hourPrice != null) map['hour_price'] = hourPrice;
    if (info != null) map['info'] = info;
    if (experience != null) map['experience'] = experience;
    
    if (interests != null) {
      for (int i = 0; i < interests!.length; i++) {
        map['interests[$i]'] = interests![i];
      }
    }
    
    if (toursType != null) {
      for (int i = 0; i < toursType!.length; i++) {
        map['tours_type[$i]'] = toursType![i];
      }
    }
    
    if (languages != null) {
      for (int i = 0; i < languages!.length; i++) {
        map['languages[$i]'] = languages![i];
      }
    }

    return map;
  }
}

class ChangePasswordRequest {
  final String newPassword;
  final String newPasswordConfirmation;
  final String oldPassword;

  const ChangePasswordRequest({
    required this.newPassword,
    required this.newPasswordConfirmation,
    required this.oldPassword,
  });

  DataMap toMap() {
    return {
      'new_password': newPassword,
      'new_password_confirmation': newPasswordConfirmation,
      'old_password': oldPassword,
    };
  }
}

class ChangeMobileRequest {
  final String mobile;

  const ChangeMobileRequest({required this.mobile});

  DataMap toMap() {
    return {'mobile': mobile};
  }
}

class SendResetCodeRequest {
  final String mobile;

  const SendResetCodeRequest({required this.mobile});

  DataMap toMap() {
    return {'mobile': mobile};
  }
}

class VerifyResetCodeRequest {
  final String mobile;
  final String code;

  const VerifyResetCodeRequest({
    required this.mobile,
    required this.code,
  });

  DataMap toMap() {
    return {
      'mobile': mobile,
      'code': code,
    };
  }
}

class ResetPasswordRequest {
  final String mobile;
  final String code;
  final String password;
  final String passwordConfirmation;

  const ResetPasswordRequest({
    required this.mobile,
    required this.code,
    required this.password,
    required this.passwordConfirmation,
  });

  DataMap toMap() {
    return {
      'mobile': mobile,
      'code': code,
      'password': password,
      'password_confirmation': passwordConfirmation,
    };
  }
}
