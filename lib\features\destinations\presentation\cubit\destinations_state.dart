// features/destinations/presentation/cubit/destinations_state.dart
part of 'destinations_cubit.dart';

abstract class DestinationsState extends Equatable {
  const DestinationsState();

  @override
  List<Object?> get props => [];
}

class DestinationsInitial extends DestinationsState {}

class DestinationsLoading extends DestinationsState {}

class DestinationsLoaded extends DestinationsState {
  final List<DestinationEntity> destinations;

  const DestinationsLoaded({required this.destinations});

  @override
  List<Object?> get props => [destinations];
}

class DestinationsError extends DestinationsState {
  final String message;
  final int statusCode;

  const DestinationsError({required this.message, required this.statusCode});

  @override
  List<Object?> get props => [message, statusCode];
}

class DestinationDetailsLoading extends DestinationsState {}

class DestinationDetailsLoaded extends DestinationsState {
  final DestinationEntity destination;

  const DestinationDetailsLoaded({required this.destination});

  @override
  List<Object?> get props => [destination];
}

class DestinationDetailsError extends DestinationsState {
  final String message;
  final int statusCode;

  const DestinationDetailsError({
    required this.message,
    required this.statusCode,
  });

  @override
  List<Object?> get props => [message, statusCode];
}
