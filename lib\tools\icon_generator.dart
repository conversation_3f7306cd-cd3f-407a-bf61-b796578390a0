// lib/tools/icon_generator.dart
import 'dart:io';
import 'dart:typed_data';
import 'dart:ui' as ui;
import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';

/// Tool for generating temporary app icons and splash logos
/// Run this as a standalone Flutter app to generate placeholder icons
class IconGenerator {
  static const int appIconSize = 1024;
  static const int splashLogoSize = 512;
  
  /// Generate app icon
  static Future<void> generateAppIcon() async {
    final recorder = ui.PictureRecorder();
    final canvas = Canvas(recorder);
    final size = Size(appIconSize.toDouble(), appIconSize.toDouble());
    
    // Background gradient
    final gradient = LinearGradient(
      begin: Alignment.topLeft,
      end: Alignment.bottomRight,
      colors: [
        Color(0xFF2196F3), // Primary blue
        Color(0xFF1976D2), // Darker blue
      ],
    );
    
    final paint = Paint()
      ..shader = gradient.createShader(Rect.fromLTWH(0, 0, size.width, size.height));
    
    canvas.drawRect(Rect.fromLTWH(0, 0, size.width, size.height), paint);
    
    // Draw travel pin
    _drawTravelPin(canvas, size);
    
    final picture = recorder.endRecording();
    final img = await picture.toImage(appIconSize, appIconSize);
    final byteData = await img.toByteData(format: ui.ImageByteFormat.png);
    
    if (byteData != null) {
      await _saveImage(byteData.buffer.asUint8List(), 'assets/icons/app_icon.png');
      print('✅ App icon generated: assets/icons/app_icon.png');
    }
  }
  
  /// Generate splash logo
  static Future<void> generateSplashLogo({bool darkMode = false}) async {
    final recorder = ui.PictureRecorder();
    final canvas = Canvas(recorder);
    final size = Size(splashLogoSize.toDouble(), splashLogoSize.toDouble());
    
    // Transparent background
    canvas.drawRect(
      Rect.fromLTWH(0, 0, size.width, size.height),
      Paint()..color = Colors.transparent,
    );
    
    // Draw compass design
    _drawCompass(canvas, size, darkMode);
    
    final picture = recorder.endRecording();
    final img = await picture.toImage(splashLogoSize, splashLogoSize);
    final byteData = await img.toByteData(format: ui.ImageByteFormat.png);
    
    if (byteData != null) {
      final filename = darkMode ? 'splash_logo_dark.png' : 'splash_logo.png';
      await _saveImage(byteData.buffer.asUint8List(), 'assets/icons/$filename');
      print('✅ Splash logo generated: assets/icons/$filename');
    }
  }
  
  /// Draw travel pin on canvas
  static void _drawTravelPin(Canvas canvas, Size size) {
    final center = Offset(size.width / 2, size.height / 2 - 50);
    final pinRadius = 120.0;
    
    // Pin circle
    final circlePaint = Paint()
      ..color = Color(0xFFFF9800) // Orange
      ..style = PaintingStyle.fill;
    
    canvas.drawCircle(center, pinRadius, circlePaint);
    
    // Pin point
    final pointPaint = Paint()
      ..color = Color(0xFFFF9800)
      ..style = PaintingStyle.fill;
    
    final pointPath = Path();
    pointPath.moveTo(center.dx, center.dy + pinRadius + 60);
    pointPath.lineTo(center.dx - 30, center.dy + pinRadius);
    pointPath.lineTo(center.dx + 30, center.dy + pinRadius);
    pointPath.close();
    
    canvas.drawPath(pointPath, pointPaint);
    
    // Inner white circle
    final innerPaint = Paint()
      ..color = Colors.white
      ..style = PaintingStyle.fill;
    
    canvas.drawCircle(center, pinRadius * 0.6, innerPaint);
    
    // "T" letter
    final textPainter = TextPainter(
      text: TextSpan(
        text: 'T',
        style: TextStyle(
          color: Color(0xFF2196F3),
          fontSize: 80,
          fontWeight: FontWeight.bold,
        ),
      ),
      textDirection: TextDirection.ltr,
    );
    
    textPainter.layout();
    textPainter.paint(
      canvas,
      Offset(
        center.dx - textPainter.width / 2,
        center.dy - textPainter.height / 2,
      ),
    );
  }
  
  /// Draw compass design on canvas
  static void _drawCompass(Canvas canvas, Size size, bool darkMode) {
    final center = Offset(size.width / 2, size.height / 2);
    final radius = 160.0;
    
    final primaryColor = darkMode ? Colors.white : Color(0xFF2196F3);
    final secondaryColor = Color(0xFFFF9800);
    
    // Outer circle
    final outerPaint = Paint()
      ..color = primaryColor
      ..style = PaintingStyle.stroke
      ..strokeWidth = 6;
    
    canvas.drawCircle(center, radius, outerPaint);
    
    // Inner circle
    final innerPaint = Paint()
      ..color = secondaryColor
      ..style = PaintingStyle.fill;
    
    canvas.drawCircle(center, 40, innerPaint);
    
    // Compass points
    _drawCompassPoints(canvas, center, radius, primaryColor, secondaryColor);
    
    // "Tripooo" text
    final textPainter = TextPainter(
      text: TextSpan(
        text: 'Tripooo',
        style: TextStyle(
          color: primaryColor,
          fontSize: 36,
          fontWeight: FontWeight.bold,
        ),
      ),
      textDirection: TextDirection.ltr,
    );
    
    textPainter.layout();
    textPainter.paint(
      canvas,
      Offset(
        center.dx - textPainter.width / 2,
        center.dy + radius + 20,
      ),
    );
  }
  
  /// Draw compass points
  static void _drawCompassPoints(
    Canvas canvas,
    Offset center,
    double radius,
    Color primaryColor,
    Color secondaryColor,
  ) {
    final pointPaint1 = Paint()..color = primaryColor;
    final pointPaint2 = Paint()..color = secondaryColor;
    
    // North point
    final northPath = Path();
    northPath.moveTo(center.dx, center.dy - radius);
    northPath.lineTo(center.dx - 12, center.dy - radius + 30);
    northPath.lineTo(center.dx + 12, center.dy - radius + 30);
    northPath.close();
    canvas.drawPath(northPath, pointPaint1);
    
    // South point
    final southPath = Path();
    southPath.moveTo(center.dx, center.dy + radius);
    southPath.lineTo(center.dx - 12, center.dy + radius - 30);
    southPath.lineTo(center.dx + 12, center.dy + radius - 30);
    southPath.close();
    canvas.drawPath(southPath, pointPaint2);
    
    // East point
    final eastPath = Path();
    eastPath.moveTo(center.dx + radius, center.dy);
    eastPath.lineTo(center.dx + radius - 30, center.dy - 12);
    eastPath.lineTo(center.dx + radius - 30, center.dy + 12);
    eastPath.close();
    canvas.drawPath(eastPath, pointPaint1);
    
    // West point
    final westPath = Path();
    westPath.moveTo(center.dx - radius, center.dy);
    westPath.lineTo(center.dx - radius + 30, center.dy - 12);
    westPath.lineTo(center.dx - radius + 30, center.dy + 12);
    westPath.close();
    canvas.drawPath(westPath, pointPaint2);
  }
  
  /// Save image to file
  static Future<void> _saveImage(Uint8List bytes, String path) async {
    final file = File(path);
    await file.create(recursive: true);
    await file.writeAsBytes(bytes);
  }
  
  /// Generate all icons
  static Future<void> generateAllIcons() async {
    print('🎨 Generating temporary icons for Tripooo app...');
    
    // Create directory
    final directory = Directory('assets/icons');
    if (!await directory.exists()) {
      await directory.create(recursive: true);
    }
    
    // Generate all icons
    await generateAppIcon();
    await generateSplashLogo(darkMode: false);
    await generateSplashLogo(darkMode: true);
    
    print('\n🎉 All temporary icons generated successfully!');
    print('\n📋 Next steps:');
    print('1. Run: flutter pub get');
    print('2. Run: flutter pub run flutter_launcher_icons:main');
    print('3. Run: flutter pub run flutter_native_splash:create');
    print('\n💡 Replace the temporary icons with your actual designs when ready.');
  }
}

/// Simple app to run the icon generator
class IconGeneratorApp extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      home: Scaffold(
        appBar: AppBar(title: Text('Icon Generator')),
        body: Center(
          child: ElevatedButton(
            onPressed: () async {
              await IconGenerator.generateAllIcons();
            },
            child: Text('Generate Icons'),
          ),
        ),
      ),
    );
  }
}

void main() {
  runApp(IconGeneratorApp());
}
