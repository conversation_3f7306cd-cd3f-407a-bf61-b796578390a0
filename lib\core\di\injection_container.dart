// core/di/injection_container.dart
import 'package:get_it/get_it.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:dio/dio.dart';

// Core
import '../network/dio_helper.dart';

// Auth Feature
import '../../features/Auth/data/datasources/auth_remote_data_source.dart';
import '../../features/Auth/data/datasources/auth_remote_data_source_impl.dart';
import '../../features/Auth/data/datasources/auth_local_data_source.dart';
import '../../features/Auth/data/repositories/auth_repository_impl.dart';
import '../../features/Auth/data/services/local_auth_service.dart';
import '../../features/Auth/domain/repositories/auth_repository.dart';
import '../../features/Auth/domain/usecases/login.dart';
import '../../features/Auth/domain/usecases/register.dart';
import '../../features/Auth/domain/usecases/logout.dart';
import '../../features/Auth/domain/usecases/get_user.dart';
import '../../features/Auth/domain/usecases/check_token.dart';
import '../../features/Auth/domain/usecases/mobile_check.dart';
import '../../features/Auth/domain/usecases/regenerate_code.dart';
import '../../features/Auth/domain/usecases/update_user.dart';
import '../../features/Auth/domain/usecases/change_password.dart';
import '../../features/Auth/domain/usecases/change_mobile_number.dart';
import '../../features/Auth/domain/usecases/send_reset_code.dart';
import '../../features/Auth/domain/usecases/verify_reset_code.dart';
import '../../features/Auth/domain/usecases/reset_password.dart';
import '../../features/Auth/domain/usecases/social_auth.dart';
import '../../features/Auth/data/services/social_auth_service.dart';
import '../../features/Auth/presentation/cubit/auth_cubit.dart';

// Guides Feature
import '../../features/guides/data/datasources/guides_remote_data_source.dart';
import '../../features/guides/data/repositories/guides_repository_impl.dart';
import '../../features/guides/domain/repositories/guides_repository.dart';
import '../../features/guides/domain/usecases/get_guides_usecase.dart';
import '../../features/guides/domain/usecases/get_featured_guides_usecase.dart';
import '../../features/guides/domain/usecases/get_guide_by_id_usecase.dart';
import '../../features/guides/presentation/cubit/guides_cubit.dart';

// Guide Details Feature
import '../../features/guides/data/datasources/guide_details_remote_data_source.dart';
import '../../features/guides/data/repositories/guide_details_repository_impl.dart';
import '../../features/guides/domain/repositories/guide_details_repository.dart';
import '../../features/guides/domain/usecases/get_guide_details.dart';

// Destinations Feature
import '../../features/destinations/data/datasources/destinations_remote_data_source.dart';
import '../../features/destinations/data/repositories/destinations_repository_impl.dart';
import '../../features/destinations/domain/repositories/destinations_repository.dart';
import '../../features/destinations/domain/usecases/get_destinations.dart';
import '../../features/destinations/domain/usecases/get_destination_details.dart';
import '../../features/destinations/presentation/cubit/destinations_cubit.dart';
import '../../features/guides/presentation/cubit/featured_guides_cubit.dart';
import '../../features/guides/presentation/cubit/guide_details_cubit.dart';

// Network
import '../network/network_info.dart';

final sl = GetIt.instance;

Future<void> init() async {
  // Initialize Dio
  DioHelper.init();

  // Register Dio instance
  sl.registerLazySingleton<Dio>(() => DioHelper.dio);

  // External dependencies
  final sharedPreferences = await SharedPreferences.getInstance();
  sl.registerLazySingleton(() => sharedPreferences);
  sl.registerLazySingleton(() => const FlutterSecureStorage());

  // Network
  sl.registerLazySingleton<NetworkInfo>(() => NetworkInfoImpl());

  // Core services
  sl.registerLazySingleton(() => LocalAuthService());
  sl.registerLazySingleton<SocialAuthService>(() => SocialAuthServiceImpl());

  // Data sources
  sl.registerLazySingleton<AuthRemoteDataSource>(
    () => AuthRemoteDataSourceImpl(),
  );

  sl.registerLazySingleton<AuthLocalDataSource>(
    () => AuthLocalDataSourceImpl(sharedPreferences: sl(), secureStorage: sl()),
  );

  // Repository
  sl.registerLazySingleton<AuthRepository>(
    () => AuthRepositoryImpl(remoteDataSource: sl(), localDataSource: sl()),
  );

  // Use cases
  sl.registerLazySingleton(() => Login(sl()));
  sl.registerLazySingleton(() => Register(sl()));
  sl.registerLazySingleton(() => Logout(sl()));
  sl.registerLazySingleton(() => GetUser(sl()));
  sl.registerLazySingleton(() => CheckToken(sl()));
  sl.registerLazySingleton(() => MobileCheck(sl()));
  sl.registerLazySingleton(() => RegenerateCode(sl()));
  sl.registerLazySingleton(() => UpdateUser(sl()));
  sl.registerLazySingleton(() => ChangePassword(sl()));
  sl.registerLazySingleton(() => ChangeMobileNumber(sl()));
  sl.registerLazySingleton(() => SendResetCode(sl()));
  sl.registerLazySingleton(() => VerifyResetCode(sl()));
  sl.registerLazySingleton(() => ResetPassword(sl()));
  sl.registerLazySingleton(() => SocialAuth(sl()));

  // Auth Cubit
  sl.registerFactory(
    () => AuthCubit(
      localAuthService: sl(),
      login: sl(),
      register: sl(),
      logout: sl(),
      getUser: sl(),
      checkToken: sl(),
      mobileCheck: sl(),
      regenerateCode: sl(),
      updateUser: sl(),
      changePassword: sl(),
      changeMobileNumber: sl(),
      sendResetCode: sl(),
      verifyResetCode: sl(),
      resetPassword: sl(),
      socialAuth: sl(),
      socialAuthService: sl(),
    ),
  );

  // Guides Data Sources
  sl.registerLazySingleton<GuidesRemoteDataSource>(
    () => GuidesRemoteDataSourceImpl(dio: DioHelper.dio),
  );

  // Guides Repositories
  sl.registerLazySingleton<GuidesRepository>(
    () => GuidesRepositoryImpl(remoteDataSource: sl(), networkInfo: sl()),
  );

  // Guide Details Data Sources
  sl.registerLazySingleton<GuideDetailsRemoteDataSource>(
    () => GuideDetailsRemoteDataSourceImpl(dio: sl()),
  );

  // Guide Details Repositories
  sl.registerLazySingleton<GuideDetailsRepository>(
    () => GuideDetailsRepositoryImpl(remoteDataSource: sl(), networkInfo: sl()),
  );

  // Guides Use Cases
  sl.registerLazySingleton(() => GetGuidesUseCase(sl()));
  sl.registerLazySingleton(() => GetFeaturedGuidesUseCase(sl()));
  sl.registerLazySingleton(() => GetGuideByIdUseCase(sl()));
  sl.registerLazySingleton(() => GetGuideDetails(sl()));

  // Destinations Data Sources
  sl.registerLazySingleton<DestinationsRemoteDataSource>(
    () => DestinationsRemoteDataSourceImpl(dio: sl()),
  );

  // Destinations Repositories
  sl.registerLazySingleton<DestinationsRepository>(
    () => DestinationsRepositoryImpl(remoteDataSource: sl(), networkInfo: sl()),
  );

  // Destinations Use Cases
  sl.registerLazySingleton(() => GetDestinations(sl()));
  sl.registerLazySingleton(() => GetDestinationDetails(sl()));

  // Guides Cubits
  sl.registerFactory(() => GuidesCubit(getGuidesUseCase: sl()));
  sl.registerFactory(() => FeaturedGuidesCubit(getFeaturedGuidesUseCase: sl()));
  sl.registerFactory(() => GuideDetailsCubit(getGuideDetailsUseCase: sl()));

  // Destinations Cubits
  sl.registerFactory(
    () => DestinationsCubit(
      getDestinationsUseCase: sl(),
      getDestinationDetailsUseCase: sl(),
    ),
  );
}
