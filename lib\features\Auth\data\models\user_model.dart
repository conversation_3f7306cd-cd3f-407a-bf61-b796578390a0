// features/Auth/data/models/user_model.dart
import 'package:tripooo_user/features/Auth/data/models/language_model.dart';

import '../../domain/entities/user.dart';
import '../../../../core/utils/typedef.dart';

class UserModel extends User {
  const UserModel({
    required super.id,
    required super.name,
    super.fatherName,
    required super.email,
    super.img,
    required super.mobile,
    super.mobileNumber,
    required super.userType,
    super.gender,
    super.dayPrice,
    super.hourPrice,
    super.info,
    super.experience,
    super.interests,
    super.toursType,
    super.languages,
    super.fcmToken,
    super.city,
    super.birthPlace,
    super.qualification,
    super.createdAt,
    super.updatedAt,
    super.token,
  });

  factory UserModel.fromMap(DataMap map) {
    return UserModel(
      id: map['id'] ?? 0,
      name: map['name'] ?? '',
      fatherName: map['father_name'],
      email: map['email'] ?? '',
      img: map['img'],
      mobile: map['mobile'] ?? map['mobile_number'] ?? '',
      mobileNumber: map['mobile_number'],
      userType: map['user_type'] ?? 1,
      gender: map['gender'],
      dayPrice: map['day_price']?.toDouble(),
      hourPrice: map['hour_price']?.toDouble(),
      info: map['info'],
      experience: map['experience'],
      interests: map['interests'] != null
          ? List<String>.from(map['interests'])
          : null,
      toursType: map['tours_type'] != null
          ? List<String>.from(map['tours_type'])
          : null,
      languages: map['languages'] != null
          ? List<LanguageModel>.from(
              map['languages'].map((lang) => LanguageModel.fromMap(lang)),
            )
          : null,

      fcmToken: map['fcm_token'],
      city: map['city'],
      birthPlace: map['birth_place'],
      qualification: map['qualification'],
      createdAt: map['created_at'] != null
          ? DateTime.tryParse(map['created_at'])
          : null,
      updatedAt: map['updated_at'] != null
          ? DateTime.tryParse(map['updated_at'])
          : null,
      token: map['token'],
    );
  }

  DataMap toMap() {
    return {
      'id': id,
      'name': name,
      'father_name': fatherName,
      'email': email,
      'img': img,
      'mobile': mobile,
      'mobile_number': mobileNumber,
      'user_type': userType,
      'gender': gender,
      'day_price': dayPrice,
      'hour_price': hourPrice,
      'info': info,
      'experience': experience,
      'interests': interests,
      'tours_type': toursType,
      'languages': languages?.map((lang) => lang.toMap()).toList(),
      'fcm_token': fcmToken,
      'city': city,
      'birth_place': birthPlace,
      'qualification': qualification,
      'created_at': createdAt?.toIso8601String(),
      'updated_at': updatedAt?.toIso8601String(),
      'token': token,
    };
  }

  @override
  UserModel copyWith({
    int? id,
    String? name,
    String? fatherName,
    String? email,
    String? img,
    String? mobile,
    String? mobileNumber,
    int? userType,
    String? gender,
    double? dayPrice,
    double? hourPrice,
    String? info,
    String? experience,
    List<String>? interests,
    List<String>? toursType,
    List<LanguageModel>? languages,
    String? fcmToken,
    String? city,
    String? birthPlace,
    String? qualification,
    DateTime? createdAt,
    DateTime? updatedAt,
    String? token,
  }) {
    return UserModel(
      id: id ?? this.id,
      name: name ?? this.name,
      fatherName: fatherName ?? this.fatherName,
      email: email ?? this.email,
      img: img ?? this.img,
      mobile: mobile ?? this.mobile,
      mobileNumber: mobileNumber ?? this.mobileNumber,
      userType: userType ?? this.userType,
      gender: gender ?? this.gender,
      dayPrice: dayPrice ?? this.dayPrice,
      hourPrice: hourPrice ?? this.hourPrice,
      info: info ?? this.info,
      experience: experience ?? this.experience,
      interests: interests ?? this.interests,
      toursType: toursType ?? this.toursType,
      languages: languages ?? this.languages,
      fcmToken: fcmToken ?? this.fcmToken,
      city: city ?? this.city,
      birthPlace: birthPlace ?? this.birthPlace,
      qualification: qualification ?? this.qualification,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      token: token ?? this.token,
    );
  }
}
