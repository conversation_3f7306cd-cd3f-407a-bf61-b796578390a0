// features/Auth/domain/usecases/update_user.dart
import 'dart:io';
import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';
import '../../../../core/error/failures.dart';
import '../../../../core/usecases/usecase.dart';
import '../entities/user.dart';
import '../repositories/auth_repository.dart';

class UpdateUser implements UseCase<User, UpdateUserParams> {
  final AuthRepository repository;

  const UpdateUser(this.repository);

  @override
  Future<Either<Failure, User>> call(UpdateUserParams params) async {
    return await repository.updateUser(
      name: params.name,
      email: params.email,
      file: params.file,
      dayPrice: params.dayPrice,
      hourPrice: params.hourPrice,
      info: params.info,
      experience: params.experience,
      interests: params.interests,
      toursType: params.toursType,
      languages: params.languages,
    );
  }
}

class UpdateUserParams extends Equatable {
  final String? name;
  final String? email;
  final File? file;
  final double? dayPrice;
  final double? hourPrice;
  final String? info;
  final String? experience;
  final List<String>? interests;
  final List<String>? toursType;
  final List<int>? languages;

  const UpdateUserParams({
    this.name,
    this.email,
    this.file,
    this.dayPrice,
    this.hourPrice,
    this.info,
    this.experience,
    this.interests,
    this.toursType,
    this.languages,
  });

  @override
  List<Object?> get props => [
        name,
        email,
        file,
        dayPrice,
        hourPrice,
        info,
        experience,
        interests,
        toursType,
        languages,
      ];
}
