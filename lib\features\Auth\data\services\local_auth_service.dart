// features/Auth/data/services/local_auth_service.dart

import 'package:flutter/services.dart';
import 'package:local_auth/local_auth.dart';
import 'package:local_auth/error_codes.dart' as auth_error;
import 'package:dartz/dartz.dart';

class LocalAuthService {
  final LocalAuthentication _localAuth = LocalAuthentication();

  Future<Either<String, bool>> authenticateWithBiometrics() async {
    try {
      // Check if device supports biometrics
      final bool isAvailable = await _localAuth.canCheckBiometrics;
      if (!isAvailable) {
        return Left('Biometric authentication is not available on this device');
      }

      // Get available biometric types
      final List<BiometricType> availableBiometrics = await _localAuth
          .getAvailableBiometrics();

      if (availableBiometrics.isEmpty) {
        return Left('No biometric authentication methods are set up');
      }

      // Authenticate
      final bool didAuthenticate = await _localAuth.authenticate(
        localizedReason: 'Please authenticate to login',
        options: const AuthenticationOptions(
          biometricOnly: true,
          stickyAuth: true,
        ),
      );

      return Right(didAuthenticate);
    } catch (e) {
      print(e.toString());
      return Left(_handleAuthError(e));
    }
  }

  Future<Either<String, List<BiometricType>>> getAvailableBiometrics() async {
    try {
      final bool isAvailable = await _localAuth.canCheckBiometrics;
      if (!isAvailable) {
        return Left('Biometric authentication is not available');
      }

      final List<BiometricType> availableBiometrics = await _localAuth
          .getAvailableBiometrics();

      return Right(availableBiometrics);
    } catch (e) {
      print(e.toString());
      return Left('Failed to get available biometrics: $e');
    }
  }

  Future<bool> isDeviceSupported() async {
    try {
      return await _localAuth.isDeviceSupported();
    } catch (e) {
      return false;
    }
  }

  String _handleAuthError(dynamic error) {
    if (error is PlatformException) {
      switch (error.code) {
        case auth_error.notAvailable:
          print('Biometric authentication is not available');
          return 'Biometric authentication is not available';
        case auth_error.notEnrolled:
          print('No biometric credentials are enrolled');
          return 'No biometric credentials are enrolled';
        case auth_error.lockedOut:
          print('Biometric authentication is locked out');
          return 'Too many failed attempts. Try again later';
        case auth_error.permanentlyLockedOut:
          print('Biometric authentication is permanently locked');
          return 'Biometric authentication is permanently locked';
        default:
          print('Authentication failed: ${error.message}');
          return 'Authentication failed: ${error.message}';
      }
    }
    return 'An unexpected error occurred during authentication';
  }
}
