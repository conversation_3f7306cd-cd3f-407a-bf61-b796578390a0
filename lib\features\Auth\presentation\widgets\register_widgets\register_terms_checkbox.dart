// lib/features/Auth/presentation/widgets/register_terms_checkbox.dart
import 'package:flutter/material.dart';
import 'package:easy_localization/easy_localization.dart';

class RegisterTermsCheckbox extends StatelessWidget {
  final bool value;
  final ValueChanged<bool?> onChanged;

  const RegisterTermsCheckbox({
    super.key,
    required this.value,
    required this.onChanged,
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        Checkbox(
          value: value,
          onChanged: onChanged,
          activeColor: Colors.blue,
        ),
        Expanded(
          child: Wrap(
            children: [
              Text(
                'agree_to'.tr(),
                style: const TextStyle(fontSize: 14),
              ),
              const SizedBox(width: 4),
              GestureDetector(
                onTap: () {
                  // TODO: Navigate to terms and conditions
                },
                child: Text(
                  'terms_conditions'.tr(),
                  style: const TextStyle(
                    fontSize: 14,
                    color: Colors.blue,
                    decoration: TextDecoration.underline,
                  ),
                ),
              ),
              const SizedBox(width: 4),
              Text(
                'and'.tr(),
                style: const TextStyle(fontSize: 14),
              ),
              const SizedBox(width: 4),
              GestureDetector(
                onTap: () {
                  // TODO: Navigate to privacy policy
                },
                child: Text(
                  'privacy_policy'.tr(),
                  style: const TextStyle(
                    fontSize: 14,
                    color: Colors.blue,
                    decoration: TextDecoration.underline,
                  ),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }
}
