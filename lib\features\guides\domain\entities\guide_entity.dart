// features/guides/domain/entities/guide_entity.dart
import 'package:equatable/equatable.dart';

class GuideEntity extends Equatable {
  final int id;
  final String name;
  final String email;
  final String? img;
  final String mobile;
  final int userType;
  final int isActivate;
  final int dayPrice;
  final int hourPrice;
  final String info;
  final String experience;
  final List<String> interests;
  final List<String> toursType;
  final List<String> albums;
  final int? countryId;
  final String? fcmToken;
  final String createdAt;
  final String updatedAt;

  const GuideEntity({
    required this.id,
    required this.name,
    required this.email,
    this.img,
    required this.mobile,
    required this.userType,
    required this.isActivate,
    required this.dayPrice,
    required this.hourPrice,
    required this.info,
    required this.experience,
    required this.interests,
    required this.toursType,
    required this.albums,
    this.countryId,
    this.fcmToken,
    required this.createdAt,
    required this.updatedAt,
  });

  // Helper methods
  String get displayName => name.isNotEmpty ? name : 'مرشد سياحي';
  
  String get displayInfo => info.isNotEmpty ? info : 'مرشد سياحي محترف';
  
  String get displayExperience => experience.isNotEmpty ? experience : 'خبرة متميزة';
  
  String get formattedDayPrice => '$dayPrice ريال/يوم';
  
  String get formattedHourPrice => '$hourPrice ريال/ساعة';
  
  bool get hasImage => img != null && img!.isNotEmpty;
  
  String get imageUrl => hasImage ? 'https://tripooo.tptechcorp.com/$img' : '';
  
  double get rating => 4.0 + (id % 10) * 0.1; // Mock rating based on ID
  
  List<String> get displayInterests => interests.where((interest) => interest.isNotEmpty).toList();
  
  List<String> get displayToursType => toursType.where((type) => type.isNotEmpty).toList();

  bool get isActive => isActivate == 1;

  @override
  List<Object?> get props => [
        id,
        name,
        email,
        img,
        mobile,
        userType,
        isActivate,
        dayPrice,
        hourPrice,
        info,
        experience,
        interests,
        toursType,
        albums,
        countryId,
        fcmToken,
        createdAt,
        updatedAt,
      ];
}
