// features/Auth/presentation/pages/profile_screen.dart
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:go_router/go_router.dart';
import 'package:tripooo_user/core/theme/theme.dart';
import 'package:tripooo_user/core/widgets/widgets.dart';
import 'package:tripooo_user/core/routing/app_router.dart';
import '../cubit/auth_cubit.dart';

class ProfileScreen extends StatefulWidget {
  const ProfileScreen({super.key});

  @override
  State<ProfileScreen> createState() => _ProfileScreenState();
}

class _ProfileScreenState extends State<ProfileScreen> {
  @override
  void initState() {
    super.initState();
    // Load user data when screen opens
    context.read<AuthCubit>().getUser();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        elevation: 0,
        leading: IconButton(
          icon: Icon(Icons.arrow_back, color: AppColors.textPrimary),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          'profile'.tr(),
          style: AppTextStyles.heading.copyWith(
            fontSize: 20,
            color: AppColors.textPrimary,
          ),
        ),
        centerTitle: true,
        actions: [
          IconButton(
            icon: Icon(Icons.edit, color: AppColors.textPrimary),
            onPressed: () {
              // TODO: Navigate to edit profile screen
            },
          ),
        ],
      ),
      body: BlocListener<AuthCubit, AuthState>(
        listener: (context, state) async {
          if (state is AuthLogoutSuccess) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text('logout_successful'.tr()),
                backgroundColor: AppColors.success,
              ),
            );
            // Small delay to ensure logout is complete
            await Future.delayed(const Duration(milliseconds: 100));
            if (context.mounted) {
              context.go(AppRoutes.login);
            }
          } else if (state is AuthError) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(state.message),
                backgroundColor: AppColors.error,
              ),
            );
          }
        },
        child: BlocBuilder<AuthCubit, AuthState>(
          builder: (context, state) {
            if (state is AuthLoading) {
              return Center(child: CircularProgressIndicator());
            }

            if (state is AuthUserLoaded) {
              return _buildProfileContent(context, state);
            }

            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.person_outline,
                    size: 64,
                    color: AppColors.textTertiary,
                  ),
                  SizedBox(height: 16),
                  Text(
                    'failed_to_load_profile'.tr(),
                    style: AppTextStyles.subtitle.copyWith(
                      color: AppColors.textSecondary,
                    ),
                  ),
                  SizedBox(height: 16),
                  CustomButton(
                    text: 'retry'.tr(),
                    onPressed: () => context.read<AuthCubit>().getUser(),
                    size: ButtonSize.medium,
                  ),
                ],
              ),
            );
          },
        ),
      ),
    );
  }

  Widget _buildProfileContent(BuildContext context, AuthUserLoaded state) {
    final user = state.user;

    return SingleChildScrollView(
      padding: const EdgeInsets.all(24.0),
      child: Column(
        children: [
          // Profile Avatar
          CircleAvatar(
            radius: 50,
            backgroundColor: AppColors.primary,
            child: user.img != null && user.img!.isNotEmpty
                ? ClipOval(
                    child: Image.network(
                      user.img!,
                      width: 100,
                      height: 100,
                      fit: BoxFit.cover,
                      errorBuilder: (context, error, stackTrace) {
                        return Icon(
                          Icons.person,
                          size: 50,
                          color: AppColors.white,
                        );
                      },
                    ),
                  )
                : Icon(Icons.person, size: 50, color: AppColors.white),
          ),

          SizedBox(height: 16),

          // User Name
          Text(
            user.name,
            style: AppTextStyles.heading.copyWith(
              fontSize: 24,
              color: AppColors.textPrimary,
            ),
          ),

          SizedBox(height: 8),

          // User Email
          Text(
            user.email,
            style: AppTextStyles.subtitle.copyWith(
              color: AppColors.textSecondary,
            ),
          ),

          SizedBox(height: 32),

          // Profile Options
          _buildProfileOption(
            icon: Icons.phone,
            title: 'phone_number'.tr(),
            subtitle: user.mobile,
            onTap: () {
              // TODO: Navigate to change mobile screen
            },
          ),

          _buildProfileOption(
            icon: Icons.lock_outline,
            title: 'change_password'.tr(),
            subtitle: 'update_your_password'.tr(),
            onTap: () => context.goToChangePassword(),
          ),

          _buildProfileOption(
            icon: Icons.language,
            title: 'language'.tr(),
            subtitle: 'change_app_language'.tr(),
            onTap: () => context.goToLanguageSelection(),
          ),

          _buildProfileOption(
            icon: Icons.notifications_outlined,
            title: 'notifications'.tr(),
            subtitle: 'manage_notifications'.tr(),
            onTap: () {
              // TODO: Navigate to notification settings
            },
          ),

          _buildProfileOption(
            icon: Icons.help_outline,
            title: 'help_support'.tr(),
            subtitle: 'get_help_and_support'.tr(),
            onTap: () {
              // TODO: Navigate to help screen
            },
          ),

          SizedBox(height: 32),

          // Logout Button
          CustomButton(
            text: 'logout'.tr(),
            onPressed: () => _showLogoutDialog(context),
            isFullWidth: true,
            size: ButtonSize.large,
            backgroundColor: AppColors.error,
          ),

          SizedBox(height: 32),
        ],
      ),
    );
  }

  Widget _buildProfileOption({
    required IconData icon,
    required String title,
    required String subtitle,
    required VoidCallback onTap,
  }) {
    return Card(
      margin: EdgeInsets.only(bottom: 12),
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(AppDimensions.radiusMedium),
      ),
      child: ListTile(
        leading: Icon(icon, color: AppColors.primary, size: 24),
        title: Text(
          title,
          style: AppTextStyles.bodyMedium.copyWith(
            fontWeight: FontWeight.w600,
            color: AppColors.textPrimary,
          ),
        ),
        subtitle: Text(
          subtitle,
          style: AppTextStyles.bodySmall.copyWith(
            color: AppColors.textSecondary,
          ),
        ),
        trailing: Icon(
          Icons.arrow_forward_ios,
          color: AppColors.textTertiary,
          size: 16,
        ),
        onTap: onTap,
      ),
    );
  }

  void _showLogoutDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text('logout'.tr()),
          content: Text('logout_confirmation'.tr()),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: Text('cancel'.tr()),
            ),
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
                context.read<AuthCubit>().logout();
              },
              child: Text(
                'logout'.tr(),
                style: TextStyle(color: AppColors.error),
              ),
            ),
          ],
        );
      },
    );
  }
}
