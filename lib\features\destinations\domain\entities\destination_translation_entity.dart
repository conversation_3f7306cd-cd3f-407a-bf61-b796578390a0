// features/destinations/domain/entities/destination_translation_entity.dart
import 'package:equatable/equatable.dart';

class DestinationTranslationEntity extends Equatable {
  final int id;
  final int destinationId;
  final String locale;
  final String? name;
  final String? info;
  final String? location;

  const DestinationTranslationEntity({
    required this.id,
    required this.destinationId,
    required this.locale,
    this.name,
    this.info,
    this.location,
  });

  @override
  List<Object?> get props => [
        id,
        destinationId,
        locale,
        name,
        info,
        location,
      ];
}
