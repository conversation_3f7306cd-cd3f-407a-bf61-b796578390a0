// features/more/presentation/widgets/more_menu_items.dart
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:tripooo_user/constants/app_images.dart';
import 'package:tripooo_user/core/routing/app_router.dart';
import 'package:tripooo_user/core/theme/app_colors.dart';
import 'package:tripooo_user/core/utils/responsive_extensions.dart';
import 'package:tripooo_user/features/Auth/presentation/cubit/auth_cubit.dart';
import 'package:tripooo_user/features/more/more_constants.dart';
import 'package:tripooo_user/features/more/presentation/widgets/more_menu_item_widget.dart';
import 'package:tripooo_user/features/onboarding/utils/responsive_helper.dart';

class MoreMenuItems extends StatelessWidget {
  final AuthState state;

  const MoreMenuItems({super.key, required this.state});

  @override
  Widget build(BuildContext context) {
    final isTablet = ResponsiveHelper.isTablet(context);

    return Padding(
      padding: EdgeInsets.symmetric(
        horizontal: MoreConstants.horizontalPadding.w(context),
      ),
      child: Column(
        children: [
          if (state is AuthUserLoaded) ...[
            _buildMenuItem(
              context,
              iconAsset: Assets.assetsIconsProfileCircle,
              title: 'الملف الشخصي',
              onTap: () => context.go(AppRoutes.profile),
              isTablet: isTablet,
            ),
            _buildDivider(),
            _buildMenuItem(
              context,
              iconAsset: Assets.assetsIconsFavourite,
              title: 'المفضلة',
              onTap: () {
                // TODO: Navigate to favorites
              },
              isTablet: isTablet,
            ),
            _buildDivider(),
            _buildMenuItem(
              context,
              iconAsset: Assets.assetsIconsSim,
              title: 'احصل على شريحة',
              onTap: () {
                // TODO: Navigate to get chip
              },
              isTablet: isTablet,
            ),
          ],
          _buildDivider(),
          _buildMenuItem(
            context,
            iconAsset: Assets.assetsIconsLanguage,
            title: 'اللغة',
            subtitle: _getCurrentLanguageName(context),
            onTap: () => context.goToLanguageSettings(),
            isTablet: isTablet,
          ),
          _buildDivider(),
          _buildMenuItem(
            context,
            iconAsset: Assets.assetsIconsAboutUs,
            title: 'نبذة عنا',
            onTap: () {
              // TODO: Navigate to about
            },
            isTablet: isTablet,
          ),
          _buildDivider(),
          _buildMenuItem(
            context,
            iconAsset: Assets.assetsIconsTechSupport,
            title: 'الدعم الفني',
            onTap: () {
              // TODO: Navigate to support
            },
            isTablet: isTablet,
          ),
          _buildDivider(),
          _buildMenuItem(
            context,
            iconAsset: Assets.assetsIconsBeGuide,
            title: 'كن مرشد سياحي معنا',
            onTap: () {
              // TODO: Navigate to become guide
            },
            isTablet: isTablet,
          ),
          _buildDivider(),
          _buildMenuItem(
            context,
            iconAsset: Assets.assetsIconsSettings,
            title: 'الاعدادات',
            onTap: () {
              // TODO: Navigate to settings
            },
            isTablet: isTablet,
          ),
          _buildDivider(),
          if (state is AuthUserLoaded) ...[
            SizedBox(
              height: isTablet
                  ? MoreConstants.spacingLarge
                  : MoreConstants.spacingMedium,
            ),
            _buildMenuItem(
              context,
              iconAsset: Assets.assetsIconsLogOut,
              title: 'تسجيل الخروج',
              onTap: () => _showLogoutDialog(context),
              isTablet: isTablet,
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildMenuItem(
    BuildContext context, {
    required String iconAsset,
    required String title,
    String? subtitle,
    required VoidCallback onTap,
    required bool isTablet,
  }) {
    return SimpleMenuItem(
      iconAsset: iconAsset,
      title: title,
      subtitle: subtitle,
      onTap: onTap,
      isTablet: isTablet,
    );
  }

  Widget _buildDivider() {
    return Divider(color: MoreConstants.dividerColor, height: 0);
  }

  String _getCurrentLanguageName(BuildContext context) {
    final currentCode = context.locale.languageCode;
    return MoreConstants.languages[currentCode] ?? 'العربية';
  }

  void _showLogoutDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text('logout'.tr()),
          content: Text('logout_confirmation'.tr()),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: Text('cancel'.tr()),
            ),
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
                context.read<AuthCubit>().logout();
              },
              child: Text(
                'logout'.tr(),
                style: TextStyle(color: AppColors.error),
              ),
            ),
          ],
        );
      },
    );
  }
}
