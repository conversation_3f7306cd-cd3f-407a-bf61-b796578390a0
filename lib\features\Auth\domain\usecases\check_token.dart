// features/Auth/domain/usecases/check_token.dart
import 'package:dartz/dartz.dart';
import '../../../../core/error/failures.dart';
import '../../../../core/usecases/usecase.dart';
import '../entities/user.dart';
import '../repositories/auth_repository.dart';

class CheckToken implements UseCaseWithoutParams<User> {
  final AuthRepository repository;

  const CheckToken(this.repository);

  @override
  Future<Either<Failure, User>> call() async {
    return await repository.checkToken();
  }
}
