# Settings Feature

This feature provides settings and configuration screens for the Tripooo User app, including language settings and other app preferences.

## 📁 Structure

```
lib/features/settings/
├── presentation/
│   ├── pages/
│   │   └── language_settings_screen.dart    # Language selection screen
│   └── widgets/
│       ├── widgets.dart                     # Widget exports
│       └── language_option_widget.dart      # Language option component
└── README.md                                # This file
```

## 🎯 Features

### Language Settings
- **Multi-language Support**: Arabic, English, French, Turkish, Hindi
- **Visual Language Selection**: Flag icons and native language names
- **Real-time Preview**: Shows current language selection
- **Persistent Storage**: Saves language preference using SharedPreferences
- **Smooth Transitions**: Loading states and success feedback

### Responsive Design
- **Tablet Support**: Larger touch targets and spacing
- **Phone Optimized**: Compact layout for smaller screens
- **Accessibility**: Clear visual indicators and proper contrast

## 🎨 UI Components

### LanguageSettingsScreen
Main screen for language selection:
- **Header Section**: Icon, title, and description
- **Language Options**: List of available languages with flags
- **Apply Button**: Confirms language change
- **Loading States**: Shows progress during language change
- **Success/Error Feedback**: SnackBar notifications

### LanguageOptionWidget
Reusable component for language selection:
- **Flag Display**: Country flag emoji
- **Language Names**: Both native and English names
- **Selection Indicator**: Visual feedback for selected language
- **Touch Feedback**: Material Design ripple effects

## 🔧 Usage

### Navigation
Access language settings from the More screen:

```dart
// Navigate to language settings
context.go(AppRoutes.languageSettings);

// Or using extension method
context.goToLanguageSettings();
```

### Language Change Process
1. User selects a language from the list
2. Taps "Apply Changes" button
3. Loading dialog appears
4. Language is saved and applied
5. Success message is shown
6. User returns to previous screen

## 🌐 Supported Languages

| Code | Language | Native Name | Flag |
|------|----------|-------------|------|
| `ar` | Arabic | العربية | 🇸🇦 |
| `en` | English | English | 🇺🇸 |
| `fr` | French | Français | 🇫🇷 |
| `tr` | Turkish | Türkçe | 🇹🇷 |
| `hi` | Hindi | हिन्दी | 🇮🇳 |

## 🔄 Language Service Integration

The settings feature integrates with the core LanguageService:

```dart
// Change language
await LanguageService.changeLanguage(context, languageCode);

// Get current language
final currentLanguage = context.locale.languageCode;

// Save language preference
await LanguageService.saveLanguage(languageCode);
```

## 📱 Responsive Features

### Tablet Adaptations
- Larger font sizes (20px vs 18px for titles)
- Increased padding (24px vs 16px)
- Bigger touch targets (60px vs 56px for buttons)
- Larger flag emojis (32px vs 28px)

### Phone Optimizations
- Compact spacing for better content fit
- Appropriate font sizes for readability
- Optimized button heights and padding

## 🎨 Theming

Uses the app's design system:
- **Colors**: AppColors for consistent theming
- **Typography**: AppTextStyles for text styling
- **Dimensions**: AppDimensions for spacing and sizing
- **Components**: Material Design components with custom styling

## 🔮 Future Enhancements

Planned features for future releases:
- [ ] Theme selection (Light/Dark mode)
- [ ] Notification preferences
- [ ] Privacy settings
- [ ] Data usage settings
- [ ] Cache management
- [ ] Account settings
- [ ] Accessibility options
- [ ] Font size preferences

## 🔗 Integration Points

### More Screen
- Language option shows current language dynamically
- Navigates to language settings when tapped
- Updates display after language change

### App-wide Language Support
- All screens automatically update when language changes
- RTL support for Arabic
- Proper text direction handling
- Localized date and number formatting

## 🚀 Getting Started

To add a new language:

1. **Add translation files**: Create new JSON file in `assets/translations/`
2. **Update supported locales**: Add to `main.dart` EasyLocalization config
3. **Add language option**: Include in LanguageSettingsScreen languages list
4. **Test thoroughly**: Ensure all screens work with new language

## 📋 Best Practices

1. **Always use translation keys**: Never hardcode text strings
2. **Test all languages**: Verify UI layout with different text lengths
3. **Handle RTL properly**: Test Arabic layout and text direction
4. **Provide feedback**: Show loading and success states
5. **Graceful errors**: Handle language change failures properly
