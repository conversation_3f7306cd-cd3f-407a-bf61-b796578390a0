// features/guides/presentation/widgets/guide_details_card.dart
import 'package:flutter/material.dart';
import 'package:tripooo_user/core/theme/theme.dart';
import 'package:tripooo_user/core/utils/responsive_extensions.dart';
import 'package:tripooo_user/core/utils/widget_extensions.dart';
import 'package:tripooo_user/features/onboarding/utils/responsive_helper.dart';
import '../../domain/entities/guide_details_entity.dart';

class GuideDetailsCard extends StatelessWidget {
  final GuideDetailsEntity guide;
  final VoidCallback? onTap;

  const GuideDetailsCard({super.key, required this.guide, this.onTap});

  @override
  Widget build(BuildContext context) {
    final isTablet = ResponsiveHelper.isTablet(context);

    return Card(
      elevation: AppDimensions.cardElevation,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(AppDimensions.cardRadius),
      ),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(AppDimensions.cardRadius),
        child:
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Guide Image
                _buildGuideImage(context),

                // Guide Info
                _buildGuideInfo(context, isTablet),

                // Languages Section
                if (guide.languages.isNotEmpty)
                  _buildLanguagesSection(context, isTablet),

                // Working Days Section
                if (guide.hasWorkingDays)
                  _buildWorkingDaysSection(context, isTablet),

                // Pricing Section
                _buildPricingSection(context, isTablet),
              ],
            ).paddingAll(
              ResponsiveHelper.getSpacing(context, type: 'medium'),
              context,
            ),
      ),
    );
  }

  Widget _buildGuideImage(BuildContext context) {
    return Container(
      height: 200.h(context),
      width: double.infinity,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(AppDimensions.radiusMedium),
        color: Colors.grey.withOpacity(0.1),
      ),
      child: guide.hasImage
          ? ClipRRect(
              borderRadius: BorderRadius.circular(AppDimensions.radiusMedium),
              child: Image.network(
                guide.imageUrl,
                fit: BoxFit.cover,
                errorBuilder: (context, error, stackTrace) =>
                    _buildPlaceholderImage(),
              ),
            )
          : _buildPlaceholderImage(),
    );
  }

  Widget _buildPlaceholderImage() {
    return Icon(Icons.person, size: 80, color: Colors.grey);
  }

  Widget _buildGuideInfo(BuildContext context, bool isTablet) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        ResponsiveHelper.getSpacing(
          context,
          type: 'medium',
        ).verticalSpaceResponsive(context),

        // Name and Rating
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Expanded(
              child: Text(
                guide.displayName,
                style: AppTextStyles.heading.copyWith(
                  fontSize: isTablet ? 20 : 18,
                ),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
            ),
            Row(
              children: [
                Icon(Icons.star, color: Colors.amber, size: 16),
                4.horizontalSpace,
                Text(
                  guide.rating.toStringAsFixed(1),
                  style: AppTextStyles.bodyMedium.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
          ],
        ),

        8.verticalSpace,

        // Info
        Text(
          guide.displayInfo,
          style: AppTextStyles.bodyMedium.copyWith(
            color: AppColors.textSecondary,
          ),
          maxLines: 2,
          overflow: TextOverflow.ellipsis,
        ),

        8.verticalSpace,

        // Experience
        Text(
          'الخبرة: ${guide.displayExperience}',
          style: AppTextStyles.bodySmall.copyWith(
            color: AppColors.textTertiary,
          ),
        ),

        // Country
        if (guide.countryName != null) ...[
          4.verticalSpace,
          Row(
            children: [
              Icon(Icons.location_on, size: 16, color: AppColors.primary),
              4.horizontalSpace,
              Text(
                guide.countryName!,
                style: AppTextStyles.bodySmall.copyWith(
                  color: AppColors.primary,
                ),
              ),
            ],
          ),
        ],
      ],
    );
  }

  Widget _buildLanguagesSection(BuildContext context, bool isTablet) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        ResponsiveHelper.getSpacing(
          context,
          type: 'medium',
        ).verticalSpaceResponsive(context),

        Text(
          'اللغات:',
          style: AppTextStyles.labelMedium.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),

        8.verticalSpace,

        Wrap(
          spacing: 8,
          runSpacing: 4,
          children: guide.getLanguageNamesInLocale('ar').map((language) {
            return Container(
              padding: EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              decoration: BoxDecoration(
                color: AppColors.primary.withOpacity(0.1),
                borderRadius: BorderRadius.circular(AppDimensions.radiusSmall),
                border: Border.all(color: AppColors.primary.withOpacity(0.3)),
              ),
              child: Text(
                language,
                style: AppTextStyles.bodySmall.copyWith(
                  color: AppColors.primary,
                  fontWeight: FontWeight.w500,
                ),
              ),
            );
          }).toList(),
        ),
      ],
    );
  }

  Widget _buildWorkingDaysSection(BuildContext context, bool isTablet) {
    final availableDays = guide.availableDays;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        ResponsiveHelper.getSpacing(
          context,
          type: 'medium',
        ).verticalSpaceResponsive(context),

        Text(
          'أيام العمل المتاحة:',
          style: AppTextStyles.labelMedium.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),

        8.verticalSpace,

        if (availableDays.isNotEmpty)
          ...availableDays.take(3).map((day) {
            return Padding(
              padding: EdgeInsets.only(bottom: 4),
              child: Row(
                children: [
                  Icon(Icons.schedule, size: 16, color: AppColors.success),
                  8.horizontalSpace,
                  Text(
                    '${day.day}: ${day.timeRange}',
                    style: AppTextStyles.bodySmall,
                  ),
                ],
              ),
            );
          })
        else
          Text(
            'لا توجد أيام متاحة حالياً',
            style: AppTextStyles.bodySmall.copyWith(
              color: AppColors.textTertiary,
            ),
          ),

        if (availableDays.length > 3) ...[
          4.verticalSpace,
          Text(
            '+${availableDays.length - 3} أيام أخرى',
            style: AppTextStyles.bodySmall.copyWith(
              color: AppColors.primary,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ],
    );
  }

  Widget _buildPricingSection(BuildContext context, bool isTablet) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        ResponsiveHelper.getSpacing(
          context,
          type: 'medium',
        ).verticalSpaceResponsive(context),

        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  guide.formattedDayPrice,
                  style: AppTextStyles.labelLarge.copyWith(
                    color: AppColors.primary,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Text(
                  guide.formattedHourPrice,
                  style: AppTextStyles.bodySmall.copyWith(
                    color: AppColors.textSecondary,
                  ),
                ),
              ],
            ),

            Container(
              padding: EdgeInsets.symmetric(horizontal: 12, vertical: 6),
              decoration: BoxDecoration(
                color: guide.isActive ? AppColors.success : AppColors.error,
                borderRadius: BorderRadius.circular(AppDimensions.radiusSmall),
              ),
              child: Text(
                guide.isActive ? 'متاح' : 'غير متاح',
                style: AppTextStyles.bodySmall.copyWith(
                  color: AppColors.white,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ],
        ),
      ],
    );
  }
}
