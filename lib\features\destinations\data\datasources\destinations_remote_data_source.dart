// features/destinations/data/datasources/destinations_remote_data_source.dart
import 'package:dio/dio.dart';
import '../../../../core/error/exceptions.dart';
import '../../../../core/network/api_endpoints.dart';
import '../models/destination_model.dart';

abstract class DestinationsRemoteDataSource {
  Future<List<DestinationModel>> getDestinations({
    int offset = 0,
    int limit = 10,
    int? countryId,
    int? priceFrom,
    int? priceTo,
  });

  Future<DestinationModel> getDestinationDetails(int destinationId);
}

class DestinationsRemoteDataSourceImpl implements DestinationsRemoteDataSource {
  final Dio dio;

  DestinationsRemoteDataSourceImpl({required this.dio});

  @override
  Future<List<DestinationModel>> getDestinations({
    int offset = 0,
    int limit = 10,
    int? countryId,
    int? priceFrom,
    int? priceTo,
  }) async {
    try {
      final response = await dio.get(
        ApiEndpoints.getFullUrl(
          ApiEndpoints.destinations(
            offset: offset,
            limit: limit,
            countryId: countryId,
            priceFrom: priceFrom,
            priceTo: priceTo,
          ),
        ),
        options: Options(headers: ApiEndpoints.defaultHeaders),
      );

      if (response.statusCode == 200) {
        final data = response.data;
        if (data['status'] == 200 && data['data'] != null) {
          final List<dynamic> destinationsJson = data['data'];
          return destinationsJson
              .map((json) => DestinationModel.fromJson(json))
              .toList();
        } else {
          throw ServerException(
            data['msg'] ?? 'Failed to get destinations',
            data['status'] ?? 500,
          );
        }
      } else {
        throw ServerException(
          'Failed to get destinations',
          response.statusCode ?? 500,
        );
      }
    } on DioException catch (e) {
      if (e.response != null) {
        final data = e.response!.data;
        throw ServerException(
          data['msg'] ?? 'Server error occurred',
          e.response!.statusCode ?? 500,
        );
      } else {
        throw ServerException('Network error occurred', 0);
      }
    } catch (e) {
      throw ServerException('Unexpected error occurred: ${e.toString()}', 500);
    }
  }

  @override
  Future<DestinationModel> getDestinationDetails(int destinationId) async {
    try {
      final response = await dio.get(
        ApiEndpoints.getFullUrl(ApiEndpoints.destinationDetails(destinationId)),
        options: Options(headers: ApiEndpoints.defaultHeaders),
      );

      if (response.statusCode == 200) {
        final data = response.data;
        if (data['status'] == 200 && data['data'] != null) {
          return DestinationModel.fromJson(data['data']);
        } else {
          throw ServerException(
            data['msg'] ?? 'Failed to get destination details',
            data['status'] ?? 500,
          );
        }
      } else {
        throw ServerException(
          'Failed to get destination details',
          response.statusCode ?? 500,
        );
      }
    } on DioException catch (e) {
      if (e.response != null) {
        final data = e.response!.data;
        throw ServerException(
          data['msg'] ?? 'Server error occurred',
          e.response!.statusCode ?? 500,
        );
      } else {
        throw ServerException('Network error occurred', 0);
      }
    } catch (e) {
      throw ServerException('Unexpected error occurred: ${e.toString()}', 500);
    }
  }
}
