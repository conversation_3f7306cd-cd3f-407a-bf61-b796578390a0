// features/destinations/domain/repositories/destinations_repository.dart
import 'package:dartz/dartz.dart';
import '../../../../core/error/failures.dart';
import '../entities/destination_entity.dart';

abstract class DestinationsRepository {
  Future<Either<Failure, List<DestinationEntity>>> getDestinations({
    int offset = 0,
    int limit = 10,
    int? countryId,
    int? priceFrom,
    int? priceTo,
  });
  
  Future<Either<Failure, DestinationEntity>> getDestinationDetails(int destinationId);
}
