// features/Auth/domain/repositories/auth_repository.dart
import 'dart:io';
import '../../../../core/utils/typedef.dart';
import '../entities/user.dart';
import '../../data/models/social_auth_models.dart';

abstract class AuthRepository {
  /// Get current user profile
  /// Requires authentication token
  ResultFuture<User> getUser();

  /// Check if the provided token is valid
  /// Returns user data if token is valid
  ResultFuture<User> checkToken();

  /// Register a new user (client or guide)
  /// Returns success message
  ResultVoid register({
    required String name,
    required String email,
    required String mobile,
    required String password,
    required String passwordConfirmation,
    required int userType,
    File? file,
    String? fcmToken,
    // Guide-specific fields
    double? dayPrice,
    double? hourPrice,
    String? info,
    String? experience,
    List<String>? interests,
    List<String>? toursType,
    List<int>? languages,
    // Branch fields (optional)
    String? branchName,
    String? branchEmail,
    List<String>? branchPayments,
  });

  /// Verify mobile number with code (OTP)
  /// Returns user data with token on successful verification
  ResultFuture<User> mobileCheck({
    required String code,
    required String mobile,
  });

  /// Request a new verification code for mobile number
  /// Returns success message
  ResultFuture<String> regenerateCode({required String mobile});

  /// Login with mobile and password
  /// Returns user data with token on successful login
  ResultFuture<User> login({
    required String mobile,
    required String password,
    String? fcmToken,
  });

  /// Update user profile information
  /// Requires authentication token
  ResultFuture<User> updateUser({
    String? name,
    String? email,
    File? file,
    double? dayPrice,
    double? hourPrice,
    String? info,
    String? experience,
    List<String>? interests,
    List<String>? toursType,
    List<int>? languages,
  });

  /// Change user password
  /// Requires authentication token
  ResultFuture<String> changePassword({
    required String newPassword,
    required String newPasswordConfirmation,
    required String oldPassword,
  });

  /// Change user mobile number
  /// Requires authentication token
  ResultFuture<String> changeMobileNumber({required String mobile});

  /// Logout user and invalidate token
  /// Requires authentication token
  ResultVoid logout();

  /// Send password reset code to mobile
  /// Returns success message
  ResultFuture<String> sendResetCode({required String mobile});

  /// Verify password reset code
  /// Returns success message if code is valid
  ResultFuture<String> verifyResetCode({
    required String mobile,
    required String code,
  });

  /// Reset password using verification code
  /// Returns success message on successful reset
  ResultFuture<String> resetPassword({
    required String mobile,
    required String code,
    required String password,
    required String passwordConfirmation,
  });

  /// Check if user is logged in
  bool isLoggedIn();

  /// Get stored authentication token
  String? getToken();

  /// Get stored user data
  User? getCurrentUser();

  /// Save user data and token to local storage
  ResultVoid saveUserData(User user, String token);

  /// Clear all stored user data and token
  ResultVoid clearUserData();

  /// Social authentication with Google, Facebook, or Apple
  ResultFuture<User> socialAuth({
    required SocialProvider provider,
    required String accessToken,
    String? idToken,
    String? email,
    String? name,
    String? profilePicture,
    String? fcmToken,
    int userType = 1,
  });
}
