// features/home/<USER>/widgets/featured_guide_card_widget.dart
import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:tripooo_user/core/theme/theme.dart';
import 'package:tripooo_user/features/guides/domain/entities/guide_entity.dart';
import 'package:tripooo_user/features/guides/presentation/cubit/featured_guides_cubit.dart';
import 'package:tripooo_user/features/guides/presentation/cubit/guides_state.dart';
import 'package:tripooo_user/features/guides/presentation/pages/guide_details_page.dart';

class FeaturedGuideCardWidget extends StatefulWidget {
  const FeaturedGuideCardWidget({super.key});

  @override
  State<FeaturedGuideCardWidget> createState() =>
      _FeaturedGuideCardWidgetState();
}

class _FeaturedGuideCardWidgetState extends State<FeaturedGuideCardWidget> {
  late PageController _pageController;
  Timer? _timer;
  int _currentIndex = 0;

  @override
  void initState() {
    super.initState();
    _pageController = PageController();
    // Load featured guides when widget initializes
    context.read<FeaturedGuidesCubit>().getFeaturedGuides();
  }

  @override
  void dispose() {
    _timer?.cancel();
    _pageController.dispose();
    super.dispose();
  }

  void _startAutoSlide(int guidesLength) {
    _timer?.cancel();
    _timer = Timer.periodic(const Duration(seconds: 4), (timer) {
      if (_currentIndex < guidesLength - 1) {
        _currentIndex++;
      } else {
        _currentIndex = 0;
      }

      if (_pageController.hasClients) {
        _pageController.animateToPage(
          _currentIndex,
          duration: const Duration(milliseconds: 800),
          curve: Curves.easeInOut,
        );
      }
    });
  }

  List<Color> _getGradientColors(int id) {
    final gradients = [
      [Colors.blue[300]!, Colors.blue[600]!],
      [Colors.green[300]!, Colors.green[600]!],
      [Colors.purple[300]!, Colors.purple[600]!],
      [Colors.orange[300]!, Colors.orange[600]!],
      [Colors.teal[300]!, Colors.teal[600]!],
      [Colors.pink[300]!, Colors.pink[600]!],
    ];
    return gradients[id % gradients.length];
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<FeaturedGuidesCubit, FeaturedGuidesState>(
      builder: (context, state) {
        if (state is FeaturedGuidesLoading) {
          return _buildLoadingWidget();
        } else if (state is FeaturedGuidesLoaded) {
          final guides = state.guides;
          if (guides.isEmpty) {
            return _buildEmptyWidget();
          }

          // Start auto slide when guides are loaded
          WidgetsBinding.instance.addPostFrameCallback((_) {
            _startAutoSlide(guides.length);
          });

          return _buildCarousel(guides);
        } else if (state is FeaturedGuidesError) {
          return _buildErrorWidget(state.message);
        }

        return _buildLoadingWidget();
      },
    );
  }

  Widget _buildLoadingWidget() {
    return Padding(
      padding: const EdgeInsets.all(20),
      child: Container(
        height: 240,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(16),
          color: Colors.grey[100],
        ),
        child: Center(
          child: CircularProgressIndicator(
            valueColor: AlwaysStoppedAnimation<Color>(AppColors.primary),
          ),
        ),
      ),
    );
  }

  Widget _buildEmptyWidget() {
    return Padding(
      padding: const EdgeInsets.all(20),
      child: Container(
        height: 240,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(16),
          color: Colors.grey[100],
        ),
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(Icons.person_search, size: 48, color: Colors.grey[400]),
              SizedBox(height: 16),
              Text(
                'لا توجد مرشدين مميزين حالياً',
                style: AppTextStyles.bodyMedium.copyWith(
                  color: Colors.grey[600],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildErrorWidget(String message) {
    return Padding(
      padding: const EdgeInsets.all(20),
      child: Container(
        height: 240,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(16),
          color: Colors.grey[100],
        ),
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(Icons.error_outline, size: 48, color: Colors.red[400]),
              SizedBox(height: 16),
              Text(
                'حدث خطأ في تحميل المرشدين',
                style: AppTextStyles.bodyMedium.copyWith(
                  color: Colors.grey[600],
                ),
              ),
              SizedBox(height: 8),
              ElevatedButton(
                onPressed: () {
                  context.read<FeaturedGuidesCubit>().getFeaturedGuides();
                },
                child: Text('إعادة المحاولة'),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildCarousel(List<GuideEntity> guides) {
    return Padding(
      padding: const EdgeInsets.all(20),
      child: Container(
        height: 240,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(16),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.1),
              blurRadius: 10,
              offset: Offset(0, 4),
            ),
          ],
        ),
        child: ClipRRect(
          borderRadius: BorderRadius.circular(16),
          child: Stack(
            children: [
              // PageView for carousel
              PageView.builder(
                controller: _pageController,
                scrollDirection: Axis.vertical, // Vertical flip
                onPageChanged: (index) {
                  setState(() {
                    _currentIndex = index;
                  });
                },
                itemCount: guides.length,
                itemBuilder: (context, index) {
                  final guide = guides[index];
                  return _buildGuideCard(guide);
                },
              ),

              // Indicator على اليمين
              Positioned(
                right: 16,
                top: 0,
                bottom: 0,
                child: Center(
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: List.generate(guides.length, (index) {
                      return Container(
                        margin: EdgeInsets.symmetric(vertical: 3),
                        width: 8,
                        height: _currentIndex == index ? 24 : 8,
                        decoration: BoxDecoration(
                          color: _currentIndex == index
                              ? Colors.white
                              : Colors.white.withValues(alpha: 0.5),
                          borderRadius: BorderRadius.circular(4),
                        ),
                      );
                    }),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildGuideCard(GuideEntity guide) {
    return Container(
      decoration: BoxDecoration(color: Colors.grey[100]),
      child: Stack(
        children: [
          // Background Image (Top Section)
          Positioned(
            top: 0,
            left: 0,
            right: 0,
            height: 120,
            child: Container(
              decoration: BoxDecoration(
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(16),
                  topRight: Radius.circular(16),
                ),
                gradient: LinearGradient(
                  colors: _getGradientColors(guide.id),
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                ),
              ),
              child: Container(
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.only(
                    topLeft: Radius.circular(16),
                    topRight: Radius.circular(16),
                  ),
                  color: Colors.black.withValues(alpha: 0.3),
                ),
              ),
            ),
          ),

          // Content Section (Bottom)
          Positioned(
            bottom: 0,
            left: 0,
            right: 60, // مساحة للـ indicator
            height: 140,
            child: Container(
              padding: EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.only(
                  bottomLeft: Radius.circular(16),
                  bottomRight: Radius.circular(16),
                ),
              ),
              child: Row(
                children: [
                  // Guide Avatar
                  Container(
                    width: 70,
                    height: 70,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(12),
                      color: Colors.grey[300],
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withValues(alpha: 0.1),
                          blurRadius: 8,
                          offset: Offset(0, 2),
                        ),
                      ],
                    ),
                    child: ClipRRect(
                      borderRadius: BorderRadius.circular(12),
                      child: guide.hasImage
                          ? Image.network(
                              guide.imageUrl,
                              fit: BoxFit.cover,
                              errorBuilder: (context, error, stackTrace) {
                                return Icon(
                                  Icons.person,
                                  color: Colors.grey[600],
                                  size: 40,
                                );
                              },
                              loadingBuilder:
                                  (context, child, loadingProgress) {
                                    if (loadingProgress == null) return child;
                                    return Center(
                                      child: CircularProgressIndicator(
                                        strokeWidth: 2,
                                        valueColor:
                                            AlwaysStoppedAnimation<Color>(
                                              AppColors.primary,
                                            ),
                                      ),
                                    );
                                  },
                            )
                          : Icon(
                              Icons.person,
                              color: Colors.grey[600],
                              size: 40,
                            ),
                    ),
                  ),
                  SizedBox(width: 12),
                  // Guide Info
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisAlignment: MainAxisAlignment.center,
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Text(
                          '${guide.displayName} (مرشد سياحي)',
                          style: AppTextStyles.heading.copyWith(
                            color: Colors.black87,
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                        SizedBox(height: 4),
                        Text(
                          guide.displayInfo,
                          style: AppTextStyles.bodySmall.copyWith(
                            color: Colors.grey[600],
                            fontSize: 13,
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                        SizedBox(height: 4),
                        Text(
                          guide.displayExperience,
                          style: AppTextStyles.bodySmall.copyWith(
                            color: Colors.grey[700],
                            fontSize: 12,
                            fontWeight: FontWeight.w500,
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                        SizedBox(height: 4),
                        Row(
                          children: [
                            // Rating Stars
                            Flexible(
                              child: Row(
                                mainAxisSize: MainAxisSize.min,
                                children: List.generate(5, (index) {
                                  return Icon(
                                    index < guide.rating.floor()
                                        ? Icons.star
                                        : Icons.star_border,
                                    color: Colors.amber,
                                    size: 16,
                                  );
                                }),
                              ),
                            ),
                            SizedBox(width: 8),
                            Flexible(
                              child: Text(
                                guide.formattedDayPrice,
                                style: AppTextStyles.bodySmall.copyWith(
                                  color: AppColors.primary,
                                  fontSize: 12,
                                  fontWeight: FontWeight.bold,
                                ),
                                overflow: TextOverflow.ellipsis,
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),

          // View Details Button
          Positioned(
            bottom: 20,
            right: 80,
            child: ElevatedButton(
              onPressed: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => GuideDetailsPage(guideId: guide.id),
                  ),
                );
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.primary,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(20),
                ),
                padding: EdgeInsets.symmetric(horizontal: 20, vertical: 10),
              ),
              child: Text(
                'عرض التفاصيل',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 13,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
