// features/guides/domain/entities/language_entity.dart
import 'package:equatable/equatable.dart';

class LanguageTranslation extends Equatable {
  final int id;
  final int languageId;
  final String locale;
  final String name;

  const LanguageTranslation({
    required this.id,
    required this.languageId,
    required this.locale,
    required this.name,
  });

  @override
  List<Object?> get props => [id, languageId, locale, name];
}

class LanguagePivot extends Equatable {
  final int userId;
  final int languageId;

  const LanguagePivot({
    required this.userId,
    required this.languageId,
  });

  @override
  List<Object?> get props => [userId, languageId];
}

class LanguageEntity extends Equatable {
  final int id;
  final String createdAt;
  final String updatedAt;
  final String name;
  final String? deletedAt;
  final int isActivate;
  final LanguagePivot? pivot;
  final List<LanguageTranslation> translations;

  const LanguageEntity({
    required this.id,
    required this.createdAt,
    required this.updatedAt,
    required this.name,
    this.deletedAt,
    required this.isActivate,
    this.pivot,
    required this.translations,
  });

  // Helper methods
  bool get isActive => isActivate == 1;

  String getTranslatedName(String locale) {
    final translation = translations.firstWhere(
      (t) => t.locale == locale,
      orElse: () => translations.isNotEmpty 
          ? translations.first 
          : LanguageTranslation(
              id: 0, 
              languageId: id, 
              locale: locale, 
              name: name,
            ),
    );
    return translation.name;
  }

  @override
  List<Object?> get props => [
        id,
        createdAt,
        updatedAt,
        name,
        deletedAt,
        isActivate,
        pivot,
        translations,
      ];
}
