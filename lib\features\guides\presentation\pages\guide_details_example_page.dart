// features/guides/presentation/pages/guide_details_example_page.dart
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:tripooo_user/core/theme/theme.dart';
import 'package:tripooo_user/core/utils/responsive_extensions.dart';
import 'package:tripooo_user/core/utils/widget_extensions.dart';
import 'package:tripooo_user/features/onboarding/utils/responsive_helper.dart';
import 'package:tripooo_user/core/di/injection_container.dart' as di;
import '../cubit/guide_details_cubit.dart';
import '../widgets/guide_details_card.dart';

class GuideDetailsExamplePage extends StatelessWidget {
  final int guideId;

  const GuideDetailsExamplePage({
    super.key,
    required this.guideId,
  });

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => di.sl<GuideDetailsCubit>()..loadGuideDetails(guideId),
      child: Scaffold(
        backgroundColor: AppColors.background,
        appBar: AppBar(
          title: Text('تفاصيل المرشد'),
          backgroundColor: AppColors.primary,
          foregroundColor: AppColors.white,
        ),
        body: BlocBuilder<GuideDetailsCubit, GuideDetailsState>(
          builder: (context, state) {
            if (state is GuideDetailsLoading) {
              return _buildLoadingState(context);
            } else if (state is GuideDetailsLoaded) {
              return _buildLoadedState(context, state);
            } else if (state is GuideDetailsError) {
              return _buildErrorState(context, state);
            }
            return _buildInitialState(context);
          },
        ),
      ),
    );
  }

  Widget _buildLoadingState(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CircularProgressIndicator(
            color: AppColors.primary,
            strokeWidth: ResponsiveHelper.isTablet(context) ? 4.0 : 3.0,
          ),
          ResponsiveHelper.getSpacing(context, type: 'medium').verticalSpaceResponsive(context),
          Text(
            'جاري تحميل تفاصيل المرشد...',
            style: AppTextStyles.bodyMedium.copyWith(
              color: AppColors.textSecondary,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildLoadedState(BuildContext context, GuideDetailsLoaded state) {
    final guide = state.guideDetails;
    final isTablet = ResponsiveHelper.isTablet(context);
    
    return SingleChildScrollView(
      child: Center(
        child: ConstrainedBox(
          constraints: BoxConstraints(
            maxWidth: isTablet ? 800.w(context) : double.infinity,
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Guide Details Card
              GuideDetailsCard(
                guide: guide,
                onTap: () {
                  // Navigate to full guide details page
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text('تم النقر على بطاقة المرشد ${guide.displayName}'),
                      backgroundColor: AppColors.success,
                    ),
                  );
                },
              ),
              
              ResponsiveHelper.getSpacing(context, type: 'large').verticalSpaceResponsive(context),
              
              // Additional Info Section
              _buildAdditionalInfoSection(context, guide, isTablet),
              
              ResponsiveHelper.getSpacing(context, type: 'large').verticalSpaceResponsive(context),
              
              // Action Buttons
              _buildActionButtons(context, guide, isTablet),
              
              ResponsiveHelper.getSpacing(context, type: 'xlarge').verticalSpaceResponsive(context),
            ],
          ).paddingSymmetric(
            horizontal: isTablet ? 32 : 16,
            vertical: isTablet ? 24 : 16,
            context: context,
          ),
        ),
      ),
    );
  }

  Widget _buildAdditionalInfoSection(BuildContext context, guide, bool isTablet) {
    return Card(
      elevation: AppDimensions.cardElevation,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(AppDimensions.cardRadius),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'معلومات إضافية',
            style: AppTextStyles.heading.copyWith(
              fontSize: isTablet ? 20 : 18,
            ),
          ),
          
          ResponsiveHelper.getSpacing(context, type: 'medium').verticalSpaceResponsive(context),
          
          // Interests
          if (guide.displayInterests.isNotEmpty) ...[
            _buildInfoRow(
              context,
              'الاهتمامات',
              guide.displayInterests.join(', '),
              Icons.favorite,
            ),
            12.verticalSpace,
          ],
          
          // Tours Type
          if (guide.displayToursType.isNotEmpty) ...[
            _buildInfoRow(
              context,
              'أنواع الجولات',
              guide.displayToursType.join(', '),
              Icons.tour,
            ),
            12.verticalSpace,
          ],
          
          // Contact Info
          _buildInfoRow(
            context,
            'رقم الهاتف',
            guide.mobile,
            Icons.phone,
          ),
          
          12.verticalSpace,
          
          _buildInfoRow(
            context,
            'البريد الإلكتروني',
            guide.email,
            Icons.email,
          ),
          
          // Verification Status
          ResponsiveHelper.getSpacing(context, type: 'medium').verticalSpaceResponsive(context),
          
          Row(
            children: [
              _buildVerificationChip(
                'البريد الإلكتروني',
                guide.isEmailVerified,
              ),
              8.horizontalSpace,
              _buildVerificationChip(
                'رقم الهاتف',
                guide.isMobileVerified,
              ),
            ],
          ),
        ],
      ).paddingAll(ResponsiveHelper.getSpacing(context, type: 'medium'), context),
    );
  }

  Widget _buildInfoRow(BuildContext context, String label, String value, IconData icon) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Icon(icon, size: 20, color: AppColors.primary),
        12.horizontalSpace,
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                label,
                style: AppTextStyles.labelMedium.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
              4.verticalSpace,
              Text(
                value,
                style: AppTextStyles.bodyMedium,
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildVerificationChip(String label, bool isVerified) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: isVerified ? AppColors.success.withOpacity(0.1) : AppColors.error.withOpacity(0.1),
        borderRadius: BorderRadius.circular(AppDimensions.radiusSmall),
        border: Border.all(
          color: isVerified ? AppColors.success : AppColors.error,
        ),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            isVerified ? Icons.verified : Icons.error,
            size: 16,
            color: isVerified ? AppColors.success : AppColors.error,
          ),
          4.horizontalSpace,
          Text(
            label,
            style: AppTextStyles.bodySmall.copyWith(
              color: isVerified ? AppColors.success : AppColors.error,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildActionButtons(BuildContext context, guide, bool isTablet) {
    return Row(
      children: [
        Expanded(
          child: ElevatedButton.icon(
            onPressed: () {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text('تم حجز المرشد ${guide.displayName}'),
                  backgroundColor: AppColors.success,
                ),
              );
            },
            icon: Icon(Icons.book_online),
            label: Text('احجز الآن'),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.primary,
              foregroundColor: AppColors.white,
              padding: EdgeInsets.symmetric(
                vertical: ResponsiveHelper.getButtonHeight(context) / 4,
              ),
            ),
          ),
        ),
        
        16.horizontalSpace,
        
        Expanded(
          child: OutlinedButton.icon(
            onPressed: () {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text('تم التواصل مع المرشد ${guide.displayName}'),
                  backgroundColor: AppColors.info,
                ),
              );
            },
            icon: Icon(Icons.chat),
            label: Text('تواصل'),
            style: OutlinedButton.styleFrom(
              foregroundColor: AppColors.primary,
              side: BorderSide(color: AppColors.primary),
              padding: EdgeInsets.symmetric(
                vertical: ResponsiveHelper.getButtonHeight(context) / 4,
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildErrorState(BuildContext context, GuideDetailsError state) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.error_outline,
            size: 64,
            color: AppColors.error,
          ),
          ResponsiveHelper.getSpacing(context, type: 'medium').verticalSpaceResponsive(context),
          Text(
            'حدث خطأ في تحميل تفاصيل المرشد',
            style: AppTextStyles.heading.copyWith(
              color: AppColors.error,
            ),
            textAlign: TextAlign.center,
          ),
          8.verticalSpace,
          Text(
            state.message,
            style: AppTextStyles.bodyMedium.copyWith(
              color: AppColors.textSecondary,
            ),
            textAlign: TextAlign.center,
          ),
          ResponsiveHelper.getSpacing(context, type: 'large').verticalSpaceResponsive(context),
          ElevatedButton.icon(
            onPressed: () {
              context.read<GuideDetailsCubit>().loadGuideDetails(guideId);
            },
            icon: Icon(Icons.refresh),
            label: Text('إعادة المحاولة'),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.primary,
              foregroundColor: AppColors.white,
            ),
          ),
        ],
      ).paddingSymmetric(
        horizontal: ResponsiveHelper.isTablet(context) ? 48 : 24,
        vertical: ResponsiveHelper.isTablet(context) ? 32 : 24,
        context: context,
      ),
    );
  }

  Widget _buildInitialState(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.person_search,
            size: 64,
            color: AppColors.primary,
          ),
          ResponsiveHelper.getSpacing(context, type: 'medium').verticalSpaceResponsive(context),
          Text(
            'اضغط لتحميل تفاصيل المرشد',
            style: AppTextStyles.heading.copyWith(
              color: AppColors.primary,
            ),
          ),
          ResponsiveHelper.getSpacing(context, type: 'large').verticalSpaceResponsive(context),
          ElevatedButton.icon(
            onPressed: () {
              context.read<GuideDetailsCubit>().loadGuideDetails(guideId);
            },
            icon: Icon(Icons.download),
            label: Text('تحميل التفاصيل'),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.primary,
              foregroundColor: AppColors.white,
            ),
          ),
        ],
      ),
    );
  }
}
