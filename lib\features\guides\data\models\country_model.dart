// features/guides/data/models/country_model.dart
import '../../domain/entities/country_entity.dart';

class CountryModel extends CountryEntity {
  const CountryModel({
    required super.id,
    required super.createdAt,
    required super.updatedAt,
    required super.name,
    super.deletedAt,
    required super.isActivate,
  });

  factory CountryModel.fromJson(Map<String, dynamic> json) {
    return CountryModel(
      id: json['id'] ?? 0,
      createdAt: json['created_at'] ?? '',
      updatedAt: json['updated_at'] ?? '',
      name: json['name'] ?? '',
      deletedAt: json['deleted_at'],
      isActivate: json['is_activate'] ?? 0,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'created_at': createdAt,
      'updated_at': updatedAt,
      'name': name,
      'deleted_at': deletedAt,
      'is_activate': isActivate,
    };
  }
}
