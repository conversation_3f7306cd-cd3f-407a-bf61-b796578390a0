// core/widgets/custom_button.dart
import 'package:flutter/material.dart';
import '../theme/theme.dart';

enum ButtonType { elevated, outlined, text }

enum ButtonSize { small, medium, large }

class CustomButton extends StatelessWidget {
  final String? text;
  final Widget? child;
  final VoidCallback? onPressed;
  final VoidCallback? onLongPress;
  final ButtonType type;
  final ButtonSize size;
  final bool isLoading;
  final bool isFullWidth;
  final Color? backgroundColor;
  final Color? foregroundColor;
  final Color? borderColor;
  final Color? disabledBackgroundColor;
  final Color? disabledForegroundColor;
  final double? borderWidth;
  final BorderRadius? borderRadius;
  final EdgeInsetsGeometry? padding;
  final double? elevation;
  final TextStyle? textStyle;
  final Widget? icon;
  final bool iconFirst;
  final double? iconSpacing;
  final double? width;
  final double? height;
  final Widget? loadingWidget;
  final Color? loadingColor;
  final AlignmentGeometry? alignment;

  const CustomButton({
    super.key,
    this.text,
    this.child,
    this.onPressed,
    this.onLongPress,
    this.type = ButtonType.elevated,
    this.size = ButtonSize.medium,
    this.isLoading = false,
    this.isFullWidth = false,
    this.backgroundColor,
    this.foregroundColor,
    this.borderColor,
    this.disabledBackgroundColor,
    this.disabledForegroundColor,
    this.borderWidth,
    this.borderRadius,
    this.padding,
    this.elevation,
    this.textStyle,
    this.icon,
    this.iconFirst = true,
    this.iconSpacing = 8.0,
    this.width,
    this.height,
    this.loadingWidget,
    this.loadingColor,
    this.alignment,
  }) : assert(
         text != null || child != null,
         'Either text or child must be provided',
       );

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    // Get size-based properties
    final sizeProps = _getSizeProperties();

    // Build button content
    Widget buttonChild = _buildButtonContent();

    // Build the actual button based on type
    Widget button = _buildButton(context, theme, sizeProps, buttonChild);

    // Apply width constraints
    if (isFullWidth) {
      button = SizedBox(width: double.infinity, child: button);
    } else if (width != null) {
      button = SizedBox(width: width, child: button);
    }

    return button;
  }

  _ButtonSizeProperties _getSizeProperties() {
    switch (size) {
      case ButtonSize.small:
        return _ButtonSizeProperties(
          height: height ?? AppDimensions.buttonHeightSmall,
          padding:
              padding ??
              const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          textStyle:
              textStyle ?? AppTextStyles.buttonText.copyWith(fontSize: 14),
        );
      case ButtonSize.medium:
        return _ButtonSizeProperties(
          height: height ?? AppDimensions.buttonHeightMedium,
          padding:
              padding ??
              const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
          textStyle: textStyle ?? AppTextStyles.buttonText,
        );
      case ButtonSize.large:
        return _ButtonSizeProperties(
          height: height ?? AppDimensions.buttonHeightLarge,
          padding:
              padding ??
              const EdgeInsets.symmetric(horizontal: 32, vertical: 16),
          textStyle:
              textStyle ?? AppTextStyles.buttonText.copyWith(fontSize: 18),
        );
    }
  }

  Widget _buildButtonContent() {
    if (isLoading) {
      return loadingWidget ??
          SizedBox(
            height: 20,
            width: 20,
            child: CircularProgressIndicator(
              color: loadingColor ?? Colors.white,
              strokeWidth: 2,
            ),
          );
    }

    if (child != null) {
      return child!;
    }

    if (icon != null && text != null) {
      return Row(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.center,
        children: iconFirst
            ? [icon!, SizedBox(width: iconSpacing), Text(text!)]
            : [Text(text!), SizedBox(width: iconSpacing), icon!],
      );
    }

    if (icon != null) {
      return icon!;
    }

    return Text(text!);
  }

  Widget _buildButton(
    BuildContext context,
    ThemeData theme,
    _ButtonSizeProperties sizeProps,
    Widget buttonChild,
  ) {
    final isEnabled = onPressed != null && !isLoading;

    switch (type) {
      case ButtonType.elevated:
        return SizedBox(
          height: sizeProps.height,
          child: ElevatedButton(
            onPressed: isEnabled ? onPressed : null,
            onLongPress: onLongPress,
            style: ElevatedButton.styleFrom(
              backgroundColor: backgroundColor ?? AppColors.primary,
              foregroundColor: foregroundColor ?? AppColors.white,
              disabledBackgroundColor:
                  disabledBackgroundColor ?? Colors.grey[300],
              disabledForegroundColor:
                  disabledForegroundColor ?? Colors.grey[600],
              elevation: elevation,
              padding: sizeProps.padding,
              shape: RoundedRectangleBorder(
                borderRadius:
                    borderRadius ??
                    BorderRadius.circular(AppDimensions.radiusMedium),
                side: borderColor != null
                    ? BorderSide(color: borderColor!, width: borderWidth ?? 1)
                    : BorderSide.none,
              ),
              textStyle: sizeProps.textStyle,
              alignment: alignment,
            ),
            child: buttonChild,
          ),
        );

      case ButtonType.outlined:
        return SizedBox(
          height: sizeProps.height,
          child: OutlinedButton(
            onPressed: isEnabled ? onPressed : null,
            onLongPress: onLongPress,
            style: OutlinedButton.styleFrom(
              foregroundColor: foregroundColor ?? Colors.blue,
              disabledForegroundColor:
                  disabledForegroundColor ?? Colors.grey[600],
              backgroundColor: backgroundColor,
              padding: sizeProps.padding,
              side: BorderSide(
                color: borderColor ?? Colors.blue,
                width: borderWidth ?? 1,
              ),
              shape: RoundedRectangleBorder(
                borderRadius: borderRadius ?? BorderRadius.circular(8),
              ),
              textStyle: sizeProps.textStyle,
              alignment: alignment,
            ),
            child: buttonChild,
          ),
        );

      case ButtonType.text:
        return SizedBox(
          height: sizeProps.height,
          child: TextButton(
            onPressed: isEnabled ? onPressed : null,
            onLongPress: onLongPress,
            style: TextButton.styleFrom(
              foregroundColor: foregroundColor ?? Colors.blue,
              disabledForegroundColor:
                  disabledForegroundColor ?? Colors.grey[600],
              backgroundColor: backgroundColor,
              padding: sizeProps.padding,
              shape: RoundedRectangleBorder(
                borderRadius: borderRadius ?? BorderRadius.circular(8),
              ),
              textStyle: sizeProps.textStyle,
              alignment: alignment,
            ),
            child: buttonChild,
          ),
        );
    }
  }
}

class _ButtonSizeProperties {
  final double height;
  final EdgeInsetsGeometry padding;
  final TextStyle textStyle;

  _ButtonSizeProperties({
    required this.height,
    required this.padding,
    required this.textStyle,
  });
}
