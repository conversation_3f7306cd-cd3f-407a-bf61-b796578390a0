// features/destinations/data/repositories/destinations_repository_impl.dart
import 'package:dartz/dartz.dart';
import '../../../../core/error/exceptions.dart';
import '../../../../core/error/failures.dart';
import '../../../../core/network/network_info.dart';
import '../../domain/entities/destination_entity.dart';
import '../../domain/repositories/destinations_repository.dart';
import '../datasources/destinations_remote_data_source.dart';

class DestinationsRepositoryImpl implements DestinationsRepository {
  final DestinationsRemoteDataSource remoteDataSource;
  final NetworkInfo networkInfo;

  DestinationsRepositoryImpl({
    required this.remoteDataSource,
    required this.networkInfo,
  });

  @override
  Future<Either<Failure, List<DestinationEntity>>> getDestinations({
    int offset = 0,
    int limit = 10,
    int? countryId,
    int? priceFrom,
    int? priceTo,
  }) async {
    if (await networkInfo.isConnected) {
      try {
        final destinations = await remoteDataSource.getDestinations(
          offset: offset,
          limit: limit,
          countryId: countryId,
          priceFrom: priceFrom,
          priceTo: priceTo,
        );
        return Right(destinations);
      } on ServerException catch (e) {
        return Left(ServerFailure(e.message, e.statusCode ?? 500));
      } catch (e) {
        return Left(ServerFailure('Unexpected error occurred', 500));
      }
    } else {
      return Left(NetworkFailure('No internet connection', 0));
    }
  }

  @override
  Future<Either<Failure, DestinationEntity>> getDestinationDetails(
    int destinationId,
  ) async {
    if (await networkInfo.isConnected) {
      try {
        final destination = await remoteDataSource.getDestinationDetails(
          destinationId,
        );
        return Right(destination);
      } on ServerException catch (e) {
        return Left(ServerFailure(e.message, e.statusCode ?? 500));
      } catch (e) {
        return Left(ServerFailure('Unexpected error occurred', 500));
      }
    } else {
      return Left(NetworkFailure('No internet connection', 0));
    }
  }
}
