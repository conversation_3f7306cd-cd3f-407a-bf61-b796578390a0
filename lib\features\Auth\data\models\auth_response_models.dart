// features/Auth/data/models/auth_response_models.dart
import 'user_model.dart';
import '../../../../core/utils/typedef.dart';

class AuthResponse {
  final int status;
  final String message;
  final UserModel? user;
  final String? token;

  const AuthResponse({
    required this.status,
    required this.message,
    this.user,
    this.token,
  });

  factory AuthResponse.fromMap(DataMap map) {
    return AuthResponse(
      status: map['status'] ?? 200,
      message: map['msg'] ?? map['message'] ?? '',
      user: map['data'] != null && map['data'] is Map<String, dynamic>
          ? UserModel.fromMap(map['data'])
          : null,
      token: map['data']?['token'] ?? map['token'],
    );
  }

  bool get isSuccess => status == 200;
}

class SimpleResponse {
  final int status;
  final String message;
  final dynamic data;

  const SimpleResponse({
    required this.status,
    required this.message,
    this.data,
  });

  factory SimpleResponse.fromMap(DataMap map) {
    return SimpleResponse(
      status: map['status'] ?? 200,
      message: map['msg'] ?? map['message'] ?? '',
      data: map['data'],
    );
  }

  bool get isSuccess => status == 200;
}

class LogoutResponse {
  final int status;
  final String message;
  final String? token;

  const LogoutResponse({
    required this.status,
    required this.message,
    this.token,
  });

  factory LogoutResponse.fromMap(DataMap map) {
    return LogoutResponse(
      status: map['status'] ?? 200,
      message: map['msg'] ?? map['message'] ?? '',
      token: map['data']?['token'],
    );
  }

  bool get isSuccess => status == 200;
}
