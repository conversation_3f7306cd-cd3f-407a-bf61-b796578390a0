// features/guides/data/models/guide_details_model.dart
import '../../domain/entities/guide_details_entity.dart';
import 'language_model.dart';
import 'country_model.dart';
import 'working_day_model.dart';

class GuideDetailsModel extends GuideDetailsEntity {
  const GuideDetailsModel({
    required super.id,
    required super.name,
    required super.email,
    super.img,
    super.emailVerifiedAt,
    required super.createdAt,
    required super.updatedAt,
    required super.mobile,
    required super.userType,
    super.deletedAt,
    required super.isActivate,
    required super.dayPrice,
    required super.hourPrice,
    required super.info,
    required super.experience,
    required super.interests,
    required super.toursType,
    required super.albums,
    super.countryId,
    super.mobileVerifiedAt,
    super.fcmToken,
    required super.languages,
    super.country,
    required super.days,
  });

  factory GuideDetailsModel.fromJson(Map<String, dynamic> json) {
    return GuideDetailsModel(
      id: json['id'] ?? 0,
      name: json['name'] ?? '',
      email: json['email'] ?? '',
      img: json['img'],
      emailVerifiedAt: json['email_verified_at'],
      createdAt: json['created_at'] ?? '',
      updatedAt: json['updated_at'] ?? '',
      mobile: json['mobile'] ?? '',
      userType: json['user_type'] ?? 0,
      deletedAt: json['deleted_at'],
      isActivate: json['is_activate'] ?? 0,
      dayPrice: json['day_price'] ?? 0,
      hourPrice: json['hour_price'] ?? 0,
      info: json['info'] ?? '',
      experience: json['experience'] ?? '',
      interests: List<String>.from(json['interests'] ?? []),
      toursType: List<String>.from(json['tours_type'] ?? []),
      albums: List<String>.from(json['albums'] ?? []),
      countryId: json['country_id'],
      mobileVerifiedAt: json['mobile_verified_at'],
      fcmToken: json['fcm_token'],
      languages: (json['languages'] as List<dynamic>?)
              ?.map((language) => LanguageModel.fromJson(language))
              .toList() ??
          [],
      country: json['country'] != null 
          ? CountryModel.fromJson(json['country'])
          : null,
      days: (json['days'] as List<dynamic>?)
              ?.map((day) => WorkingDayModel.fromJson(day))
              .toList() ??
          [],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'email': email,
      'img': img,
      'email_verified_at': emailVerifiedAt,
      'created_at': createdAt,
      'updated_at': updatedAt,
      'mobile': mobile,
      'user_type': userType,
      'deleted_at': deletedAt,
      'is_activate': isActivate,
      'day_price': dayPrice,
      'hour_price': hourPrice,
      'info': info,
      'experience': experience,
      'interests': interests,
      'tours_type': toursType,
      'albums': albums,
      'country_id': countryId,
      'mobile_verified_at': mobileVerifiedAt,
      'fcm_token': fcmToken,
      'languages': languages
          .map((language) => (language as LanguageModel).toJson())
          .toList(),
      'country': (country as CountryModel?)?.toJson(),
      'days': days
          .map((day) => (day as WorkingDayModel).toJson())
          .toList(),
    };
  }
}
