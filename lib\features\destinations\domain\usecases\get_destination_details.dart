// features/destinations/domain/usecases/get_destination_details.dart
import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';
import '../../../../core/error/failures.dart';
import '../../../../core/usecases/usecase.dart';
import '../entities/destination_entity.dart';
import '../repositories/destinations_repository.dart';

class GetDestinationDetails implements UseCase<DestinationEntity, GetDestinationDetailsParams> {
  final DestinationsRepository repository;

  GetDestinationDetails(this.repository);

  @override
  Future<Either<Failure, DestinationEntity>> call(GetDestinationDetailsParams params) async {
    return await repository.getDestinationDetails(params.destinationId);
  }
}

class GetDestinationDetailsParams extends Equatable {
  final int destinationId;

  const GetDestinationDetailsParams({required this.destinationId});

  @override
  List<Object> get props => [destinationId];
}
