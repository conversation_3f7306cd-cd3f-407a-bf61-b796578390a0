// features/destinations/presentation/cubit/destinations_cubit.dart
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:equatable/equatable.dart';
import '../../domain/entities/destination_entity.dart';
import '../../domain/usecases/get_destinations.dart';
import '../../domain/usecases/get_destination_details.dart';

part 'destinations_state.dart';

class DestinationsCubit extends Cubit<DestinationsState> {
  final GetDestinations getDestinationsUseCase;
  final GetDestinationDetails getDestinationDetailsUseCase;

  DestinationsCubit({
    required this.getDestinationsUseCase,
    required this.getDestinationDetailsUseCase,
  }) : super(DestinationsInitial());

  Future<void> loadDestinations({
    int offset = 0,
    int limit = 10,
    int? countryId,
    int? priceFrom,
    int? priceTo,
    bool isRefresh = false,
  }) async {
    if (isRefresh) {
      emit(DestinationsLoading());
    } else if (state is! DestinationsLoaded) {
      emit(DestinationsLoading());
    }

    final result = await getDestinationsUseCase(
      GetDestinationsParams(
        offset: offset,
        limit: limit,
        countryId: countryId,
        priceFrom: priceFrom,
        priceTo: priceTo,
      ),
    );

    result.fold(
      (failure) => emit(
        DestinationsError(
          message: failure.message,
          statusCode: failure.statusCode,
        ),
      ),
      (destinations) {
        if (isRefresh || offset == 0) {
          emit(DestinationsLoaded(destinations: destinations));
        } else {
          // Load more - append to existing list
          final currentState = state;
          if (currentState is DestinationsLoaded) {
            final updatedDestinations = [
              ...currentState.destinations,
              ...destinations,
            ];
            emit(DestinationsLoaded(destinations: updatedDestinations));
          } else {
            emit(DestinationsLoaded(destinations: destinations));
          }
        }
      },
    );
  }

  Future<void> loadDestinationDetails(int destinationId) async {
    emit(DestinationDetailsLoading());

    final result = await getDestinationDetailsUseCase(
      GetDestinationDetailsParams(destinationId: destinationId),
    );

    result.fold(
      (failure) => emit(
        DestinationDetailsError(
          message: failure.message,
          statusCode: failure.statusCode,
        ),
      ),
      (destination) => emit(DestinationDetailsLoaded(destination: destination)),
    );
  }

  Future<void> refreshDestinations({
    int? countryId,
    int? priceFrom,
    int? priceTo,
  }) async {
    await loadDestinations(
      offset: 0,
      limit: 10,
      countryId: countryId,
      priceFrom: priceFrom,
      priceTo: priceTo,
      isRefresh: true,
    );
  }

  Future<void> loadMoreDestinations({
    int? countryId,
    int? priceFrom,
    int? priceTo,
  }) async {
    final currentState = state;
    if (currentState is DestinationsLoaded) {
      await loadDestinations(
        offset: currentState.destinations.length,
        limit: 10,
        countryId: countryId,
        priceFrom: priceFrom,
        priceTo: priceTo,
        isRefresh: false,
      );
    }
  }

  Future<void> getDestinationDetails(int destinationId) async {
    emit(DestinationsLoading());

    final result = await getDestinationDetailsUseCase(
      GetDestinationDetailsParams(destinationId: destinationId),
    );

    result.fold(
      (failure) => emit(
        DestinationsError(
          message: failure.message,
          statusCode: failure.statusCode,
        ),
      ),
      (destination) => emit(DestinationDetailsLoaded(destination: destination)),
    );
  }

  void resetState() {
    emit(DestinationsInitial());
  }
}
