// features/Auth/domain/usecases/verify_reset_code.dart
import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';
import '../../../../core/error/failures.dart';
import '../../../../core/usecases/usecase.dart';
import '../repositories/auth_repository.dart';

class VerifyResetCode implements UseCase<String, VerifyResetCodeParams> {
  final AuthRepository repository;

  const VerifyResetCode(this.repository);

  @override
  Future<Either<Failure, String>> call(VerifyResetCodeParams params) async {
    return await repository.verifyResetCode(
      mobile: params.mobile,
      code: params.code,
    );
  }
}

class VerifyResetCodeParams extends Equatable {
  final String mobile;
  final String code;

  const VerifyResetCodeParams({
    required this.mobile,
    required this.code,
  });

  @override
  List<Object> get props => [mobile, code];
}
