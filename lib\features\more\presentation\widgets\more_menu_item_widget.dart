// features/more/presentation/widgets/more_menu_item_widget.dart
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:tripooo_user/core/theme/theme.dart';
import 'package:tripooo_user/core/utils/responsive_extensions.dart';

class SimpleMenuItem extends StatelessWidget {
  final String iconAsset; // بدل IconData
  final String title;
  final String? subtitle;
  final VoidCallback onTap;
  final bool isTablet;
  final bool isLogout;

  const SimpleMenuItem({
    super.key,
    required this.iconAsset,
    required this.title,
    this.subtitle,
    required this.onTap,
    required this.isTablet,
    this.isLogout = false,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.only(bottom: isTablet ? 16 : 12),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onTap,
          borderRadius: BorderRadius.circular(8),
          child: Padding(
            padding: EdgeInsets.symmetric(
              horizontal: isTablet ? 20 : 16,
              vertical: isTablet ? 16 : 12,
            ),
            child: Row(
              children: [
                CircleAvatar(
                  radius: 24.h(context),
                  backgroundColor: Color(0xffF6F6F6),
                  child: SvgPicture.asset(
                    iconAsset,
                    width: 24.h(context),
                    height: 24.h(context),
                    color: isLogout ? AppColors.error : AppColors.textPrimary,
                  ),
                ),

                SizedBox(width: 12.w(context)),

                Expanded(
                  child: Row(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      Text(
                        title,
                        style: AppTextStyles.bodyMedium.copyWith(
                          fontWeight: FontWeight.w500,
                          color: isLogout
                              ? AppColors.error
                              : AppColors.textPrimary,
                          fontSize: isTablet ? 18 : 16,
                        ),
                      ),
                      Spacer(),
                      if (subtitle != null) ...[
                        Text(
                          subtitle!,
                          style: AppTextStyles.bodySmall.copyWith(
                            color: AppColors.primary,
                          ),
                        ),
                      ],
                      SizedBox(width: 8.w(context)),
                    ],
                  ),
                ),

                Icon(
                  Icons.arrow_forward_ios,
                  color: Colors.black,
                  size: 20.w(context),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
