// features/wallet/presentation/pages/payment_method_screen.dart
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:tripooo_user/core/theme/theme.dart';
import 'package:tripooo_user/core/routing/app_router.dart';

class PaymentMethodScreen extends StatefulWidget {
  final String? amount;
  const PaymentMethodScreen({super.key, this.amount});

  @override
  State<PaymentMethodScreen> createState() => _PaymentMethodScreenState();
}

class _PaymentMethodScreenState extends State<PaymentMethodScreen> {
  final TextEditingController cardNumber = TextEditingController();
  final TextEditingController expiry = TextEditingController();
  final TextEditingController cvc = TextEditingController();
  bool saveCard = false;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        elevation: 0,
        backgroundColor: Colors.white,
        foregroundColor: Colors.black,
        title: Text('طريقة الدفع', style: AppTextStyles.appBarTitle),
      ),
      body: ListView(
        padding: const EdgeInsets.all(16),
        children: [
          Text('أدخل تفاصيل البطاقة', style: AppTextStyles.fieldLabel),
          const SizedBox(height: 8),
          TextField(
            controller: cardNumber,
            keyboardType: TextInputType.number,
            decoration: InputDecoration(
              hintText: 'Card Number',
              suffixIcon: const Icon(Icons.photo_camera_outlined),
              border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
            ),
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              Expanded(
                child: TextField(
                  controller: expiry,
                  keyboardType: TextInputType.datetime,
                  decoration: InputDecoration(
                    hintText: 'MM/YY',
                    border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
                  ),
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: TextField(
                  controller: cvc,
                  keyboardType: TextInputType.number,
                  decoration: InputDecoration(
                    hintText: 'CVC',
                    border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              Switch(value: saveCard, onChanged: (v) => setState(() => saveCard = v)),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  'حفظ البطاقة لوقت لاحق\nسيتم تسريع الدفع لاحقًا وتذكر تفاصيلك',
                  style: AppTextStyles.bodySmall,
                ),
              ),
            ],
          ),
        ],
      ),
      bottomNavigationBar: SafeArea(
        minimum: const EdgeInsets.all(16),
        child: SizedBox(
          height: 52,
          child: ElevatedButton(
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.primary,
              shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
            ),
            onPressed: () => context.push(AppRoutes.walletTopupSuccess, extra: widget.amount ?? '0'),
            child: Text('متابعة', style: AppTextStyles.buttonText),
          ),
        ),
      ),
    );
  }
}

