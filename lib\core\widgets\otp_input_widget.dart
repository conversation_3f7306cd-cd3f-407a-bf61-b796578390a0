// core/widgets/otp_input_widget.dart
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

class OtpInputWidget extends StatefulWidget {
  final int length;
  final Function(String) onCompleted;
  final Function(String)? onChanged;
  final double fieldWidth;
  final double fieldHeight;
  final double spacing;
  final TextStyle? textStyle;
  final InputDecoration? decoration;
  final bool isRtl;
  final bool autoFocus;
  final TextInputType keyboardType;

  const OtpInputWidget({
    super.key,
    required this.length,
    required this.onCompleted,
    this.onChanged,
    this.fieldWidth = 44.0,
    this.fieldHeight = 65.0,
    this.spacing = 6.0,
    this.textStyle,
    this.decoration,
    this.isRtl = true,
    this.autoFocus = true,
    this.keyboardType = TextInputType.number,
  });

  @override
  State<OtpInputWidget> createState() => _OtpInputWidgetState();
}

class _OtpInputWidgetState extends State<OtpInputWidget> {
  late List<FocusNode> _focusNodes;
  late List<TextEditingController> _controllers;

  @override
  void initState() {
    super.initState();
    _focusNodes = List.generate(widget.length, (_) => FocusNode());
    _controllers = List.generate(widget.length, (_) => TextEditingController());

    if (widget.autoFocus && _focusNodes.isNotEmpty) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (widget.isRtl) {
          _focusNodes.last.requestFocus();
        } else {
          _focusNodes.first.requestFocus();
        }
      });
    }
  }

  @override
  void dispose() {
    for (var node in _focusNodes) {
      node.dispose();
    }
    for (var controller in _controllers) {
      controller.dispose();
    }
    super.dispose();
  }

  String get _code => widget.isRtl
      ? _controllers.reversed.map((c) => c.text).join()
      : _controllers.map((c) => c.text).join();

  void _onCodeChanged(int idx, String value) {
    if (value.isEmpty) return;

    // always override with last character
    final newChar = value[value.length - 1];
    _controllers[idx].text = newChar;
    _controllers[idx].selection = TextSelection.collapsed(offset: 1);

    // Move focus
    if (widget.isRtl) {
      if (idx > 0) {
        FocusScope.of(context).requestFocus(_focusNodes[idx - 1]);
      }
    } else {
      if (idx < widget.length - 1) {
        FocusScope.of(context).requestFocus(_focusNodes[idx + 1]);
      }
    }

    final currentCode = _code;
    if (currentCode.length == widget.length) {
      widget.onCompleted(currentCode);
    }
    if (widget.onChanged != null) {
      widget.onChanged!(currentCode);
    }

    setState(() {});
  }

  void _handleBackspace(int idx) {
    if (_controllers[idx].text.isNotEmpty) {
      _controllers[idx].clear();
    } else {
      if (widget.isRtl) {
        if (idx < widget.length - 1) {
          _controllers[idx + 1].clear();
          FocusScope.of(context).requestFocus(_focusNodes[idx + 1]);
        }
      } else {
        if (idx > 0) {
          _controllers[idx - 1].clear();
          FocusScope.of(context).requestFocus(_focusNodes[idx - 1]);
        }
      }
    }

    Future.delayed(Duration(milliseconds: 10), () {
      final currentCode = _code;
      if (widget.onChanged != null) {
        widget.onChanged!(currentCode);
      }
      setState(() {});
    });
  }

  InputDecoration get _defaultDecoration => InputDecoration(
    counterText: '',
    border: OutlineInputBorder(
      borderRadius: BorderRadius.circular(8),
      borderSide: BorderSide(color: Colors.grey[300]!),
    ),
    focusedBorder: OutlineInputBorder(
      borderRadius: BorderRadius.circular(8),
      borderSide: BorderSide(color: Colors.blue, width: 2),
    ),
    enabledBorder: OutlineInputBorder(
      borderRadius: BorderRadius.circular(8),
      borderSide: BorderSide(color: Colors.grey[300]!),
    ),
  );

  TextStyle get _defaultTextStyle =>
      TextStyle(fontSize: 22, fontWeight: FontWeight.bold);

  @override
  Widget build(BuildContext context) {
    return Directionality(
      textDirection: widget.isRtl ? TextDirection.rtl : TextDirection.ltr,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: List.generate(widget.length, (idx) {
          return Container(
            width: widget.fieldWidth,
            height: widget.fieldHeight,
            margin: EdgeInsets.symmetric(horizontal: widget.spacing),
            child: Focus(
              onKey: (node, event) {
                if (event is RawKeyDownEvent &&
                    event.logicalKey == LogicalKeyboardKey.backspace) {
                  _handleBackspace(idx);
                  return KeyEventResult.handled;
                }
                return KeyEventResult.ignored;
              },
              child: TextField(
                controller: _controllers[idx],
                focusNode: _focusNodes[idx],
                keyboardType: widget.keyboardType,
                textAlign: TextAlign.center,
                maxLength: 1,
                style: widget.textStyle ?? _defaultTextStyle,
                decoration: widget.decoration ?? _defaultDecoration,
                inputFormatters: [FilteringTextInputFormatter.digitsOnly],
                onChanged: (v) => _onCodeChanged(idx, v),
                onTap: () {
                  _controllers[idx].selection = TextSelection(
                    baseOffset: 0,
                    extentOffset: _controllers[idx].text.length,
                  );
                },
              ),
            ),
          );
        }),
      ),
    );
  }

  // Public method to clear all fields
  void clear() {
    for (var controller in _controllers) {
      controller.clear();
    }
    if (widget.autoFocus) {
      if (widget.isRtl) {
        _focusNodes.last.requestFocus();
      } else {
        _focusNodes.first.requestFocus();
      }
    }
  }

  // Public method to get current code
  String getCurrentCode() => _code;
}
