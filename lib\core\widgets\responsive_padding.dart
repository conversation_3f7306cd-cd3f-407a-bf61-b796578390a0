// core/widgets/responsive_padding.dart
import 'package:flutter/material.dart';
import '../utils/responsive_extensions.dart';

class ResponsivePadding extends StatelessWidget {
  final Widget child;
  const ResponsivePadding({required this.child, super.key});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.symmetric(
        horizontal: 16.w(context),
        vertical: 8.h(context),
      ),
      child: child,
    );
  }
}
