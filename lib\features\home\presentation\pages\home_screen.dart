// features/home/<USER>/pages/home_screen.dart
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:go_router/go_router.dart';
import 'package:tripooo_user/core/theme/theme.dart';

import 'package:tripooo_user/core/routing/app_router.dart';
import 'package:tripooo_user/core/utils/responsive_extensions.dart';
import 'package:tripooo_user/core/utils/widget_extensions.dart';
import 'package:tripooo_user/features/onboarding/utils/responsive_helper.dart';
import '../../../Auth/presentation/cubit/auth_cubit.dart';
import '../../../guides/presentation/cubit/featured_guides_cubit.dart';
import '../../../../core/di/injection_container.dart' as di;
import '../widgets/widgets.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  @override
  void initState() {
    super.initState();
    // No need to load user data in Guest Mode
    // User data will be loaded when needed (e.g., when user logs in)
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    // Rebuild when language changes
    setState(() {});
  }

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => di.sl<FeaturedGuidesCubit>(),
      child: Scaffold(
        backgroundColor: AppColors.background,
        body: BlocListener<AuthCubit, AuthState>(
          listener: (context, state) async {
            if (state is AuthLogoutSuccess) {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text('logout_successful'.tr()),
                  backgroundColor: AppColors.success,
                ),
              );
              // Small delay to ensure logout is complete
              await Future.delayed(const Duration(milliseconds: 100));
              if (context.mounted) {
                context.go(AppRoutes.login);
              }
            }
          },
          child: BlocBuilder<AuthCubit, AuthState>(
            builder: (context, state) {
              if (state is AuthLoading) {
                return Center(
                  child: CircularProgressIndicator(
                    color: AppColors.primary,
                    strokeWidth: ResponsiveHelper.isTablet(context) ? 4.0 : 3.0,
                  ),
                );
              }

              // Always show home content regardless of auth state (Guest Mode support)
              return _buildHomeContent(context, state);
            },
          ),
        ),
        bottomNavigationBar: const BottomNavigationWidget(),
      ),
    );
  }

  Widget _buildHomeContent(BuildContext context, AuthState state) {
    final isTablet = ResponsiveHelper.isTablet(context);
    final isLandscape = ResponsiveHelper.isLandscape(context);

    return SingleChildScrollView(
      child: Center(
        child: ConstrainedBox(
          constraints: BoxConstraints(
            maxWidth: isTablet ? 1200.w(context) : double.infinity,
          ),
          child:
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const HomeHeaderWidget(),

                  // const FeaturedGuideCardWidget(),
                  const ServicesSectionWidget(),

                  ResponsiveHelper.getSpacing(
                    context,
                    type: isLandscape ? 'medium' : 'large',
                  ).verticalSpaceResponsive(context),

                  const DestinationsSectionWidget(),

                  ResponsiveHelper.getSpacing(
                    context,
                    type: isLandscape ? 'medium' : 'large',
                  ).verticalSpaceResponsive(context),

                  const FeaturedGuidesSectionWidget(),

                  ResponsiveHelper.getSpacing(
                    context,
                    type: isLandscape ? 'large' : 'xlarge',
                  ).verticalSpaceResponsive(context),
                ],
              ).paddingSymmetric(
                horizontal: isTablet ? 48 : 24,
                vertical: isTablet ? 32 : 16,
                context: context,
              ),
        ),
      ),
    );
  }
}
