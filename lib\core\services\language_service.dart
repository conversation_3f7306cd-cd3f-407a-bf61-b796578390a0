// core/services/language_service.dart
import 'package:flutter/material.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:shared_preferences/shared_preferences.dart';

class LanguageService {
  static const String _languageKey = 'selected_language';
  static const String _defaultLanguage = 'ar';
  
  static Future<void> initializeLanguage() async {
    final prefs = await SharedPreferences.getInstance();
    final savedLanguage = prefs.getString(_languageKey);
    
    // If no language is saved, set Arabic as default
    if (savedLanguage == null) {
      await prefs.setString(_languageKey, _defaultLanguage);
    }
  }
  
  static Future<String> getSavedLanguage() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString(_languageKey) ?? _defaultLanguage;
  }
  
  static Future<void> saveLanguage(String languageCode) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(_languageKey, languageCode);
  }
  
  static Future<void> changeLanguage(BuildContext context, String languageCode) async {
    await saveLanguage(languageCode);
    await context.setLocale(Locale(languageCode));
  }
  
  static Locale getDefaultLocale() {
    return const Locale(_defaultLanguage);
  }
  
  static Future<Locale> getSavedLocale() async {
    final languageCode = await getSavedLanguage();
    return Locale(languageCode);
  }
  
  static bool isRTL(String languageCode) {
    return languageCode == 'ar';
  }
}
