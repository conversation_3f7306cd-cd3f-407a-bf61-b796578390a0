# Onboarding Feature

A fully responsive onboarding system for the Tripooo app with beautiful animations and adaptive design.

## Features

✅ **Responsive Design**
- Adapts to different screen sizes (mobile, tablet)
- Optimized for both portrait and landscape orientations
- Dynamic font sizes and spacing based on device type

✅ **Beautiful UI**
- Smooth page transitions with PageView
- Animated page indicators
- Gradient overlays on background images
- Curved bottom containers for content

✅ **Accessibility**
- Proper text scaling
- Touch-friendly button sizes
- Clear visual hierarchy

✅ **Customizable**
- Easy to modify content through OnboardingData
- Configurable colors and dimensions
- Debug mode for development

## Structure

```
lib/features/onboarding/
├── models/
│   └── onboarding_model.dart      # Data models for onboarding pages
├── screens/
│   └── onboarding_screen.dart     # Main onboarding screen
├── widgets/
│   ├── onboarding_page.dart       # Individual page widget
│   └── page_indicators.dart       # Animated page indicators
├── utils/
│   └── responsive_helper.dart     # Responsive design utilities
├── demo/
│   └── onboarding_demo.dart       # Demo app for testing
└── onboarding.dart                # Feature exports
```

## Usage

### Basic Implementation

```dart
import 'package:tripooo_user/features/onboarding/onboarding.dart';

// Navigate to onboarding
Navigator.push(
  context,
  MaterialPageRoute(builder: (context) => const OnboardingScreen()),
);
```

### Customizing Content

Edit the `OnboardingData.pages` in `models/onboarding_model.dart`:

```dart
static const List<OnboardingModel> pages = [
  OnboardingModel(
    image: 'assets/images/onboarding/your_image.png',
    title: 'Your Title',
    description: 'Your description text here.',
    buttonText: 'Next',
  ),
  // Add more pages...
];
```

### Responsive Customization

Use `ResponsiveHelper` for custom responsive behavior:

```dart
final isTablet = ResponsiveHelper.isTablet(context);
final fontSize = ResponsiveHelper.getTitleFontSize(context);
final spacing = ResponsiveHelper.getSpacing(context, type: 'large');
```

## Design Specifications

### Colors
- Primary: `AppColors.primary` (Blue #007AFF)
- Background: `AppColors.white`
- Text: `AppColors.textPrimary` (Black)
- Indicators: `AppColors.primary` (active), `AppColors.textTertiary` (inactive)

### Typography
- **Title**: 30px (mobile), 36px (tablet), weight 700
- **Description**: 16px (mobile), 18px (tablet), weight 400
- **Button**: 16px, weight 700

### Spacing
- **Mobile**: 16-24px padding, 16-32px spacing
- **Tablet**: 32-48px padding, 24-48px spacing

### Animations
- Page transitions: 300ms ease-in-out
- Indicator animations: 300ms duration
- Smooth scaling for responsive changes

## Assets Required

Place your onboarding images in `assets/images/onboarding/`:
- `onboarding_1.png` - Hot air balloons scene
- `onboarding_2.png` - Second onboarding image  
- `onboarding_3.png` - Third onboarding image

### Image Requirements
- **Format**: PNG or JPG
- **Mobile**: 375x600px minimum
- **Tablet**: 768x1024px minimum
- **File Size**: Under 500KB each

## Testing

Run the demo app:

```bash
flutter run lib/features/onboarding/demo/onboarding_demo.dart
```

## Integration

To integrate with your main app:

1. Add onboarding images to `assets/images/onboarding/`
2. Update `pubspec.yaml` to include the assets
3. Import and use `OnboardingScreen` in your app flow
4. Handle navigation after onboarding completion

```dart
void _finishOnboarding() {
  // Navigate to main app
  Navigator.pushReplacementNamed(context, '/home');
  
  // Or save onboarding completion status
  SharedPreferences.getInstance().then((prefs) {
    prefs.setBool('onboarding_completed', true);
  });
}
```

## Debug Mode

Toggle debug mode in `OnboardingScreen`:

```dart
bool _isDebugMode = true; // Set to false in production
```

This shows a red "DEBUG" badge in the top-right corner during development.

## Responsive Breakpoints

- **Mobile**: < 600px width
- **Tablet**: ≥ 600px width
- **Landscape**: width > height
- **Small Screen**: < 700px height

The system automatically adapts layouts, fonts, and spacing based on these breakpoints.
