// core/services/fcm_helper.dart
import 'dart:developer';

import '../di/injection_container.dart';
import '../../features/Auth/data/datasources/auth_local_data_source.dart';
import 'fcm_service.dart';

class FCMHelper {
  static final AuthLocalDataSource _localDataSource = sl<AuthLocalDataSource>();

  /// Get FCM token and save it locally
  static Future<String?> getAndSaveFCMToken() async {
    try {
      // Get FCM token from Firebase
      String? fcmToken = await FCMService.getToken();

      if (fcmToken != null) {
        // Save token locally
        await _localDataSource.saveFcmToken(fcmToken);
        return fcmToken;
      }

      return null;
    } catch (e) {
      log('Error getting and saving FCM token: $e');
      return null;
    }
  }

  /// Get saved FCM token from local storage
  static Future<String?> getSavedFCMToken() async {
    try {
      return await _localDataSource.getFcmToken();
    } catch (e) {
      log('Error getting saved FCM token: $e');
      return null;
    }
  }

  /// Get FCM token (try saved first, then fetch new)
  static Future<String?> getFCMToken() async {
    try {
      // First try to get saved token
      String? savedToken = await getSavedFCMToken();

      if (savedToken != null && savedToken.isNotEmpty) {
        return savedToken;
      }

      // If no saved token, get new one
      return await getAndSaveFCMToken();
    } catch (e) {
      log('Error getting FCM token: $e');
      return null;
    }
  }

  /// Refresh FCM token and save it
  static Future<String?> refreshFCMToken() async {
    try {
      // Get new token from Firebase
      String? newToken = await FCMService.getToken();

      if (newToken != null) {
        // Save new token locally
        await _localDataSource.saveFcmToken(newToken);
        return newToken;
      }

      return null;
    } catch (e) {
      log('Error refreshing FCM token: $e');
      return null;
    }
  }

  /// Setup token refresh listener
  static void setupTokenRefreshListener() {
    FCMService.onTokenRefresh((newToken) async {
      try {
        // Save new token when it refreshes
        await _localDataSource.saveFcmToken(newToken);
        log('FCM token refreshed and saved: $newToken');
      } catch (e) {
        log('Error saving refreshed FCM token: $e');
      }
    });
  }
}
