// core/utils/either_extensions.dart
import 'package:dartz/dartz.dart';

/// Extension methods for Either to make it easier to work with left/right values
extension EitherExtensions<L, R> on Either<L, R> {
  /// Returns true if this is a Left value
  bool get isLeft => fold((_) => true, (_) => false);

  /// Returns true if this is a Right value
  bool get isRight => fold((_) => false, (_) => true);

  /// Gets the left value if it exists, otherwise returns null
  L? get leftOrNull => fold((l) => l, (_) => null);

  /// Gets the right value if it exists, otherwise returns null
  R? get rightOrNull => fold((_) => null, (r) => r);

  /// Gets the left value or throws an exception if this is a Right
  L get leftValue =>
      fold((l) => l, (_) => throw Exception('Called leftValue on Right'));

  /// Gets the right value or throws an exception if this is a Left
  R get rightValue =>
      fold((_) => throw Exception('Called rightValue on Left'), (r) => r);

  /// Maps the right value if it exists
  Either<L, T> mapRight<T>(T Function(R) mapper) {
    return fold((l) => Left(l), (r) => Right(mapper(r)));
  }

  /// Maps the left value if it exists
  Either<T, R> mapLeft<T>(T Function(L) mapper) {
    return fold((l) => Left(mapper(l)), (r) => Right(r));
  }

  /// Executes a function if this is a Right value
  Either<L, R> onRight(void Function(R) action) {
    fold((_) {}, action);
    return this;
  }

  /// Executes a function if this is a Left value
  Either<L, R> onLeft(void Function(L) action) {
    fold(action, (_) {});
    return this;
  }
}

/// Utility functions for working with Either
class EitherUtils {
  /// Creates a Right value
  static Either<L, R> right<L, R>(R value) => Right(value);

  /// Creates a Left value
  static Either<L, R> left<L, R>(L value) => Left(value);

  /// Converts a nullable value to Either
  /// If value is null, returns Left with the provided error
  /// Otherwise returns Right with the value
  static Either<L, R> fromNullable<L, R>(R? value, L error) {
    return value != null ? Right(value) : Left(error);
  }

  /// Tries to execute a function and returns Either
  /// If the function throws, returns Left with the exception
  /// Otherwise returns Right with the result
  static Either<Exception, R> tryCatch<R>(R Function() function) {
    try {
      return Right(function());
    } catch (e) {
      return Left(e is Exception ? e : Exception(e.toString()));
    }
  }

  /// Combines multiple Either values
  /// Returns Right with a list of all right values if all are Right
  /// Returns Left with the first left value if any is Left
  static Either<L, List<R>> sequence<L, R>(List<Either<L, R>> eithers) {
    final results = <R>[];
    for (final either in eithers) {
      // Use fold directly instead of creating intermediate Either
      final leftValue = either.fold<L?>((l) => l, (r) => null);

      if (leftValue != null) {
        return Left(leftValue);
      }

      final rightValue = either.fold<R?>((l) => null, (r) => r);

      if (rightValue != null) {
        results.add(rightValue);
      }
    }
    return Right(results);
  }
}

/// Example usage of Either with dartz for authentication
class AuthExamples {
  /// Example of using Either for validation
  static Either<String, String> validateEmail(String email) {
    if (email.isEmpty) {
      return EitherUtils.left('Email cannot be empty');
    }
    if (!email.contains('@')) {
      return EitherUtils.left('Invalid email format');
    }
    return EitherUtils.right(email);
  }

  /// Example of using Either for password validation
  static Either<String, String> validatePassword(String password) {
    if (password.isEmpty) {
      return EitherUtils.left('Password cannot be empty');
    }
    if (password.length < 6) {
      return EitherUtils.left('Password must be at least 6 characters');
    }
    return EitherUtils.right(password);
  }

  /// Example of combining validations
  static Either<String, Map<String, String>> validateLoginForm(
    String email,
    String password,
  ) {
    final emailValidation = validateEmail(email);
    final passwordValidation = validatePassword(password);

    // Using fold to handle left/right
    return emailValidation.fold(
      (emailError) => EitherUtils.left(emailError),
      (validEmail) => passwordValidation.fold(
        (passwordError) => EitherUtils.left(passwordError),
        (validPassword) =>
            EitherUtils.right({'email': validEmail, 'password': validPassword}),
      ),
    );
  }

  /// Example of using Either with async operations
  static Future<Either<String, String>> simulateApiCall(String data) async {
    await Future.delayed(Duration(seconds: 1));

    if (data.isEmpty) {
      return EitherUtils.left('Data cannot be empty');
    }

    // Simulate random failure
    if (DateTime.now().millisecond % 2 == 0) {
      return EitherUtils.left('Network error');
    }

    return EitherUtils.right('Success: $data processed');
  }
}
