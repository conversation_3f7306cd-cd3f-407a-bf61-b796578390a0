// features/destinations/presentation/pages/destinations_test_page.dart
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:tripooo_user/core/theme/theme.dart';
import 'package:tripooo_user/core/utils/responsive_extensions.dart';
import 'package:tripooo_user/core/utils/widget_extensions.dart';
import 'package:tripooo_user/features/onboarding/utils/responsive_helper.dart';
import 'package:tripooo_user/core/di/injection_container.dart' as di;
import '../cubit/destinations_cubit.dart';
import '../../../home/<USER>/widgets/destinations_section_widget.dart';

class DestinationsTestPage extends StatelessWidget {
  const DestinationsTestPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: AppBar(
        title: Text('اختبار الوجهات السياحية'),
        backgroundColor: AppColors.primary,
        foregroundColor: AppColors.white,
      ),
      body: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Test Section Widget
            ResponsiveHelper.getSpacing(context, type: 'large').verticalSpaceResponsive(context),
            
            Text(
              'اختبار DestinationsSectionWidget',
              style: AppTextStyles.heading.copyWith(
                fontSize: ResponsiveHelper.isTablet(context) ? 20 : 18,
              ),
            ).paddingSymmetric(
              horizontal: ResponsiveHelper.isTablet(context) ? 32 : 20,
              context: context,
            ),
            
            ResponsiveHelper.getSpacing(context, type: 'medium').verticalSpaceResponsive(context),
            
            const DestinationsSectionWidget(),
            
            ResponsiveHelper.getSpacing(context, type: 'large').verticalSpaceResponsive(context),
            
            // Manual Test Section
            Text(
              'اختبار يدوي للـ API',
              style: AppTextStyles.heading.copyWith(
                fontSize: ResponsiveHelper.isTablet(context) ? 20 : 18,
              ),
            ).paddingSymmetric(
              horizontal: ResponsiveHelper.isTablet(context) ? 32 : 20,
              context: context,
            ),
            
            ResponsiveHelper.getSpacing(context, type: 'medium').verticalSpaceResponsive(context),
            
            _buildManualTestSection(context),
            
            ResponsiveHelper.getSpacing(context, type: 'xlarge').verticalSpaceResponsive(context),
          ],
        ),
      ),
    );
  }

  Widget _buildManualTestSection(BuildContext context) {
    return BlocProvider(
      create: (context) => di.sl<DestinationsCubit>(),
      child: BlocBuilder<DestinationsCubit, DestinationsState>(
        builder: (context, state) {
          return Column(
            children: [
              // Control Buttons
              _buildControlButtons(context),
              
              ResponsiveHelper.getSpacing(context, type: 'medium').verticalSpaceResponsive(context),
              
              // Results
              _buildResults(context, state),
            ],
          ).paddingSymmetric(
            horizontal: ResponsiveHelper.isTablet(context) ? 32 : 20,
            context: context,
          );
        },
      ),
    );
  }

  Widget _buildControlButtons(BuildContext context) {
    return Wrap(
      spacing: 8,
      runSpacing: 8,
      children: [
        ElevatedButton(
          onPressed: () {
            context.read<DestinationsCubit>().loadDestinations();
          },
          style: ElevatedButton.styleFrom(
            backgroundColor: AppColors.primary,
            foregroundColor: AppColors.white,
          ),
          child: Text('تحميل الوجهات'),
        ),
        ElevatedButton(
          onPressed: () {
            context.read<DestinationsCubit>().loadDestinations(
              countryId: 1,
              limit: 5,
            );
          },
          style: ElevatedButton.styleFrom(
            backgroundColor: AppColors.secondary,
            foregroundColor: AppColors.white,
          ),
          child: Text('فلترة حسب البلد'),
        ),
        ElevatedButton(
          onPressed: () {
            context.read<DestinationsCubit>().loadDestinations(
              priceFrom: 100,
              priceTo: 500,
              limit: 5,
            );
          },
          style: ElevatedButton.styleFrom(
            backgroundColor: AppColors.success,
            foregroundColor: AppColors.white,
          ),
          child: Text('فلترة حسب السعر'),
        ),
        ElevatedButton(
          onPressed: () {
            context.read<DestinationsCubit>().refreshDestinations();
          },
          style: ElevatedButton.styleFrom(
            backgroundColor: AppColors.info,
            foregroundColor: AppColors.white,
          ),
          child: Text('تحديث'),
        ),
        ElevatedButton(
          onPressed: () {
            context.read<DestinationsCubit>().resetState();
          },
          style: ElevatedButton.styleFrom(
            backgroundColor: AppColors.error,
            foregroundColor: AppColors.white,
          ),
          child: Text('إعادة تعيين'),
        ),
      ],
    );
  }

  Widget _buildResults(BuildContext context, DestinationsState state) {
    if (state is DestinationsInitial) {
      return Container(
        height: 200.h(context),
        decoration: BoxDecoration(
          border: Border.all(color: AppColors.primary.withOpacity(0.3)),
          borderRadius: BorderRadius.circular(AppDimensions.radiusMedium),
        ),
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.play_arrow,
                size: 48,
                color: AppColors.primary,
              ),
              8.verticalSpace,
              Text(
                'اضغط على أحد الأزرار لاختبار الـ API',
                style: AppTextStyles.bodyMedium.copyWith(
                  color: AppColors.primary,
                ),
              ),
            ],
          ),
        ),
      );
    } else if (state is DestinationsLoading) {
      return Container(
        height: 200.h(context),
        decoration: BoxDecoration(
          border: Border.all(color: AppColors.info.withOpacity(0.3)),
          borderRadius: BorderRadius.circular(AppDimensions.radiusMedium),
        ),
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              CircularProgressIndicator(color: AppColors.info),
              16.verticalSpace,
              Text(
                'جاري تحميل الوجهات...',
                style: AppTextStyles.bodyMedium.copyWith(
                  color: AppColors.info,
                ),
              ),
            ],
          ),
        ),
      );
    } else if (state is DestinationsLoaded) {
      return Container(
        decoration: BoxDecoration(
          border: Border.all(color: AppColors.success.withOpacity(0.3)),
          borderRadius: BorderRadius.circular(AppDimensions.radiusMedium),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Container(
              width: double.infinity,
              padding: EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: AppColors.success.withOpacity(0.1),
                borderRadius: BorderRadius.vertical(
                  top: Radius.circular(AppDimensions.radiusMedium),
                ),
              ),
              child: Text(
                'تم تحميل ${state.destinations.length} وجهة بنجاح ✅',
                style: AppTextStyles.labelMedium.copyWith(
                  color: AppColors.success,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
            ListView.separated(
              shrinkWrap: true,
              physics: NeverScrollableScrollPhysics(),
              itemCount: state.destinations.length,
              separatorBuilder: (context, index) => Divider(height: 1),
              itemBuilder: (context, index) {
                final destination = state.destinations[index];
                return ListTile(
                  leading: CircleAvatar(
                    backgroundColor: AppColors.primary.withOpacity(0.1),
                    child: Text(
                      '${destination.id}',
                      style: AppTextStyles.bodySmall.copyWith(
                        color: AppColors.primary,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                  title: Text(
                    destination.displayName,
                    style: AppTextStyles.bodyMedium.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  subtitle: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(destination.displayLocation),
                      4.verticalSpace,
                      Text(
                        destination.formattedPrice,
                        style: AppTextStyles.bodySmall.copyWith(
                          color: AppColors.success,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                  trailing: Icon(
                    destination.isActive ? Icons.check_circle : Icons.cancel,
                    color: destination.isActive ? AppColors.success : AppColors.error,
                  ),
                );
              },
            ),
          ],
        ),
      );
    } else if (state is DestinationsError) {
      return Container(
        height: 200.h(context),
        decoration: BoxDecoration(
          border: Border.all(color: AppColors.error.withOpacity(0.3)),
          borderRadius: BorderRadius.circular(AppDimensions.radiusMedium),
        ),
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.error_outline,
                size: 48,
                color: AppColors.error,
              ),
              8.verticalSpace,
              Text(
                'خطأ في تحميل الوجهات',
                style: AppTextStyles.bodyMedium.copyWith(
                  color: AppColors.error,
                  fontWeight: FontWeight.bold,
                ),
              ),
              4.verticalSpace,
              Text(
                'الرسالة: ${state.message}',
                style: AppTextStyles.bodySmall.copyWith(
                  color: AppColors.textSecondary,
                ),
                textAlign: TextAlign.center,
              ),
              4.verticalSpace,
              Text(
                'كود الخطأ: ${state.statusCode}',
                style: AppTextStyles.bodySmall.copyWith(
                  color: AppColors.textSecondary,
                ),
              ),
            ],
          ),
        ),
      );
    }
    
    return Container();
  }
}
