// features/onboarding/screens/onboarding_screen.dart
import 'package:flutter/material.dart';
import 'package:tripooo_user/core/theme/theme.dart';
import 'package:tripooo_user/core/widgets/custom_button.dart';
import '../models/onboarding_model.dart';
import '../widgets/onboarding_page.dart';
import '../widgets/page_indicators.dart';

class OnboardingScreen extends StatefulWidget {
  const OnboardingScreen({super.key});

  @override
  State<OnboardingScreen> createState() => _OnboardingScreenState();
}

class _OnboardingScreenState extends State<OnboardingScreen> {
  final PageController _pageController = PageController();
  int _currentIndex = 0;
  final bool _isDebugMode = true; // Set to false in production

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  void _nextPage() {
    if (_currentIndex < OnboardingData.pages.length - 1) {
      _pageController.nextPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    } else {
      _finishOnboarding();
    }
  }

  void _finishOnboarding() {
    // Navigate to main app or login screen
    // Navigator.pushReplacementNamed(context, '/login');
    ScaffoldMessenger.of(
      context,
    ).showSnackBar(const SnackBar(content: Text('Onboarding completed!')));
  }

  void _skipOnboarding() {
    _finishOnboarding();
  }

  @override
  Widget build(BuildContext context) {
    final screenSize = MediaQuery.of(context).size;
    final isTablet = screenSize.width > 600;
    final isLandscape = screenSize.width > screenSize.height;

    return Scaffold(
      body: SafeArea(
        child: Stack(
          children: [
            // PageView
            PageView.builder(
              controller: _pageController,
              onPageChanged: (index) {
                setState(() {
                  _currentIndex = index;
                });
              },
              itemCount: OnboardingData.pages.length,
              itemBuilder: (context, index) {
                return OnboardingPage(
                  model: OnboardingData.pages[index],
                  isDebugMode: _isDebugMode,
                );
              },
            ),

            // Skip Button
            if (_currentIndex < OnboardingData.pages.length - 1)
              Positioned(
                top: isTablet ? 20 : 16,
                left: isTablet ? 24 : 16,
                child: _buildSkipButton(isTablet),
              ),

            // Bottom Content (Button and Indicators)
            Positioned(
              bottom: 0,
              left: 0,
              right: 0,
              child: _buildBottomContent(isTablet, isLandscape),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSkipButton(bool isTablet) {
    return TextButton(
      onPressed: _skipOnboarding,
      style: TextButton.styleFrom(
        padding: EdgeInsets.symmetric(
          horizontal: isTablet ? 20 : 16,
          vertical: isTablet ? 12 : 8,
        ),
        backgroundColor: AppColors.white.withValues(alpha: 0.9),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(AppDimensions.radiusMedium),
        ),
      ),
      child: Text(
        'Skip',
        style: isTablet
            ? AppTextStyles.labelLarge.copyWith(fontSize: 16)
            : AppTextStyles.labelMedium,
      ),
    );
  }

  Widget _buildBottomContent(bool isTablet, bool isLandscape) {
    return Container(
      padding: EdgeInsets.symmetric(
        horizontal: isTablet
            ? AppDimensions.paddingXLarge * 2
            : AppDimensions.paddingLarge,
        vertical: isTablet
            ? AppDimensions.paddingXLarge
            : AppDimensions.paddingLarge,
      ),
      decoration: const BoxDecoration(color: AppColors.white),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Page Indicators
          PageIndicators(
            currentIndex: _currentIndex,
            totalPages: OnboardingData.pages.length,
          ),

          SizedBox(
            height: isTablet
                ? AppDimensions.spacing24
                : AppDimensions.spacing20,
          ),

          // Next/Get Started Button
          SizedBox(
            width: double.infinity,
            height: isTablet ? 60 : 50,
            child: CustomButton(
              text: OnboardingData.pages[_currentIndex].buttonText ?? 'Next',
              onPressed: _nextPage,
              type: ButtonType.elevated,
              size: isTablet ? ButtonSize.large : ButtonSize.medium,
              isFullWidth: true,
            ),
          ),

          // Bottom safe area
          SizedBox(height: MediaQuery.of(context).padding.bottom),
        ],
      ),
    );
  }
}
