// features/guides/presentation/pages/featured_guide_details_page.dart
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:tripooo_user/core/theme/theme.dart';
import 'package:tripooo_user/core/utils/responsive_extensions.dart';
import 'package:tripooo_user/core/utils/widget_extensions.dart';
import 'package:tripooo_user/features/onboarding/utils/responsive_helper.dart';
import 'package:tripooo_user/core/di/injection_container.dart' as di;
import '../cubit/featured_guides_cubit.dart';
import '../cubit/guides_state.dart';
import '../../domain/entities/guide_entity.dart';

class FeaturedGuideDetailsPage extends StatelessWidget {
  final int guideId;
  final String? guideName;

  const FeaturedGuideDetailsPage({
    super.key,
    required this.guideId,
    this.guideName,
  });

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => di.sl<FeaturedGuidesCubit>()..getGuideById(guideId),
      child: Scaffold(
        backgroundColor: AppColors.background,
        body: BlocBuilder<FeaturedGuidesCubit, FeaturedGuidesState>(
          builder: (context, state) {
            if (state is FeaturedGuidesLoading) {
              return _buildLoadingState(context);
            } else if (state is FeaturedGuideDetailsLoaded) {
              return _buildDetailsContent(context, state.guide);
            } else if (state is FeaturedGuidesError) {
              return _buildErrorState(context, state.message);
            }
            return _buildInitialState(context);
          },
        ),
      ),
    );
  }

  Widget _buildLoadingState(BuildContext context) {
    return CustomScrollView(
      slivers: [
        _buildAppBar(context, guideName ?? 'تفاصيل المرشد'),
        SliverFillRemaining(
          child: Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                CircularProgressIndicator(color: AppColors.primary),
                16.verticalSpace,
                Text(
                  'جاري تحميل تفاصيل المرشد...',
                  style: AppTextStyles.bodyMedium.copyWith(
                    color: AppColors.textSecondary,
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildErrorState(BuildContext context, String message) {
    return CustomScrollView(
      slivers: [
        _buildAppBar(context, guideName ?? 'تفاصيل المرشد'),
        SliverFillRemaining(
          child: Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(Icons.error_outline, size: 64, color: AppColors.error),
                16.verticalSpace,
                Text(
                  'حدث خطأ في تحميل التفاصيل',
                  style: AppTextStyles.heading.copyWith(color: AppColors.error),
                ),
                8.verticalSpace,
                Text(
                  message,
                  style: AppTextStyles.bodyMedium.copyWith(
                    color: AppColors.textSecondary,
                  ),
                  textAlign: TextAlign.center,
                ),
                24.verticalSpace,
                ElevatedButton(
                  onPressed: () {
                    context.read<FeaturedGuidesCubit>().getGuideById(guideId);
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppColors.primary,
                    foregroundColor: AppColors.white,
                  ),
                  child: Text('إعادة المحاولة'),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildInitialState(BuildContext context) {
    return CustomScrollView(
      slivers: [
        _buildAppBar(context, guideName ?? 'تفاصيل المرشد'),
        SliverFillRemaining(
          child: Center(
            child: Text(
              'لا توجد بيانات متاحة',
              style: AppTextStyles.bodyMedium.copyWith(
                color: AppColors.textSecondary,
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildDetailsContent(BuildContext context, GuideEntity guide) {
    return CustomScrollView(
      slivers: [
        _buildAppBar(context, guide.displayName),
        _buildProfileSection(context, guide),
        _buildContentSection(context, guide),
      ],
    );
  }

  Widget _buildAppBar(BuildContext context, String title) {
    return SliverAppBar(
      expandedHeight: 0,
      floating: true,
      pinned: true,
      backgroundColor: AppColors.primary,
      foregroundColor: AppColors.white,
      title: Text(
        title,
        style: AppTextStyles.heading.copyWith(
          color: AppColors.white,
          fontSize: ResponsiveHelper.isTablet(context) ? 20 : 18,
        ),
      ),
      leading: IconButton(
        icon: Icon(Icons.arrow_back),
        onPressed: () => Navigator.of(context).pop(),
      ),
      actions: [
        IconButton(
          icon: Icon(Icons.favorite_border),
          onPressed: () {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text('تم إضافة المرشد للمفضلة'),
                backgroundColor: AppColors.success,
              ),
            );
          },
        ),
        IconButton(
          icon: Icon(Icons.share),
          onPressed: () {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text('تم مشاركة المرشد'),
                backgroundColor: AppColors.info,
              ),
            );
          },
        ),
      ],
    );
  }

  Widget _buildProfileSection(BuildContext context, GuideEntity guide) {
    return SliverToBoxAdapter(
      child: SizedBox(
        height: 280.h(context),
        child: Stack(
          children: [
            // Background Gradient
            Container(
              width: double.infinity,
              height: double.infinity,
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [
                    AppColors.primary.withOpacity(0.8),
                    AppColors.primary,
                  ],
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                ),
              ),
            ),

            // Profile Content
            Positioned(
              bottom: 0,
              left: 0,
              right: 0,
              child: Container(
                padding: EdgeInsets.all(24),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.only(
                    topLeft: Radius.circular(24),
                    topRight: Radius.circular(24),
                  ),
                ),
                child: Column(
                  children: [
                    // Profile Image
                    Container(
                      width: 100,
                      height: 100,
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        border: Border.all(color: Colors.white, width: 4),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withOpacity(0.1),
                            blurRadius: 8,
                            offset: Offset(0, 2),
                          ),
                        ],
                      ),
                      child: ClipOval(
                        child: guide.hasImage
                            ? Image.network(
                                guide.imageUrl,
                                fit: BoxFit.cover,
                                errorBuilder: (context, error, stackTrace) =>
                                    _buildPlaceholderAvatar(),
                              )
                            : _buildPlaceholderAvatar(),
                      ),
                    ),
                    16.verticalSpace,

                    // Name and Rating
                    Text(
                      guide.displayName,
                      style: AppTextStyles.heading.copyWith(
                        fontSize: ResponsiveHelper.isTablet(context) ? 24 : 20,
                        fontWeight: FontWeight.bold,
                      ),
                      textAlign: TextAlign.center,
                    ),
                    8.verticalSpace,

                    // Rating Stars
                    Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        ...List.generate(5, (index) {
                          return Icon(
                            index < guide.rating.floor()
                                ? Icons.star
                                : Icons.star_border,
                            color: Colors.amber,
                            size: 20,
                          );
                        }),
                        8.horizontalSpace,
                        Text(
                          '(${guide.rating.toStringAsFixed(1)})',
                          style: AppTextStyles.bodyMedium.copyWith(
                            color: AppColors.textSecondary,
                          ),
                        ),
                      ],
                    ),
                    12.verticalSpace,

                    // Price Info
                    Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        _buildPriceChip('يومي', guide.formattedDayPrice),
                        16.horizontalSpace,
                        _buildPriceChip('ساعي', guide.formattedHourPrice),
                      ],
                    ),
                  ],
                ),
              ),
            ),

            // Status Badge
            Positioned(
              top: 16,
              right: 16,
              child: Container(
                padding: EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                decoration: BoxDecoration(
                  color: guide.isActive ? AppColors.success : AppColors.error,
                  borderRadius: BorderRadius.circular(
                    AppDimensions.radiusSmall,
                  ),
                ),
                child: Text(
                  guide.isActive ? 'متاح' : 'غير متاح',
                  style: AppTextStyles.bodySmall.copyWith(
                    color: AppColors.white,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildContentSection(BuildContext context, GuideEntity guide) {
    return SliverToBoxAdapter(
      child:
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Basic Info
              _buildBasicInfo(context, guide),

              // Experience
              _buildExperience(context, guide),

              // Action Buttons
              _buildActionButtons(context, guide),

              // Bottom Spacing
              ResponsiveHelper.getSpacing(
                context,
                type: 'xlarge',
              ).verticalSpaceResponsive(context),
            ],
          ).paddingSymmetric(
            horizontal: ResponsiveHelper.isTablet(context) ? 32 : 20,
            vertical: ResponsiveHelper.isTablet(context) ? 24 : 16,
            context: context,
          ),
    );
  }

  Widget _buildPlaceholderAvatar() {
    return Container(
      width: double.infinity,
      height: double.infinity,
      color: AppColors.primary.withOpacity(0.1),
      child: Icon(
        Icons.person,
        size: 40,
        color: AppColors.primary.withOpacity(0.5),
      ),
    );
  }

  Widget _buildPriceChip(String label, String price) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        color: AppColors.primary.withOpacity(0.1),
        borderRadius: BorderRadius.circular(AppDimensions.radiusSmall),
      ),
      child: Column(
        children: [
          Text(
            label,
            style: AppTextStyles.bodySmall.copyWith(
              color: AppColors.primary,
              fontSize: 10,
            ),
          ),
          2.verticalSpace,
          Text(
            price,
            style: AppTextStyles.bodyMedium.copyWith(
              color: AppColors.primary,
              fontWeight: FontWeight.bold,
              fontSize: 12,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBasicInfo(BuildContext context, GuideEntity guide) {
    return Container(
      padding: EdgeInsets.all(20),
      margin: EdgeInsets.only(bottom: 16),
      decoration: BoxDecoration(
        color: AppColors.white,
        borderRadius: BorderRadius.circular(AppDimensions.radiusMedium),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 8,
            offset: Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'معلومات المرشد',
            style: AppTextStyles.heading.copyWith(
              fontSize: ResponsiveHelper.isTablet(context) ? 18 : 16,
              fontWeight: FontWeight.bold,
            ),
          ),
          12.verticalSpace,
          Text(
            guide.displayInfo,
            style: AppTextStyles.bodyMedium.copyWith(height: 1.6),
          ),
        ],
      ),
    );
  }

  Widget _buildExperience(BuildContext context, GuideEntity guide) {
    return Container(
      padding: EdgeInsets.all(20),
      margin: EdgeInsets.only(bottom: 16),
      decoration: BoxDecoration(
        color: AppColors.white,
        borderRadius: BorderRadius.circular(AppDimensions.radiusMedium),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 8,
            offset: Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'الخبرة',
            style: AppTextStyles.heading.copyWith(
              fontSize: ResponsiveHelper.isTablet(context) ? 18 : 16,
              fontWeight: FontWeight.bold,
            ),
          ),
          12.verticalSpace,
          Text(
            guide.displayExperience,
            style: AppTextStyles.bodyMedium.copyWith(height: 1.6),
          ),
        ],
      ),
    );
  }

  Widget _buildActionButtons(BuildContext context, GuideEntity guide) {
    return Container(
      padding: EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: AppColors.white,
        borderRadius: BorderRadius.circular(AppDimensions.radiusMedium),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 8,
            offset: Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          SizedBox(
            width: double.infinity,
            child: ElevatedButton(
              onPressed: guide.isActive
                  ? () {
                      ScaffoldMessenger.of(context).showSnackBar(
                        SnackBar(
                          content: Text('سيتم إضافة نظام الحجز قريباً'),
                          backgroundColor: AppColors.success,
                        ),
                      );
                    }
                  : null,
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.success,
                foregroundColor: AppColors.white,
                padding: EdgeInsets.symmetric(vertical: 16),
                disabledBackgroundColor: Colors.grey,
              ),
              child: Text(
                guide.isActive ? 'احجز المرشد' : 'غير متاح للحجز',
                style: AppTextStyles.bodyLarge.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ),
          12.verticalSpace,
          Row(
            children: [
              Expanded(
                child: OutlinedButton.icon(
                  onPressed: () {
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                        content: Text('تم الاتصال بالمرشد'),
                        backgroundColor: AppColors.info,
                      ),
                    );
                  },
                  icon: Icon(Icons.phone),
                  label: Text('اتصل'),
                  style: OutlinedButton.styleFrom(
                    foregroundColor: AppColors.primary,
                    side: BorderSide(color: AppColors.primary),
                    padding: EdgeInsets.symmetric(vertical: 12),
                  ),
                ),
              ),
              12.horizontalSpace,
              Expanded(
                child: OutlinedButton.icon(
                  onPressed: () {
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                        content: Text('تم إرسال رسالة للمرشد'),
                        backgroundColor: AppColors.info,
                      ),
                    );
                  },
                  icon: Icon(Icons.message),
                  label: Text('رسالة'),
                  style: OutlinedButton.styleFrom(
                    foregroundColor: AppColors.primary,
                    side: BorderSide(color: AppColors.primary),
                    padding: EdgeInsets.symmetric(vertical: 12),
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
