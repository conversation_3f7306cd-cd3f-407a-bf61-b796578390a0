// features/wallet/presentation/pages/wallet_home_screen.dart
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:tripooo_user/core/theme/theme.dart';
import 'package:tripooo_user/core/routing/app_router.dart';

class WalletHomeScreen extends StatelessWidget {
  const WalletHomeScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        elevation: 0,
        backgroundColor: AppColors.primary,
        foregroundColor: Colors.white,
        title: Text('المحفظة', style: AppTextStyles.appBarTitle.copyWith(color: Colors.white)),
      ),
      body: Column(
        children: [
          _WalletHeader(),
          const Divider(height: 1),
          Expanded(child: _TransactionsList()),
          SafeArea(
            minimum: const EdgeInsets.all(16),
            child: SizedBox(
              width: double.infinity,
              height: 52,
              child: ElevatedButton(
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.primary,
                  shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
                ),
                onPressed: () => context.push(AppRoutes.walletAddBalance),
                child: Text('إضافة رصيد جديدة', style: AppTextStyles.buttonText),
              ),
            ),
          ),
        ],
      ),
    );
  }
}

class _WalletHeader extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(16),
      decoration: const BoxDecoration(
        gradient: LinearGradient(
          colors: [Color(0xFF6E8EF5), Color(0xFF7EB4FF)],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              const Icon(Icons.account_balance_wallet_outlined, color: Colors.white),
              const SizedBox(width: 8),
              Text('المحفظة', style: AppTextStyles.title.copyWith(color: Colors.white)),
            ],
          ),
          const SizedBox(height: 8),
          Row(
            children: [
              Text('ر.س', style: AppTextStyles.titleSmall.copyWith(color: Colors.white)),
              const SizedBox(width: 8),
              Text('4,200', style: AppTextStyles.heading.copyWith(color: Colors.white)),
            ],
          ),
          const SizedBox(height: 12),
          Container(
            height: 40,
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(10),
            ),
            child: TextButton.icon(
              onPressed: () => context.push(AppRoutes.walletAddBalance),
              icon: const Icon(Icons.add, color: Colors.black),
              label: Text('إضافة شحن جديدة', style: AppTextStyles.labelLarge),
            ),
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              const Icon(Icons.history, color: Colors.white70, size: 18),
              const SizedBox(width: 6),
              Text('آخر العمليات', style: AppTextStyles.labelLarge.copyWith(color: Colors.white)),
            ],
          ),
        ],
      ),
    );
  }
}

class _TransactionsList extends StatelessWidget {
  final List<_Txn> txns = const [
    _Txn(date: '2025-06-24', title: 'عملية الشحن', amount: '+1000 ر.س', desc: 'المحفظة: تم شحن الرصيد'),
    _Txn(date: '2025-06-24', title: 'عملية خصم', amount: '-75 ر.س', desc: 'شراء من المحفظة'),
    _Txn(date: '2025-06-23', title: 'عملية الشحن', amount: '+200 ر.س', desc: 'Apple Pay / تم الشحن'),
  ];

  const _TransactionsList({super.key});

  @override
  Widget build(BuildContext context) {
    return ListView.separated(
      padding: const EdgeInsets.symmetric(vertical: 12),
      itemBuilder: (_, i) => _TxnTile(txn: txns[i]),
      separatorBuilder: (_, __) => const Divider(height: 1),
      itemCount: txns.length,
    );
  }
}

class _TxnTile extends StatelessWidget {
  final _Txn txn;
  const _TxnTile({required this.txn});

  @override
  Widget build(BuildContext context) {
    return ListTile(
      title: Row(
        children: [
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              color: Colors.blue.shade50,
              borderRadius: BorderRadius.circular(20),
            ),
            child: Text(txn.date, style: AppTextStyles.labelSmall.copyWith(color: AppColors.textSecondary)),
          ),
          const SizedBox(width: 8),
          Text(txn.title, style: AppTextStyles.labelLarge),
        ],
      ),
      subtitle: Padding(
        padding: const EdgeInsets.only(top: 6),
        child: Text(txn.desc, style: AppTextStyles.bodySmall),
      ),
      trailing: Text(txn.amount, style: AppTextStyles.titleSmall.copyWith(
        color: txn.amount.startsWith('-') ? AppColors.error : AppColors.success,
      )),
    );
  }
}

class _Txn {
  final String date;
  final String title;
  final String amount;
  final String desc;
  const _Txn({required this.date, required this.title, required this.amount, required this.desc});
}

