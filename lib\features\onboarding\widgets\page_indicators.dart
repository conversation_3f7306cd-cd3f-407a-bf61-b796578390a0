// features/onboarding/widgets/page_indicators.dart
import 'package:flutter/material.dart';
import 'package:tripooo_user/core/theme/theme.dart';
import '../utils/responsive_helper.dart';

class PageIndicators extends StatelessWidget {
  final int currentIndex;
  final int totalPages;

  const PageIndicators({
    super.key,
    required this.currentIndex,
    required this.totalPages,
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: List.generate(
        totalPages,
        (index) => _buildIndicator(context, index == currentIndex),
      ),
    );
  }

  Widget _buildIndicator(BuildContext context, bool isActive) {
    final size = ResponsiveHelper.getIndicatorSize(context);
    final spacing = ResponsiveHelper.getSpacing(context, type: 'small');

    return AnimatedContainer(
      duration: const Duration(milliseconds: 300),
      margin: EdgeInsets.symmetric(horizontal: spacing / 2),
      height: size,
      width: isActive ? size * 3 : size,
      decoration: BoxDecoration(
        color: isActive ? AppColors.primary : AppColors.textTertiary,
        borderRadius: BorderRadius.circular(size / 2),
      ),
    );
  }
}
