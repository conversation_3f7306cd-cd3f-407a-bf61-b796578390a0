// lib/core/theme/app_dimensions.dart

class AppDimensions {
  // Spacing
  static const double spacing4 = 4.0;
  static const double spacing8 = 8.0;
  static const double spacing12 = 12.0;
  static const double spacing16 = 16.0;
  static const double spacing20 = 20.0;
  static const double spacing24 = 24.0;
  static const double spacing32 = 32.0;
  static const double spacing40 = 40.0;
  static const double spacing48 = 48.0;
  static const double spacing56 = 56.0;
  static const double spacing64 = 64.0;
  
  // Padding
  static const double paddingSmall = 8.0;
  static const double paddingMedium = 16.0;
  static const double paddingLarge = 24.0;
  static const double paddingXLarge = 32.0;
  
  // Margin
  static const double marginSmall = 8.0;
  static const double marginMedium = 16.0;
  static const double marginLarge = 24.0;
  static const double marginXLarge = 32.0;
  
  // Border Radius
  static const double radiusSmall = 4.0;
  static const double radiusMedium = 8.0;
  static const double radiusLarge = 12.0;
  static const double radiusXLarge = 16.0;
  static const double radiusCircular = 50.0;
  
  // Button Heights
  static const double buttonHeightSmall = 36.0;
  static const double buttonHeightMedium = 48.0;
  static const double buttonHeightLarge = 56.0;
  
  // Icon Sizes
  static const double iconSizeSmall = 16.0;
  static const double iconSizeMedium = 24.0;
  static const double iconSizeLarge = 32.0;
  static const double iconSizeXLarge = 48.0;
  
  // Form Field Heights
  static const double textFieldHeight = 48.0;
  static const double textFieldHeightLarge = 56.0;
  
  // App Bar
  static const double appBarHeight = 56.0;
  static const double appBarElevation = 0.0;
  
  // Bottom Navigation
  static const double bottomNavHeight = 60.0;
  static const double bottomNavElevation = 8.0;
  
  // Card
  static const double cardElevation = 2.0;
  static const double cardRadius = 12.0;
  
  // Dialog
  static const double dialogRadius = 16.0;
  static const double dialogElevation = 8.0;
  
  // Divider
  static const double dividerThickness = 1.0;
  
  // Border Width
  static const double borderWidthThin = 1.0;
  static const double borderWidthMedium = 2.0;
  static const double borderWidthThick = 3.0;
  
  // Screen Padding
  static const double screenPaddingHorizontal = 24.0;
  static const double screenPaddingVertical = 16.0;
  
  // List Item Heights
  static const double listItemHeight = 56.0;
  static const double listItemHeightLarge = 72.0;
  
  // Avatar Sizes
  static const double avatarSizeSmall = 32.0;
  static const double avatarSizeMedium = 48.0;
  static const double avatarSizeLarge = 64.0;
  static const double avatarSizeXLarge = 96.0;
  
  // Tab Bar
  static const double tabBarHeight = 48.0;
  static const double tabIndicatorHeight = 3.0;
  
  // Floating Action Button
  static const double fabSize = 56.0;
  static const double fabSizeSmall = 40.0;
  static const double fabSizeLarge = 64.0;
  
  // Snackbar
  static const double snackbarRadius = 8.0;
  static const double snackbarElevation = 6.0;
  
  // Chip
  static const double chipHeight = 32.0;
  static const double chipRadius = 16.0;
  
  // Progress Indicator
  static const double progressIndicatorSize = 20.0;
  static const double progressIndicatorStrokeWidth = 2.0;
  
  // Checkbox
  static const double checkboxSize = 20.0;
  
  // Radio Button
  static const double radioButtonSize = 20.0;
  
  // Switch
  static const double switchWidth = 51.0;
  static const double switchHeight = 31.0;
  
  // Slider
  static const double sliderHeight = 40.0;
  static const double sliderThumbRadius = 10.0;
  static const double sliderTrackHeight = 4.0;
}
