// features/guides/domain/entities/guide_details_entity.dart
import 'package:equatable/equatable.dart';
import 'language_entity.dart';
import 'country_entity.dart';
import 'working_day_entity.dart';

class GuideDetailsEntity extends Equatable {
  final int id;
  final String name;
  final String email;
  final String? img;
  final String? emailVerifiedAt;
  final String createdAt;
  final String updatedAt;
  final String mobile;
  final int userType;
  final String? deletedAt;
  final int isActivate;
  final int dayPrice;
  final int hourPrice;
  final String info;
  final String experience;
  final List<String> interests;
  final List<String> toursType;
  final List<String> albums;
  final int? countryId;
  final String? mobileVerifiedAt;
  final String? fcmToken;
  final List<LanguageEntity> languages;
  final CountryEntity? country;
  final List<WorkingDayEntity> days;

  const GuideDetailsEntity({
    required this.id,
    required this.name,
    required this.email,
    this.img,
    this.emailVerifiedAt,
    required this.createdAt,
    required this.updatedAt,
    required this.mobile,
    required this.userType,
    this.deletedAt,
    required this.isActivate,
    required this.dayPrice,
    required this.hourPrice,
    required this.info,
    required this.experience,
    required this.interests,
    required this.toursType,
    required this.albums,
    this.countryId,
    this.mobileVerifiedAt,
    this.fcmToken,
    required this.languages,
    this.country,
    required this.days,
  });

  // Helper methods
  String get displayName => name.isNotEmpty ? name : 'مرشد سياحي';
  
  String get displayInfo => info.isNotEmpty ? info : 'مرشد سياحي محترف';
  
  String get displayExperience => experience.isNotEmpty ? experience : 'خبرة متميزة';
  
  String get formattedDayPrice => '$dayPrice ريال/يوم';
  
  String get formattedHourPrice => '$hourPrice ريال/ساعة';
  
  bool get hasImage => img != null && img!.isNotEmpty;
  
  String get imageUrl => hasImage ? 'https://tripooo.tptechcorp.com/$img' : '';
  
  double get rating => 4.0 + (id % 10) * 0.1; // Mock rating based on ID
  
  List<String> get displayInterests => interests.where((interest) => interest.isNotEmpty).toList();
  
  List<String> get displayToursType => toursType.where((type) => type.isNotEmpty).toList();

  bool get isActive => isActivate == 1;

  bool get isEmailVerified => emailVerifiedAt != null;

  bool get isMobileVerified => mobileVerifiedAt != null;

  String? get countryName => country?.name;

  List<String> get languageNames => languages.map((lang) => lang.name).toList();

  List<String> getLanguageNamesInLocale(String locale) {
    return languages.map((lang) => lang.getTranslatedName(locale)).toList();
  }

  List<WorkingDayEntity> get availableDays => days.where((day) => day.isAvailable).toList();

  List<WorkingDayEntity> get workingDays => days.where((day) => day.isActive).toList();

  bool get hasWorkingDays => workingDays.isNotEmpty;

  @override
  List<Object?> get props => [
        id,
        name,
        email,
        img,
        emailVerifiedAt,
        createdAt,
        updatedAt,
        mobile,
        userType,
        deletedAt,
        isActivate,
        dayPrice,
        hourPrice,
        info,
        experience,
        interests,
        toursType,
        albums,
        countryId,
        mobileVerifiedAt,
        fcmToken,
        languages,
        country,
        days,
      ];
}
