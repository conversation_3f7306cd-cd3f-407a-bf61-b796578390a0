// features/Auth/data/models/social_auth_models.dart
import '../../../../core/utils/typedef.dart';

enum SocialProvider {
  google,
  facebook,
  apple,
}

class SocialAuthRequest {
  final SocialProvider provider;
  final String accessToken;
  final String? idToken;
  final String? email;
  final String? name;
  final String? profilePicture;
  final String? fcmToken;
  final int userType; // 1 for client, 2 for guide

  const SocialAuthRequest({
    required this.provider,
    required this.accessToken,
    this.idToken,
    this.email,
    this.name,
    this.profilePicture,
    this.fcmToken,
    this.userType = 1,
  });

  DataMap toMap() {
    return {
      'provider': provider.name,
      'access_token': accessToken,
      if (idToken != null) 'id_token': idToken,
      if (email != null) 'email': email,
      if (name != null) 'name': name,
      if (profilePicture != null) 'profile_picture': profilePicture,
      if (fcmToken != null) 'fcm_token': fcmToken,
      'user_type': userType,
    };
  }
}

class SocialUserInfo {
  final String id;
  final String? email;
  final String? name;
  final String? firstName;
  final String? lastName;
  final String? profilePicture;
  final SocialProvider provider;

  const SocialUserInfo({
    required this.id,
    this.email,
    this.name,
    this.firstName,
    this.lastName,
    this.profilePicture,
    required this.provider,
  });

  factory SocialUserInfo.fromGoogle({
    required String id,
    String? email,
    String? displayName,
    String? photoUrl,
  }) {
    return SocialUserInfo(
      id: id,
      email: email,
      name: displayName,
      profilePicture: photoUrl,
      provider: SocialProvider.google,
    );
  }

  factory SocialUserInfo.fromFacebook({
    required String id,
    String? email,
    String? name,
    String? firstName,
    String? lastName,
    String? pictureUrl,
  }) {
    return SocialUserInfo(
      id: id,
      email: email,
      name: name,
      firstName: firstName,
      lastName: lastName,
      profilePicture: pictureUrl,
      provider: SocialProvider.facebook,
    );
  }

  factory SocialUserInfo.fromApple({
    required String id,
    String? email,
    String? fullName,
    String? firstName,
    String? lastName,
  }) {
    return SocialUserInfo(
      id: id,
      email: email,
      name: fullName,
      firstName: firstName,
      lastName: lastName,
      provider: SocialProvider.apple,
    );
  }

  String get displayName {
    if (name != null && name!.isNotEmpty) return name!;
    if (firstName != null && lastName != null) {
      return '$firstName $lastName';
    }
    if (firstName != null) return firstName!;
    if (lastName != null) return lastName!;
    return email ?? 'User';
  }
}
