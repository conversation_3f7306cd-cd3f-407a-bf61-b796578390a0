// features/Auth/data/datasources/auth_local_data_source.dart
import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import '../../../../core/utils/constants.dart';
import '../../../../core/error/exceptions.dart';
import '../models/user_model.dart';

abstract class AuthLocalDataSource {
  /// Save authentication token securely
  Future<void> saveToken(String token);

  /// Get stored authentication token
  Future<String?> getToken();

  /// Save user data to local storage
  Future<void> saveUser(UserModel user);

  /// Get stored user data
  Future<UserModel?> getUser();

  /// Save login state
  Future<void> saveLoginState(bool isLoggedIn);

  /// Get login state
  Future<bool> getLoginState();

  /// Save remember me preference
  Future<void> saveRememberMe(bool rememberMe);

  /// Get remember me preference
  Future<bool> getRememberMe();

  /// Save FCM token
  Future<void> saveFcmToken(String fcmToken);

  /// Get FCM token
  Future<String?> getFcmToken();

  /// Clear all stored data
  Future<void> clearAll();
}

class AuthLocalDataSourceImpl implements AuthLocalDataSource {
  final SharedPreferences _sharedPreferences;
  final FlutterSecureStorage _secureStorage;

  const AuthLocalDataSourceImpl({
    required SharedPreferences sharedPreferences,
    required FlutterSecureStorage secureStorage,
  })  : _sharedPreferences = sharedPreferences,
        _secureStorage = secureStorage;

  @override
  Future<void> saveToken(String token) async {
    try {
      await _secureStorage.write(key: AppConstants.tokenKey, value: token);
    } catch (e) {
      throw CacheException('Failed to save token: $e');
    }
  }

  @override
  Future<String?> getToken() async {
    try {
      return await _secureStorage.read(key: AppConstants.tokenKey);
    } catch (e) {
      throw CacheException('Failed to get token: $e');
    }
  }

  @override
  Future<void> saveUser(UserModel user) async {
    try {
      final userJson = json.encode(user.toMap());
      await _sharedPreferences.setString(AppConstants.userKey, userJson);
    } catch (e) {
      throw CacheException('Failed to save user: $e');
    }
  }

  @override
  Future<UserModel?> getUser() async {
    try {
      final userJson = _sharedPreferences.getString(AppConstants.userKey);
      if (userJson != null) {
        final userMap = json.decode(userJson) as Map<String, dynamic>;
        return UserModel.fromMap(userMap);
      }
      return null;
    } catch (e) {
      throw CacheException('Failed to get user: $e');
    }
  }

  @override
  Future<void> saveLoginState(bool isLoggedIn) async {
    try {
      await _sharedPreferences.setBool(AppConstants.isLoggedInKey, isLoggedIn);
    } catch (e) {
      throw CacheException('Failed to save login state: $e');
    }
  }

  @override
  Future<bool> getLoginState() async {
    try {
      return _sharedPreferences.getBool(AppConstants.isLoggedInKey) ?? false;
    } catch (e) {
      throw CacheException('Failed to get login state: $e');
    }
  }

  @override
  Future<void> saveRememberMe(bool rememberMe) async {
    try {
      await _sharedPreferences.setBool(AppConstants.rememberMeKey, rememberMe);
    } catch (e) {
      throw CacheException('Failed to save remember me: $e');
    }
  }

  @override
  Future<bool> getRememberMe() async {
    try {
      return _sharedPreferences.getBool(AppConstants.rememberMeKey) ?? false;
    } catch (e) {
      throw CacheException('Failed to get remember me: $e');
    }
  }

  @override
  Future<void> saveFcmToken(String fcmToken) async {
    try {
      await _sharedPreferences.setString(AppConstants.fcmTokenKey, fcmToken);
    } catch (e) {
      throw CacheException('Failed to save FCM token: $e');
    }
  }

  @override
  Future<String?> getFcmToken() async {
    try {
      return _sharedPreferences.getString(AppConstants.fcmTokenKey);
    } catch (e) {
      throw CacheException('Failed to get FCM token: $e');
    }
  }

  @override
  Future<void> clearAll() async {
    try {
      await _secureStorage.delete(key: AppConstants.tokenKey);
      await _sharedPreferences.remove(AppConstants.userKey);
      await _sharedPreferences.remove(AppConstants.isLoggedInKey);
      // Keep remember me and FCM token
    } catch (e) {
      throw CacheException('Failed to clear data: $e');
    }
  }
}
