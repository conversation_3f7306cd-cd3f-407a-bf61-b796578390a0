# 🔑 كيفية الحصول على SHA-1 Fingerprint

## للحصول على SHA-1 fingerprint لإضافته في Firebase Console:

### الطريقة الأولى: من Android Studio
1. افتح Android Studio
2. اذه<PERSON> إلى `View` → `Tool Windows` → `Gradle`
3. انتقل إلى `android` → `signingReport`
4. شغل المهمة وانسخ SHA1 من النتائج

### الطريقة الثانية: من Command Line
```bash
cd android
./gradlew signingReport
```

### الطريقة الثالثة: باستخدام keytool
```bash
keytool -list -v -keystore ~/.android/debug.keystore -alias androiddebugkey -storepass android -keypass android
```

## إضافة SHA-1 في Firebase Console:
1. اذهب إلى [Firebase Console](https://console.firebase.google.com)
2. اختر مشروعك `tripooo-34420`
3. اذ<PERSON><PERSON> إلى Project Settings
4. في تبويب "Your apps"، اختر Android app
5. اضغط "Add fingerprint"
6. الصق SHA-1 fingerprint
7. احفظ التغييرات

## تحديث google-services.json:
بعد إضافة SHA-1، حمل ملف `google-services.json` الجديد واستبدل الموجود في `android/app/`
