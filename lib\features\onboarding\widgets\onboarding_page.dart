// features/onboarding/widgets/onboarding_page.dart
import 'package:flutter/material.dart';
import 'package:tripooo_user/core/theme/theme.dart';
import '../models/onboarding_model.dart';
import '../utils/responsive_helper.dart';

class OnboardingPage extends StatelessWidget {
  final OnboardingModel model;
  final bool isDebugMode;

  const OnboardingPage({
    super.key,
    required this.model,
    this.isDebugMode = false,
  });

  @override
  Widget build(BuildContext context) {
    final isTablet = ResponsiveHelper.isTablet(context);
    final isLandscape = ResponsiveHelper.isLandscape(context);

    return Stack(
      children: [
        // Background Image
        Positioned.fill(
          child: _buildBackgroundImage(context, isTablet, isLandscape),
        ),

        // Debug Badge
        if (isDebugMode)
          Positioned(top: 40, right: 0, child: _buildDebugBadge()),

        // Content
        Positioned.fill(child: _buildContent(context, isTablet, isLandscape)),
      ],
    );
  }

  Widget _buildBackgroundImage(
    BuildContext context,
    bool isTablet,
    bool isLandscape,
  ) {
    return Container(
      decoration: BoxDecoration(
        image: DecorationImage(
          image: AssetImage(model.image),
          fit: BoxFit.cover,
        ),
      ),
      child: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Colors.transparent,
              Colors.black.withValues(alpha: 0.3),
              Colors.white.withValues(alpha: 0.9),
            ],
            stops: const [0.0, 0.6, 1.0],
          ),
        ),
      ),
    );
  }

  Widget _buildDebugBadge() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: const BoxDecoration(
        color: Colors.red,
        borderRadius: BorderRadius.only(bottomLeft: Radius.circular(8)),
      ),
      child: Text(
        'DEBUG',
        style: AppTextStyles.labelSmall.copyWith(
          color: AppColors.white,
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }

  Widget _buildContent(BuildContext context, bool isTablet, bool isLandscape) {
    final contentHeight = ResponsiveHelper.getContentHeight(context);

    return Align(
      alignment: Alignment.bottomCenter,
      child: Container(
        height: contentHeight,
        width: double.infinity,
        padding: ResponsiveHelper.getContentPadding(context),
        decoration: BoxDecoration(
          color: AppColors.white,
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(
              isTablet
                  ? AppDimensions.radiusXLarge * 2
                  : AppDimensions.radiusXLarge,
            ),
            topRight: Radius.circular(
              isTablet
                  ? AppDimensions.radiusXLarge * 2
                  : AppDimensions.radiusXLarge,
            ),
          ),
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // Title
            Text(
              model.title,
              style: AppTextStyles.heading.copyWith(
                fontSize: ResponsiveHelper.getTitleFontSize(context),
              ),
              textAlign: TextAlign.center,
            ),

            SizedBox(
              height: ResponsiveHelper.getSpacing(context, type: 'medium'),
            ),

            // Description
            Text(
              model.description,
              style: AppTextStyles.subtitle.copyWith(
                fontSize: ResponsiveHelper.getDescriptionFontSize(context),
              ),
              textAlign: TextAlign.center,
              maxLines: isLandscape ? 2 : 3,
              overflow: TextOverflow.ellipsis,
            ),

            SizedBox(
              height: ResponsiveHelper.getSpacing(context, type: 'large'),
            ),

            // Flexible space for button and indicators
            const SizedBox(height: 60),
          ],
        ),
      ),
    );
  }
}
