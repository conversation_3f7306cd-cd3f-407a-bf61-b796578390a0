// core/widgets/phone_text_field.dart
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../models/country_model.dart';
import '../theme/theme.dart';
import 'country_picker_dialog.dart';

class PhoneTextField extends StatefulWidget {
  final String? label;
  final String? hintText;
  final TextEditingController? controller;
  final String? Function(String?)? validator;
  final Country? initialCountry;
  final Color? countryCodeBackgroundColor;
  final Color? countryCodeTextColor;
  final Function(Country)? onCountryChanged;
  final bool showCountryCodeDropdown;
  final TextAlign textAlign;
  final TextStyle? textStyle;
  final TextStyle? labelStyle;
  final TextStyle? hintStyle;
  final TextStyle? countryCodeStyle;
  final EdgeInsetsGeometry? contentPadding;
  final InputBorder? border;
  final InputBorder? enabledBorder;
  final InputBorder? focusedBorder;
  final InputBorder? errorBorder;
  final Color? fillColor;
  final bool filled;
  final Function(String)? onChanged;
  final Function(String)? onSubmitted;
  final FocusNode? focusNode;
  final List<TextInputFormatter>? inputFormatters;
  final bool enabled;
  final bool readOnly;
  final double? labelSpacing;
  final CrossAxisAlignment labelAlignment;
  final bool showLabel;
  final double? countryCodeWidth;
  final double? spacing;

  const PhoneTextField({
    super.key,
    this.label,
    this.hintText,
    this.controller,
    this.validator,
    this.initialCountry,
    this.countryCodeBackgroundColor,
    this.countryCodeTextColor,
    this.onCountryChanged,
    this.showCountryCodeDropdown = true,
    this.textAlign = TextAlign.right,
    this.textStyle,
    this.labelStyle,
    this.hintStyle,
    this.countryCodeStyle,
    this.contentPadding,
    this.border,
    this.enabledBorder,
    this.focusedBorder,
    this.errorBorder,
    this.fillColor,
    this.filled = false,
    this.onChanged,
    this.onSubmitted,
    this.focusNode,
    this.inputFormatters,
    this.enabled = true,
    this.readOnly = false,
    this.labelSpacing = 8.0,
    this.labelAlignment = CrossAxisAlignment.start,
    this.showLabel = true,
    this.countryCodeWidth,
    this.spacing = 8.0,
  });

  @override
  State<PhoneTextField> createState() => _PhoneTextFieldState();
}

class _PhoneTextFieldState extends State<PhoneTextField> {
  late Country _selectedCountry;
  late PhoneValidationRule _currentRule;

  @override
  void initState() {
    super.initState();
    _selectedCountry = widget.initialCountry ?? CountryData.getDefaultCountry();
    _updateValidationRule();

    // ✅ Listener to remove dial code on paste
    widget.controller?.addListener(_removeDialCodeIfNeeded);
  }

  @override
  void dispose() {
    widget.controller?.removeListener(_removeDialCodeIfNeeded);
    super.dispose();
  }

  void _updateValidationRule() {
    _currentRule =
        CountryData.getPhoneRule(_selectedCountry.code) ??
        CountryData.getDefaultPhoneRule();
  }

  void _showCountryPicker() async {
    final country = await showCountryPicker(
      context: context,
      selectedCountry: _selectedCountry,
    );

    if (country != null) {
      setState(() {
        _selectedCountry = country;
        _updateValidationRule();
      });
      widget.onCountryChanged?.call(country);
    }
  }

  /// ✅ This method checks if the input starts with the dial code and removes it
  void _removeDialCodeIfNeeded() {
    final controller = widget.controller;
    if (controller == null) return;

    final dialCode = _selectedCountry.dialCode;
    final text = controller.text;

    if (text.startsWith(dialCode)) {
      final newText = text.substring(dialCode.length);
      controller.value = TextEditingValue(
        text: newText,
        selection: TextSelection.collapsed(offset: newText.length),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    final textDirection = Directionality.of(context);
    final isRTL = textDirection == TextDirection.rtl;

    CrossAxisAlignment effectiveLabelAlignment;
    if (widget.labelAlignment == CrossAxisAlignment.start) {
      effectiveLabelAlignment = isRTL
          ? CrossAxisAlignment.end
          : CrossAxisAlignment.start;
    } else if (widget.labelAlignment == CrossAxisAlignment.end) {
      effectiveLabelAlignment = isRTL
          ? CrossAxisAlignment.start
          : CrossAxisAlignment.end;
    } else {
      effectiveLabelAlignment = widget.labelAlignment;
    }

    return Column(
      crossAxisAlignment: effectiveLabelAlignment,
      children: [
        if (widget.label != null && widget.showLabel) ...[
          Text(
            widget.label!,
            style:
                widget.labelStyle ??
                const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                  color: Colors.black,
                ),
          ),
          SizedBox(height: widget.labelSpacing),
        ],
        _buildPhoneFieldWithSuffix(context),
      ],
    );
  }

  Widget _buildPhoneFieldWithSuffix(BuildContext context) {
    return TextFormField(
      controller: widget.controller,
      validator: widget.validator,
      keyboardType: TextInputType.phone,
      textAlign: widget.textAlign,
      style: widget.textStyle,
      onChanged: widget.onChanged,
      onFieldSubmitted: widget.onSubmitted,
      focusNode: widget.focusNode,
      inputFormatters:
          widget.inputFormatters ??
          [
            FilteringTextInputFormatter.digitsOnly,
            LengthLimitingTextInputFormatter(_currentRule.maxLength),
          ],
      enabled: widget.enabled,
      readOnly: widget.readOnly,
      decoration: InputDecoration(
        hintText: widget.hintText ?? _currentRule.hintText,
        hintStyle: widget.hintStyle ?? TextStyle(color: Colors.grey[400]),
        contentPadding:
            widget.contentPadding ??
            const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        filled: widget.filled,
        fillColor: widget.fillColor,
        suffixIcon: _buildCountryCodeSuffix(),
        border:
            widget.border ??
            OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(color: Colors.grey[300]!),
            ),
        enabledBorder:
            widget.enabledBorder ??
            OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(color: Colors.grey[300]!),
            ),
        focusedBorder:
            widget.focusedBorder ??
            OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: const BorderSide(color: Colors.blue),
            ),
        errorBorder:
            widget.errorBorder ??
            OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: const BorderSide(color: Colors.red),
            ),
        focusedErrorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: const BorderSide(color: Colors.red, width: 2),
        ),
      ),
    );
  }

  Widget _buildCountryCodeSuffix() {
    return GestureDetector(
      onTap: widget.showCountryCodeDropdown ? _showCountryPicker : null,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        decoration: BoxDecoration(
          color: widget.fillColor ?? Colors.transparent,
          border: Border(
            left: BorderSide(color: AppColors.borderColor, width: 1),
          ),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            if (_selectedCountry.flag.isNotEmpty) ...[
              Text(_selectedCountry.flag, style: const TextStyle(fontSize: 16)),
              const SizedBox(width: 4),
            ],
            Text(
              _selectedCountry.dialCode,
              style:
                  widget.countryCodeStyle ??
                  TextStyle(
                    fontSize: 16,
                    color: widget.countryCodeTextColor ?? Colors.black87,
                    fontWeight: FontWeight.w500,
                  ),
            ),
            if (widget.showCountryCodeDropdown) ...[
              const SizedBox(width: 4),
              Icon(
                Icons.keyboard_arrow_down,
                color: widget.countryCodeTextColor ?? Colors.black87,
                size: 20,
              ),
            ],
          ],
        ),
      ),
    );
  }
}
