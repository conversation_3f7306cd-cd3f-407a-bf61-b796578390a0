// features/Auth/presentation/pages/login_screen.dart
import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:go_router/go_router.dart';
import 'package:tripooo_user/constants/app_images.dart';
import 'package:tripooo_user/core/theme/theme.dart';
import 'package:tripooo_user/core/utils/responsive_extensions.dart';
import 'package:tripooo_user/core/utils/widget_extensions.dart';
import 'package:tripooo_user/features/onboarding/utils/responsive_helper.dart';
import 'package:tripooo_user/features/Auth/presentation/cubit/auth_cubit.dart';
import 'package:tripooo_user/core/routing/app_router.dart';
import 'package:tripooo_user/core/models/country_model.dart';
import 'package:tripooo_user/core/widgets/widgets.dart';
import 'dart:ui';

class LoginScreen extends StatefulWidget {
  const LoginScreen({super.key});

  @override
  State<LoginScreen> createState() => _LoginScreenState();
}

class _LoginScreenState extends State<LoginScreen> {
  final _formKey = GlobalKey<FormState>();
  final _phoneController = TextEditingController();
  final _passwordController = TextEditingController();
  bool _obscurePassword = true;
  final bool _rememberMe = false;
  late Country _selectedCountry;

  @override
  void initState() {
    super.initState();
    _selectedCountry = CountryData.getDefaultCountry();
  }

  @override
  void dispose() {
    _phoneController.dispose();
    _passwordController.dispose();
    super.dispose();
  }

  String? _validatePhone(String? value) {
    if (value == null || value.trim().isEmpty) {
      return 'phone_required'.tr();
    }

    final rule =
        CountryData.getPhoneRule(_selectedCountry.code) ??
        CountryData.getDefaultPhoneRule();

    if (value.trim().length < rule.minLength ||
        value.trim().length > rule.maxLength) {
      return Localizations.localeOf(context).languageCode == 'ar'
          ? rule.errorMessageAr
          : rule.errorMessage;
    }

    final phoneRegex = RegExp(rule.pattern);
    if (!phoneRegex.hasMatch(value.trim())) {
      return Localizations.localeOf(context).languageCode == 'ar'
          ? rule.errorMessageAr
          : rule.errorMessage;
    }

    return null;
  }

  String? _validatePassword(String? value) {
    if (value == null || value.isEmpty) {
      return 'password_required'.tr();
    }
    return null;
  }

  void _submitForm() {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    context.read<AuthCubit>().login(
      mobile: _selectedCountry.dialCode + _phoneController.text.trim(),
      password: _passwordController.text,
    );
  }

  @override
  Widget build(BuildContext context) {
    final isTablet = ResponsiveHelper.isTablet(context);
    final isLandscape = ResponsiveHelper.isLandscape(context);
    final isSmallScreen = ResponsiveHelper.isSmallScreen(context);

    return Scaffold(
      backgroundColor: Colors.white,
      body: BlocListener<AuthCubit, AuthState>(
        listener: (context, state) async {
          if (state is AuthLoginSuccess) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text('login_successful'.tr()),
                backgroundColor: Colors.green,
              ),
            );
            log('Login successful', name: 'LoginScreen');
          } else if (state is AuthNavigateToHome) {
            if (context.mounted) {
              // Force navigation to home without going through AuthGuard
              context.go(AppRoutes.home);
            }
          } else if (state is AuthSocialLoginSuccess) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text('social_login_successful'.tr()),
                backgroundColor: Colors.green,
              ),
            );
            // Add small delay to ensure data is saved
            await Future.delayed(const Duration(milliseconds: 200));
            if (context.mounted) {
              // Force navigation to home without going through AuthGuard
              context.go(AppRoutes.home);
            }
          } else if (state is AuthError) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(state.message),
                backgroundColor: Colors.red,
              ),
            );
          }
        },
        child: SafeArea(
          child: SingleChildScrollView(
            physics: const BouncingScrollPhysics(),
            child: _buildResponsiveLayout(
              context,
              isTablet,
              isLandscape,
              isSmallScreen,
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildResponsiveLayout(
    BuildContext context,
    bool isTablet,
    bool isLandscape,
    bool isSmallScreen,
  ) {
    if (isLandscape && isTablet) {
      return _buildLandscapeTabletLayout(context);
    } else if (isTablet) {
      return _buildTabletLayout(context);
    } else {
      return _buildMobileLayout(context, isSmallScreen);
    }
  }

  Widget _buildMobileLayout(BuildContext context, bool isSmallScreen) {
    return Stack(
      children: [
        BackdropFilter(
          filter: ImageFilter.blur(sigmaX: 2.0, sigmaY: 2.0),
          child: CustomImageView(
            url: Assets.assetsImagesRegisterPattern,
            alignment: Alignment.topRight,
            height: 350.h(context),
            width: context.screenWidth * 0.35,
            fit: BoxFit.fill,
          ),
        ),
        Padding(
          padding: ResponsiveHelper.getContentPadding(context),
          child: Form(
            key: _formKey,
            child: Column(
              children: [
                (isSmallScreen ? 15 : 25).verticalSpaceResponsive(context),

                Text(
                  'login_title'.tr(),
                  textAlign: TextAlign.center,
                  style: AppTextStyles.title.copyWith(
                    fontSize: ResponsiveHelper.getTitleFontSize(context),
                  ),
                ),

                ResponsiveHelper.getSpacing(
                  context,
                  type: 'small',
                ).verticalSpace,

                Text(
                  'login_subtitle'.tr(),
                  textAlign: TextAlign.center,
                  style: AppTextStyles.bodyMedium.copyWith(
                    fontSize: ResponsiveHelper.getDescriptionFontSize(context),
                    color: Colors.grey[600],
                  ),
                ),

                ResponsiveHelper.getSpacing(
                  context,
                  type: 'medium',
                ).verticalSpace,

                _buildTabBar(context),

                ResponsiveHelper.getSpacing(
                  context,
                  type: 'large',
                ).verticalSpace,

                ..._buildFormFields(context, isSmallScreen),

                ResponsiveHelper.getSpacing(
                  context,
                  type: 'xlarge',
                ).verticalSpace,
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildTabletLayout(BuildContext context) {
    return Center(
      child: Container(
        constraints: const BoxConstraints(maxWidth: 600),
        child: Stack(
          children: [
            BackdropFilter(
              filter: ImageFilter.blur(sigmaX: 2.0, sigmaY: 2.0),
              child: CustomImageView(
                url: Assets.assetsImagesRegisterPattern,
                alignment: Alignment.topRight,
                height: 257.h(context),
                width: context.screenWidth * 0.3,
                fit: BoxFit.fill,
              ),
            ),
            Padding(
              padding: ResponsiveHelper.getContentPadding(context),
              child: Form(
                key: _formKey,
                child: Column(
                  children: [
                    40.verticalSpaceResponsive(context),

                    Text(
                      'login_title'.tr(),
                      textAlign: TextAlign.center,
                      style: AppTextStyles.title.copyWith(
                        fontSize: ResponsiveHelper.getTitleFontSize(context),
                      ),
                    ),

                    ResponsiveHelper.getSpacing(
                      context,
                      type: 'small',
                    ).verticalSpace,

                    Text(
                      'login_subtitle'.tr(),
                      textAlign: TextAlign.center,
                      style: AppTextStyles.bodyMedium.copyWith(
                        fontSize: ResponsiveHelper.getDescriptionFontSize(
                          context,
                        ),
                        color: Colors.grey[600],
                      ),
                    ),

                    ResponsiveHelper.getSpacing(
                      context,
                      type: 'medium',
                    ).verticalSpace,

                    _buildTabBar(context),

                    ResponsiveHelper.getSpacing(
                      context,
                      type: 'xlarge',
                    ).verticalSpace,

                    ..._buildFormFields(context, false),

                    ResponsiveHelper.getSpacing(
                      context,
                      type: 'xlarge',
                    ).verticalSpace,
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildLandscapeTabletLayout(BuildContext context) {
    return Row(
      children: [
        BackdropFilter(
          filter: ImageFilter.blur(sigmaX: 2.0, sigmaY: 2.0),
          child: CustomImageView(
            url: Assets.assetsImagesRegisterPattern,
            alignment: Alignment.center,
            height: 257.h(context),
            width: context.screenWidth * .3,
            fit: BoxFit.cover,
          ),
        ),
        Expanded(
          flex: 1,
          child: SingleChildScrollView(
            padding: ResponsiveHelper.getContentPadding(context),
            child: Form(
              key: _formKey,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  24.verticalSpaceResponsive(context),

                  Text(
                    'login_title'.tr(),
                    textAlign: TextAlign.center,
                    style: AppTextStyles.title.copyWith(
                      fontSize: ResponsiveHelper.getTitleFontSize(context),
                    ),
                  ),

                  ResponsiveHelper.getSpacing(
                    context,
                    type: 'small',
                  ).verticalSpace,

                  Text(
                    'login_subtitle'.tr(),
                    textAlign: TextAlign.center,
                    style: AppTextStyles.bodyMedium.copyWith(
                      fontSize: ResponsiveHelper.getDescriptionFontSize(
                        context,
                      ),
                      color: Colors.grey[600],
                    ),
                  ),

                  ResponsiveHelper.getSpacing(
                    context,
                    type: 'medium',
                  ).verticalSpace,

                  _buildTabBar(context),

                  ResponsiveHelper.getSpacing(
                    context,
                    type: 'large',
                  ).verticalSpace,

                  ..._buildFormFields(context, false),

                  ResponsiveHelper.getSpacing(
                    context,
                    type: 'large',
                  ).verticalSpace,
                ],
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildTabBar(BuildContext context) {
    return CustomTabBar(
      tabs: [
        CustomTab(text: 'register'.tr()),
        CustomTab(text: 'login'.tr()),
      ],
      selectedIndex: 1,
      onTabSelected: (index) {
        if (index == 0) {
          context.goToRegister();
        }
      },
    );
  }

  List<Widget> _buildFormFields(BuildContext context, bool isSmallScreen) {
    final spacing = isSmallScreen
        ? ResponsiveHelper.getSpacing(context, type: 'medium')
        : ResponsiveHelper.getSpacing(context, type: 'large');

    return [
      PhoneTextField(
        label: 'phone_number'.tr(),
        controller: _phoneController,
        // validator: _validatePhone,
        labelAlignment: CrossAxisAlignment.end,
        filled: true,
        fillColor: AppColors.surfaceVariant,
        textAlign: TextAlign.right,
        initialCountry: _selectedCountry,
        onCountryChanged: (country) {
          setState(() {
            _selectedCountry = country;
          });
        },
        contentPadding: const EdgeInsets.symmetric(
          horizontal: 16,
          vertical: 16,
        ),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(AppDimensions.radiusMedium),
          borderSide: BorderSide(color: AppColors.borderColor),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(AppDimensions.radiusMedium),
          borderSide: BorderSide(color: AppColors.borderColor),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(AppDimensions.radiusMedium),
          borderSide: BorderSide(color: AppColors.primary, width: 2),
        ),
        errorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(AppDimensions.radiusMedium),
          borderSide: BorderSide(color: AppColors.error),
        ),
        countryCodeTextColor: AppColors.textPrimary,
      ),

      spacing.verticalSpace,

      CustomTextField(
        label: 'password'.tr(),
        controller: _passwordController,
        hintText: 'enter_password'.tr(),
        obscureText: _obscurePassword,
        validator: _validatePassword,
        textAlign: TextAlign.right,
        labelAlignment: CrossAxisAlignment.end,
        suffixIcon: IconButton(
          icon: Icon(
            _obscurePassword ? Icons.visibility_off : Icons.visibility,
            color: Colors.grey[400],
          ),
          onPressed: () => setState(() => _obscurePassword = !_obscurePassword),
        ),
      ),

      spacing.verticalSpace,

      Row(
        mainAxisAlignment: MainAxisAlignment.end,
        children: [
          TextButton(
            style: TextButton.styleFrom(padding: EdgeInsets.zero),
            onPressed: () {
              // TODO: Navigate to forgot password
            },
            child: Text(
              'forgot_password'.tr(),
              style: AppTextStyles.labelSmall.copyWith(
                color: AppColors.primary,
              ),
            ),
          ),
        ],
      ),

      spacing.verticalSpace,

      BlocBuilder<AuthCubit, AuthState>(
        builder: (context, state) {
          return CustomButton(
            text: 'login'.tr(),
            isLoading: state is AuthLoading,
            onPressed: _submitForm,
            isFullWidth: true,
            size: ButtonSize.large,
          );
        },
      ),

      spacing.verticalSpace,

      // Text(
      //   'or_through'.tr(),
      //   textAlign: TextAlign.center,
      //   style: TextStyle(
      //     fontSize: ResponsiveHelper.getDescriptionFontSize(context),
      //     color: Colors.grey[600],
      //   ),
      // ),

      // spacing.verticalSpace,

      // SocialAuthButtons(userType: 1, fcmToken: null),
    ];
  }
}
