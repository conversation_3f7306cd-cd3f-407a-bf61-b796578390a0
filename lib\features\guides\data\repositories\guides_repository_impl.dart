// features/guides/data/repositories/guides_repository_impl.dart
import 'package:dartz/dartz.dart';
import 'package:tripooo_user/core/error/exceptions.dart';
import 'package:tripooo_user/core/error/failures.dart';
import 'package:tripooo_user/core/network/network_info.dart';
import 'package:tripooo_user/features/guides/data/datasources/guides_remote_data_source.dart';
import 'package:tripooo_user/features/guides/domain/entities/guide_entity.dart';
import 'package:tripooo_user/features/guides/domain/repositories/guides_repository.dart';

class GuidesRepositoryImpl implements GuidesRepository {
  final GuidesRemoteDataSource remoteDataSource;
  final NetworkInfo networkInfo;

  GuidesRepositoryImpl({
    required this.remoteDataSource,
    required this.networkInfo,
  });

  @override
  Future<Either<Failure, List<GuideEntity>>> getGuides({
    int? countryId,
    int? dayPriceFrom,
    int? dayPriceTo,
    int? hourPriceFrom,
    int? hourPriceTo,
    int page = 1,
    int limit = 10,
  }) async {
    if (await networkInfo.isConnected) {
      try {
        final guides = await remoteDataSource.getGuides(
          countryId: countryId,
          dayPriceFrom: dayPriceFrom,
          dayPriceTo: dayPriceTo,
          hourPriceFrom: hourPriceFrom,
          hourPriceTo: hourPriceTo,
          page: page,
          limit: limit,
        );
        return Right(guides.map((model) => model.toEntity()).toList());
      } on ServerException catch (e) {
        return Left(ServerFailure(e.message));
      } catch (e) {
        return Left(ServerFailure(e.toString()));
      }
    } else {
      return Left(NetworkFailure('No internet connection'));
    }
  }

  @override
  Future<Either<Failure, List<GuideEntity>>> getFeaturedGuides({
    int limit = 4,
  }) async {
    if (await networkInfo.isConnected) {
      try {
        final guides = await remoteDataSource.getFeaturedGuides(limit: limit);
        return Right(guides.map((model) => model.toEntity()).toList());
      } on ServerException catch (e) {
        return Left(ServerFailure(e.message));
      } catch (e) {
        return Left(ServerFailure(e.toString()));
      }
    } else {
      return Left(NetworkFailure('No internet connection'));
    }
  }

  @override
  Future<Either<Failure, GuideEntity>> getGuideById(int id) async {
    if (await networkInfo.isConnected) {
      try {
        final guide = await remoteDataSource.getGuideById(id);
        return Right(guide.toEntity());
      } on ServerException catch (e) {
        return Left(ServerFailure(e.message));
      } catch (e) {
        return Left(ServerFailure(e.toString()));
      }
    } else {
      return Left(NetworkFailure('No internet connection'));
    }
  }

  @override
  Future<Either<Failure, List<GuideEntity>>> searchGuides({
    required String query,
    int page = 1,
    int limit = 10,
  }) async {
    if (await networkInfo.isConnected) {
      try {
        final guides = await remoteDataSource.searchGuides(
          query: query,
          page: page,
          limit: limit,
        );
        return Right(guides.map((model) => model.toEntity()).toList());
      } on ServerException catch (e) {
        return Left(ServerFailure(e.message));
      } catch (e) {
        return Left(ServerFailure(e.toString()));
      }
    } else {
      return Left(NetworkFailure('No internet connection'));
    }
  }

  @override
  Future<Either<Failure, List<GuideEntity>>> getGuidesByInterests({
    required List<String> interests,
    int page = 1,
    int limit = 10,
  }) async {
    if (await networkInfo.isConnected) {
      try {
        // For now, we'll use the general getGuides method
        // In a real implementation, you might have a specific endpoint for this
        final guides = await remoteDataSource.getGuides(
          page: page,
          limit: limit,
        );

        // Filter by interests on the client side
        final filteredGuides = guides.where((guide) {
          return guide.interests.any(
            (interest) => interests.any(
              (searchInterest) =>
                  interest.toLowerCase().contains(searchInterest.toLowerCase()),
            ),
          );
        }).toList();

        return Right(filteredGuides.map((model) => model.toEntity()).toList());
      } on ServerException catch (e) {
        return Left(ServerFailure(e.message));
      } catch (e) {
        return Left(ServerFailure(e.toString()));
      }
    } else {
      return Left(NetworkFailure('No internet connection'));
    }
  }

  @override
  Future<Either<Failure, List<GuideEntity>>> getGuidesByTourTypes({
    required List<String> tourTypes,
    int page = 1,
    int limit = 10,
  }) async {
    if (await networkInfo.isConnected) {
      try {
        // For now, we'll use the general getGuides method
        // In a real implementation, you might have a specific endpoint for this
        final guides = await remoteDataSource.getGuides(
          page: page,
          limit: limit,
        );

        // Filter by tour types on the client side
        final filteredGuides = guides.where((guide) {
          return guide.toursType.any(
            (tourType) => tourTypes.any(
              (searchTourType) =>
                  tourType.toLowerCase().contains(searchTourType.toLowerCase()),
            ),
          );
        }).toList();

        return Right(filteredGuides.map((model) => model.toEntity()).toList());
      } on ServerException catch (e) {
        return Left(ServerFailure(e.message));
      } catch (e) {
        return Left(ServerFailure(e.toString()));
      }
    } else {
      return Left(NetworkFailure('No internet connection'));
    }
  }
}
