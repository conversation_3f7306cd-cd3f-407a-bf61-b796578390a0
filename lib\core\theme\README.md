# Theme System Documentation

## Overview
This theme system provides a comprehensive design system for the Tripooo app, ensuring consistency across all UI components.

## Files Structure

### 1. `app_theme.dart`
Main theme configuration file that defines:
- Light and dark themes
- Material 3 design system
- Component-specific themes (buttons, inputs, cards, etc.)

### 2. `app_colors.dart`
Color palette based on design specifications:
- **Primary Colors**: Blue (#007AFF) for main actions
- **Secondary Colors**: Green (#34C759) for phone country code
- **Text Colors**: Black (#000) for headings, gray variants for secondary text
- **State Colors**: Error, warning, success, info
- **Background Colors**: White backgrounds, light gray for surfaces

### 3. `app_text_styles.dart`
Typography system based on Almarai font family:
- **Heading**: 30px, weight 700, 100% line height
- **Subtitle**: 16px, weight 400, 160% line height  
- **Button Text**: 16px, weight 700, 100% line height, white color
- **Body Text**: Various sizes (18px, 16px, 14px)
- **Labels**: Different weights and sizes for form labels

### 4. `app_dimensions.dart`
Spacing and sizing constants:
- **Spacing**: 4px to 64px increments
- **Border Radius**: 4px (small) to 16px (large)
- **Button Heights**: 36px (small), 48px (medium), 56px (large)
- **Icon Sizes**: 16px to 48px
- **Screen Padding**: 24px horizontal, 16px vertical

## Usage Examples

### Using Colors
```dart
import 'package:tripooo_user/core/theme/theme.dart';

Container(
  color: AppColors.primary,
  child: Text(
    'Hello',
    style: TextStyle(color: AppColors.white),
  ),
)
```

### Using Text Styles
```dart
Text(
  'Main Heading',
  style: AppTextStyles.heading,
)

Text(
  'Subtitle text',
  style: AppTextStyles.subtitle,
)

ElevatedButton(
  child: Text(
    'Button',
    style: AppTextStyles.buttonText,
  ),
)
```

### Using Dimensions
```dart
Container(
  padding: EdgeInsets.all(AppDimensions.paddingLarge),
  margin: EdgeInsets.symmetric(
    horizontal: AppDimensions.marginMedium,
  ),
  decoration: BoxDecoration(
    borderRadius: BorderRadius.circular(AppDimensions.radiusMedium),
  ),
)
```

### Using Theme in Widgets
The custom widgets automatically use the theme system:

```dart
CustomTextField(
  label: 'Email',
  // Automatically uses AppTextStyles.fieldLabel for label
  // Automatically uses AppColors.borderColor for borders
  // Automatically uses AppDimensions.radiusMedium for border radius
)

CustomButton(
  text: 'Submit',
  // Automatically uses AppTextStyles.buttonText for text
  // Automatically uses AppColors.primary for background
  // Automatically uses AppDimensions.buttonHeightMedium for height
)
```

## Design Specifications Implemented

### Heading Style
- Color: #000 (Black)
- Font: Almarai
- Size: 30px
- Weight: 700
- Line Height: 100%

### Subtitle Style  
- Color: #000 (Black)
- Font: Almarai
- Size: 16px
- Weight: 400
- Line Height: 160%

### Button Text Style
- Color: #FFF (White)
- Font: Almarai  
- Size: 16px
- Weight: 700
- Line Height: 100%

## Benefits

1. **Consistency**: All components use the same design tokens
2. **Maintainability**: Change colors/fonts in one place
3. **Scalability**: Easy to add new styles and components
4. **Design System**: Follows Material 3 guidelines
5. **Accessibility**: Proper contrast ratios and sizing
6. **Localization**: RTL support with Almarai font

## Integration

Import the theme in your main app:

```dart
import 'package:tripooo_user/core/theme/theme.dart';

MaterialApp(
  theme: AppTheme.lightTheme,
  darkTheme: AppTheme.darkTheme,
  // ... rest of app
)
```

All custom widgets automatically inherit these theme values, ensuring consistent design across the entire application.
