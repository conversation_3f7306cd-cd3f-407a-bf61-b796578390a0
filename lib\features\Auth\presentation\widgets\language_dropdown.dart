// features/Auth/presentation/widgets/language_dropdown.dart
import 'package:flutter/material.dart';
import 'package:easy_localization/easy_localization.dart';

class LanguageDropdown extends StatelessWidget {
  final Color iconColor;
  final Color dropdownColor;
  final TextStyle? textStyle;

  const LanguageDropdown({
    super.key,
    this.iconColor = Colors.white,
    this.dropdownColor = Colors.blue,
    this.textStyle,
  });

  @override
  Widget build(BuildContext context) {
    return DropdownButtonHideUnderline(
      child: DropdownButton<Locale>(
        value: context.locale,
        icon: Icon(Icons.language, color: iconColor),
        dropdownColor: dropdownColor,
        style: textStyle ?? const TextStyle(color: Colors.white, fontSize: 18),
        items: [
          DropdownMenuItem(
            value: const Locale('ar'),
            child: Row(
              children: [
                Icon(Icons.language, color: iconColor),
                const SizedBox(width: 8),
                Text('العربية (Arabic)', style: TextStyle(color: iconColor)),
              ],
            ),
          ),
          DropdownMenuItem(
            value: const Locale('en'),
            child: Row(
              children: [
                Icon(Icons.language, color: iconColor),
                const SizedBox(width: 8),
                Text(
                  'English (الإنجليزية)',
                  style: TextStyle(color: iconColor),
                ),
              ],
            ),
          ),
          DropdownMenuItem(
            value: const Locale('fr'),
            child: Row(
              children: [
                Icon(Icons.language, color: iconColor),
                const SizedBox(width: 8),
                Text('Français (الفرنسية)', style: TextStyle(color: iconColor)),
              ],
            ),
          ),
          DropdownMenuItem(
            value: const Locale('tr'),
            child: Row(
              children: [
                Icon(Icons.language, color: iconColor),
                const SizedBox(width: 8),
                Text('Türkçe (التركية)', style: TextStyle(color: iconColor)),
              ],
            ),
          ),
          DropdownMenuItem(
            value: const Locale('hi'),
            child: Row(
              children: [
                Icon(Icons.language, color: iconColor),
                const SizedBox(width: 8),
                Text('हिन्दी (Hindi)', style: TextStyle(color: iconColor)),
              ],
            ),
          ),
        ],
        onChanged: (locale) {
          if (locale != null) {
            context.setLocale(locale);
          }
        },
      ),
    );
  }
}
