// features/Auth/domain/usecases/register.dart
import 'dart:io';
import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';
import '../../../../core/error/failures.dart';
import '../../../../core/usecases/usecase.dart';
import '../repositories/auth_repository.dart';

class Register implements UseCase<void, RegisterParams> {
  final AuthRepository repository;

  const Register(this.repository);

  @override
  Future<Either<Failure, void>> call(RegisterParams params) async {
    return await repository.register(
      name: params.name,
      email: params.email,
      mobile: params.mobile,
      password: params.password,
      passwordConfirmation: params.passwordConfirmation,
      userType: params.userType,
      file: params.file,
      fcmToken: params.fcmToken,
      dayPrice: params.dayPrice,
      hourPrice: params.hourPrice,
      info: params.info,
      experience: params.experience,
      interests: params.interests,
      toursType: params.toursType,
      languages: params.languages,
      branchName: params.branchName,
      branchEmail: params.branchEmail,
      branchPayments: params.branchPayments,
    );
  }
}

class RegisterParams extends Equatable {
  final String name;
  final String email;
  final String mobile;
  final String password;
  final String passwordConfirmation;
  final int userType;
  final File? file;
  final String? fcmToken;
  final double? dayPrice;
  final double? hourPrice;
  final String? info;
  final String? experience;
  final List<String>? interests;
  final List<String>? toursType;
  final List<int>? languages;
  final String? branchName;
  final String? branchEmail;
  final List<String>? branchPayments;

  const RegisterParams({
    required this.name,
    required this.email,
    required this.mobile,
    required this.password,
    required this.passwordConfirmation,
    required this.userType,
    this.file,
    this.fcmToken,
    this.dayPrice,
    this.hourPrice,
    this.info,
    this.experience,
    this.interests,
    this.toursType,
    this.languages,
    this.branchName,
    this.branchEmail,
    this.branchPayments,
  });

  @override
  List<Object?> get props => [
        name,
        email,
        mobile,
        password,
        passwordConfirmation,
        userType,
        file,
        fcmToken,
        dayPrice,
        hourPrice,
        info,
        experience,
        interests,
        toursType,
        languages,
        branchName,
        branchEmail,
        branchPayments,
      ];
}
