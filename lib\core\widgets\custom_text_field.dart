// core/widgets/custom_text_field.dart
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../theme/theme.dart';

class CustomTextField extends StatelessWidget {
  final String? label;
  final String? hintText;
  final TextEditingController? controller;
  final String? Function(String?)? validator;
  final TextInputType? keyboardType;
  final bool obscureText;
  final bool enabled;
  final bool readOnly;
  final int? maxLines;
  final int? minLines;
  final int? maxLength;
  final Widget? prefixIcon;
  final Widget? suffixIcon;
  final String? prefixText;
  final String? suffixText;
  final bool rtlPrefixText;
  final TextAlign textAlign;
  final TextStyle? textStyle;
  final TextStyle? labelStyle;
  final TextStyle? hintStyle;
  final EdgeInsetsGeometry? contentPadding;
  final InputBorder? border;
  final InputBorder? enabledBorder;
  final InputBorder? focusedBorder;
  final InputBorder? errorBorder;
  final InputBorder? disabledBorder;
  final Color? fillColor;
  final bool filled;
  final VoidCallback? onTap;
  final Function(String)? onChanged;
  final Function(String)? onSubmitted;
  final FocusNode? focusNode;
  final List<TextInputFormatter>? inputFormatters;
  final TextCapitalization textCapitalization;
  final bool autofocus;
  final String? errorText;
  final Widget? prefix;
  final Widget? suffix;
  final double? labelSpacing;
  final CrossAxisAlignment labelAlignment;
  final bool showLabel;

  const CustomTextField({
    super.key,
    this.label,
    this.hintText,
    this.controller,
    this.validator,
    this.keyboardType,
    this.obscureText = false,
    this.enabled = true,
    this.readOnly = false,
    this.maxLines = 1,
    this.minLines,
    this.maxLength,
    this.prefixIcon,
    this.suffixIcon,
    this.prefixText,
    this.suffixText,
    this.textAlign = TextAlign.start,
    this.textStyle,
    this.labelStyle,
    this.hintStyle,
    this.contentPadding,
    this.border,
    this.enabledBorder,
    this.focusedBorder,
    this.errorBorder,
    this.disabledBorder,
    this.fillColor,
    this.filled = false,
    this.onTap,
    this.onChanged,
    this.onSubmitted,
    this.focusNode,
    this.inputFormatters,
    this.textCapitalization = TextCapitalization.none,
    this.autofocus = false,
    this.errorText,
    this.prefix,
    this.suffix,
    this.labelSpacing = 8.0,
    this.labelAlignment = CrossAxisAlignment.start,
    this.showLabel = true,
    this.rtlPrefixText = false,
  });

  @override
  Widget build(BuildContext context) {
    final textDirection = Directionality.of(context);

    CrossAxisAlignment effectiveLabelAlignment;
    if (labelAlignment == CrossAxisAlignment.start) {
      effectiveLabelAlignment = textDirection == TextDirection.rtl
          ? CrossAxisAlignment.end
          : CrossAxisAlignment.start;
    } else if (labelAlignment == CrossAxisAlignment.end) {
      effectiveLabelAlignment = textDirection == TextDirection.rtl
          ? CrossAxisAlignment.start
          : CrossAxisAlignment.end;
    } else {
      effectiveLabelAlignment = labelAlignment;
    }

    return Column(
      crossAxisAlignment: effectiveLabelAlignment,
      children: [
        if (label != null && showLabel) ...[
          Text(label!, style: labelStyle ?? AppTextStyles.fieldLabel),
          SizedBox(height: labelSpacing),
        ],
        TextFormField(
          controller: controller,
          validator: validator,
          keyboardType: keyboardType,
          obscureText: obscureText,
          enabled: enabled,
          readOnly: readOnly,
          maxLines: maxLines,
          minLines: minLines,
          maxLength: maxLength,
          textAlign: textAlign == TextAlign.start
              ? (textDirection == TextDirection.rtl
                    ? TextAlign.right
                    : TextAlign.left)
              : textAlign,
          style: textStyle,
          onTap: onTap,
          onChanged: onChanged,
          onFieldSubmitted: onSubmitted,
          focusNode: focusNode,
          inputFormatters: inputFormatters,
          textCapitalization: textCapitalization,
          autofocus: autofocus,
          decoration: InputDecoration(
            hintText: hintText,
            hintStyle: hintStyle ?? AppTextStyles.hintText,
            prefixIcon: prefixIcon,
            suffixIcon: suffixIcon,
            prefixText: prefixText,
            suffixText: suffixText,
            prefix: prefix,
            suffix: suffix,
            errorText: errorText,
            contentPadding:
                contentPadding ??
                const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            filled: filled,
            fillColor: fillColor,
            border:
                border ??
                OutlineInputBorder(
                  borderRadius: BorderRadius.circular(
                    AppDimensions.radiusMedium,
                  ),
                  borderSide: BorderSide(color: AppColors.borderColor),
                ),
            enabledBorder:
                enabledBorder ??
                OutlineInputBorder(
                  borderRadius: BorderRadius.circular(
                    AppDimensions.radiusMedium,
                  ),
                  borderSide: BorderSide(color: AppColors.borderColor),
                ),
            focusedBorder:
                focusedBorder ??
                OutlineInputBorder(
                  borderRadius: BorderRadius.circular(
                    AppDimensions.radiusMedium,
                  ),
                  borderSide: BorderSide(color: AppColors.primary),
                ),
            errorBorder:
                errorBorder ??
                OutlineInputBorder(
                  borderRadius: BorderRadius.circular(
                    AppDimensions.radiusMedium,
                  ),
                  borderSide: BorderSide(color: AppColors.error),
                ),
            focusedErrorBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(AppDimensions.radiusMedium),
              borderSide: BorderSide(color: AppColors.error, width: 2),
            ),
            disabledBorder:
                disabledBorder ??
                OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                  borderSide: BorderSide(color: Colors.grey[200]!),
                ),
          ),
        ),
      ],
    );
  }
}
