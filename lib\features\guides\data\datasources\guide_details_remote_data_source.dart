// features/guides/data/datasources/guide_details_remote_data_source.dart
import 'package:dio/dio.dart';
import '../../../../core/error/exceptions.dart';
import '../../../../core/network/api_endpoints.dart';
import '../models/guide_details_model.dart';

abstract class GuideDetailsRemoteDataSource {
  Future<GuideDetailsModel> getGuideDetails(int guideId);
}

class GuideDetailsRemoteDataSourceImpl implements GuideDetailsRemoteDataSource {
  final Dio dio;

  GuideDetailsRemoteDataSourceImpl({required this.dio});

  @override
  Future<GuideDetailsModel> getGuideDetails(int guideId) async {
    try {
      final response = await dio.get(
        ApiEndpoints.getFullUrl(ApiEndpoints.guideDetails(guideId)),
        options: Options(headers: ApiEndpoints.defaultHeaders),
      );

      if (response.statusCode == 200) {
        final data = response.data;
        if (data['status'] == 200 && data['data'] != null) {
          return GuideDetailsModel.fromJson(data['data']);
        } else {
          throw ServerException(
            data['msg'] ?? 'Failed to get guide details',
            data['status'] ?? 500,
          );
        }
      } else {
        throw ServerException(
          'Failed to get guide details',
          response.statusCode ?? 500,
        );
      }
    } on DioException catch (e) {
      if (e.response != null) {
        final data = e.response!.data;
        throw ServerException(
          data['msg'] ?? 'Server error occurred',
          e.response!.statusCode ?? 500,
        );
      } else {
        throw ServerException('Network error occurred', 0);
      }
    } catch (e) {
      throw ServerException('Unexpected error occurred: ${e.toString()}', 500);
    }
  }
}
