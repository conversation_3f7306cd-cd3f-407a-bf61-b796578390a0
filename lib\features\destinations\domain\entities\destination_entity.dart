// features/destinations/domain/entities/destination_entity.dart
import 'package:equatable/equatable.dart';
import 'destination_translation_entity.dart';

class DestinationEntity extends Equatable {
  final int id;
  final String createdAt;
  final String updatedAt;
  final String? img;
  final List<String> albums;
  final int dayPrice;
  final String mapLocation;
  final int? countryId;
  final String? deletedAt;
  final int isActivate;
  final String name;
  final String info;
  final String location;
  final List<DestinationTranslationEntity> translations;

  const DestinationEntity({
    required this.id,
    required this.createdAt,
    required this.updatedAt,
    this.img,
    required this.albums,
    required this.dayPrice,
    required this.mapLocation,
    this.countryId,
    this.deletedAt,
    required this.isActivate,
    required this.name,
    required this.info,
    required this.location,
    required this.translations,
  });

  // Helper methods
  bool get isActive => isActivate == 1;

  bool get hasImage => img != null && img!.isNotEmpty;

  String get imageUrl => hasImage ? 'https://tripooo.tptechcorp.com/$img' : '';

  List<String> get albumUrls => albums
      .where((album) => album.isNotEmpty)
      .map((album) => 'https://tripooo.tptechcorp.com/$album')
      .toList();

  bool get hasAlbums => albumUrls.isNotEmpty;

  String get formattedPrice => '$dayPrice ريال/يوم';

  String getTranslatedName(String locale) {
    final translation = translations.firstWhere(
      (t) => t.locale == locale && t.name != null,
      orElse: () => translations.isNotEmpty 
          ? translations.first 
          : DestinationTranslationEntity(
              id: 0, 
              destinationId: id, 
              locale: locale,
              name: name,
              info: info,
              location: location,
            ),
    );
    return translation.name ?? name;
  }

  String getTranslatedInfo(String locale) {
    final translation = translations.firstWhere(
      (t) => t.locale == locale && t.info != null,
      orElse: () => translations.isNotEmpty 
          ? translations.first 
          : DestinationTranslationEntity(
              id: 0, 
              destinationId: id, 
              locale: locale,
              name: name,
              info: info,
              location: location,
            ),
    );
    return translation.info ?? info;
  }

  String getTranslatedLocation(String locale) {
    final translation = translations.firstWhere(
      (t) => t.locale == locale && t.location != null,
      orElse: () => translations.isNotEmpty 
          ? translations.first 
          : DestinationTranslationEntity(
              id: 0, 
              destinationId: id, 
              locale: locale,
              name: name,
              info: info,
              location: location,
            ),
    );
    return translation.location ?? location;
  }

  String get displayName => name.isNotEmpty ? name : 'وجهة سياحية';
  
  String get displayInfo => info.isNotEmpty ? info : 'وجهة سياحية رائعة';
  
  String get displayLocation => location.isNotEmpty ? location : 'موقع مميز';

  @override
  List<Object?> get props => [
        id,
        createdAt,
        updatedAt,
        img,
        albums,
        dayPrice,
        mapLocation,
        countryId,
        deletedAt,
        isActivate,
        name,
        info,
        location,
        translations,
      ];
}
