// features/destinations/data/models/destination_model.dart
import '../../domain/entities/destination_entity.dart';
import 'destination_translation_model.dart';

class DestinationModel extends DestinationEntity {
  const DestinationModel({
    required super.id,
    required super.createdAt,
    required super.updatedAt,
    super.img,
    required super.albums,
    required super.dayPrice,
    required super.mapLocation,
    super.countryId,
    super.deletedAt,
    required super.isActivate,
    required super.name,
    required super.info,
    required super.location,
    required super.translations,
  });

  factory DestinationModel.fromJson(Map<String, dynamic> json) {
    return DestinationModel(
      id: json['id'] ?? 0,
      createdAt: json['created_at'] ?? '',
      updatedAt: json['updated_at'] ?? '',
      img: json['img'],
      albums: List<String>.from(json['albums'] ?? []),
      dayPrice: json['day_price'] ?? 0,
      mapLocation: json['map_location'] ?? '',
      countryId: json['country_id'],
      deletedAt: json['deleted_at'],
      isActivate: json['is_activate'] ?? 0,
      name: json['name'] ?? '',
      info: json['info'] ?? '',
      location: json['location'] ?? '',
      translations: (json['translations'] as List<dynamic>?)
              ?.map((translation) => DestinationTranslationModel.fromJson(translation))
              .toList() ??
          [],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'created_at': createdAt,
      'updated_at': updatedAt,
      'img': img,
      'albums': albums,
      'day_price': dayPrice,
      'map_location': mapLocation,
      'country_id': countryId,
      'deleted_at': deletedAt,
      'is_activate': isActivate,
      'name': name,
      'info': info,
      'location': location,
      'translations': translations
          .map((translation) => (translation as DestinationTranslationModel).toJson())
          .toList(),
    };
  }
}
