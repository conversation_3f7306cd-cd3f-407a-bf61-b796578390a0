// features/guides/domain/entities/country_entity.dart
import 'package:equatable/equatable.dart';

class CountryEntity extends Equatable {
  final int id;
  final String createdAt;
  final String updatedAt;
  final String name;
  final String? deletedAt;
  final int isActivate;

  const CountryEntity({
    required this.id,
    required this.createdAt,
    required this.updatedAt,
    required this.name,
    this.deletedAt,
    required this.isActivate,
  });

  // Helper methods
  bool get isActive => isActivate == 1;

  @override
  List<Object?> get props => [
        id,
        createdAt,
        updatedAt,
        name,
        deletedAt,
        isActivate,
      ];
}
