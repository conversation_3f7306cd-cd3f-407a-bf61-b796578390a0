// features/guides/domain/usecases/get_guides_usecase.dart
import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';
import 'package:tripooo_user/core/error/failures.dart';
import 'package:tripooo_user/core/usecases/usecase.dart';
import 'package:tripooo_user/features/guides/domain/entities/guide_entity.dart';
import 'package:tripooo_user/features/guides/domain/repositories/guides_repository.dart';

class GetGuidesUseCase implements UseCase<List<GuideEntity>, GetGuidesParams> {
  final GuidesRepository repository;

  GetGuidesUseCase(this.repository);

  @override
  Future<Either<Failure, List<GuideEntity>>> call(GetGuidesParams params) async {
    return await repository.getGuides(
      countryId: params.countryId,
      dayPriceFrom: params.dayPriceFrom,
      dayPriceTo: params.dayPriceTo,
      hourPriceFrom: params.hourPriceFrom,
      hourPriceTo: params.hourPriceTo,
      page: params.page,
      limit: params.limit,
    );
  }
}

class GetGuidesParams extends Equatable {
  final int? countryId;
  final int? dayPriceFrom;
  final int? dayPriceTo;
  final int? hourPriceFrom;
  final int? hourPriceTo;
  final int page;
  final int limit;

  const GetGuidesParams({
    this.countryId,
    this.dayPriceFrom,
    this.dayPriceTo,
    this.hourPriceFrom,
    this.hourPriceTo,
    this.page = 1,
    this.limit = 10,
  });

  @override
  List<Object?> get props => [
        countryId,
        dayPriceFrom,
        dayPriceTo,
        hourPriceFrom,
        hourPriceTo,
        page,
        limit,
      ];
}
