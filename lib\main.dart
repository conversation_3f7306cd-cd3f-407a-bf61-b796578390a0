// main.dart
import 'package:clarity_flutter/clarity_flutter.dart';
import 'package:flutter/material.dart';
import 'package:easy_localization/easy_localization.dart' hide TextDirection;
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:tripooo_user/core/bloc_observer.dart';

import 'features/Auth/presentation/cubit/auth_cubit.dart';
import 'core/di/injection_container.dart' as di;
import 'core/routing/app_router.dart';
import 'core/services/language_service.dart';
import 'core/services/fcm_service.dart';
import 'core/services/fcm_helper.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  await EasyLocalization.ensureInitialized();

  await LanguageService.initializeLanguage();
  Bloc.observer = AppBlocObserver();

  // Initialize FCM
  await FCMService.initialize();

  // Setup notification handlers
  FCMService.setupNotificationHandlers();

  // Handle initial message if app was opened from notification
  await FCMService.handleInitialMessage();

  await di.init();

  // Setup FCM token refresh listener
  FCMHelper.setupTokenRefreshListener();
  final config = ClarityConfig(
    projectId: "siuybjsfvm",
    logLevel: LogLevel.None,
  );
  runApp(
    EasyLocalization(
      supportedLocales: [
        Locale('ar'),
        Locale('en'),
        Locale('fr'),
        Locale('tr'),
        Locale('hi'),
      ],
      path: 'assets/translations',

      fallbackLocale: Locale('ar'),
      startLocale: Locale('ar'),
      saveLocale: true,
      child: BlocProvider(
        create: (_) => di.sl<AuthCubit>(),
        child: ClarityWidget(app: MainApp(), clarityConfig: config),
      ),
    ),
  );
}

class MainApp extends StatelessWidget {
  const MainApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp.router(
      debugShowCheckedModeBanner: false,
      localizationsDelegates: context.localizationDelegates,
      supportedLocales: context.supportedLocales,
      locale: context.locale,
      routerConfig: AppRouter.router,
      builder: (context, child) {
        return child!;
      },
    );
  }
}
