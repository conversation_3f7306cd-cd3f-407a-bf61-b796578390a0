// features/Auth/domain/usecases/get_user.dart
import 'package:dartz/dartz.dart';
import '../../../../core/error/failures.dart';
import '../../../../core/usecases/usecase.dart';
import '../entities/user.dart';
import '../repositories/auth_repository.dart';

class GetUser implements UseCaseWithoutParams<User> {
  final AuthRepository repository;

  const GetUser(this.repository);

  @override
  Future<Either<Failure, User>> call() async {
    return await repository.getUser();
  }
}
