// features/wallet/presentation/pages/topup_success_screen.dart
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:tripooo_user/core/routing/app_router.dart';
import 'package:tripooo_user/core/theme/theme.dart';

class TopupSuccessScreen extends StatelessWidget {
  final String? amount;
  const TopupSuccessScreen({super.key, this.amount});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        elevation: 0,
        backgroundColor: AppColors.primary,
        foregroundColor: Colors.white,
      ),
      body: Column(
        children: [
          const SizedBox(height: 24),
          const Icon(Icons.check_circle, color: Colors.blue, size: 72),
          const SizedBox(height: 12),
          Text('تم تحويل المبلغ بنجاح!', style: AppTextStyles.title),
          const SizedBox(height: 8),
          Text('تم خصم المبلغ من محفظتك بنجاح', style: AppTextStyles.bodySmall),
          const SizedBox(height: 24),
          Card(
            margin: const EdgeInsets.symmetric(horizontal: 16),
            shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      const Icon(Icons.account_circle, size: 32),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text('أحمد المصري', style: AppTextStyles.labelLarge),
                            Text('9012 5678 1343 0000 12', style: AppTextStyles.bodySmall),
                          ],
                        ),
                      ),
                      Text('${amount ?? '0'} ر.س', style: AppTextStyles.titleSmall.copyWith(color: AppColors.success)),
                    ],
                  ),
                  const SizedBox(height: 12),
                  _Line(label: 'تاريخ عملية الشحن', value: '12 فبراير 2025، 08:45 مساءً'),
                  _Line(label: 'رقم المعاملة', value: '202502108675890124'),
                ],
              ),
            ),
          ),
          const Spacer(),
          SafeArea(
            minimum: const EdgeInsets.all(16),
            child: SizedBox(
              width: double.infinity,
              height: 52,
              child: ElevatedButton(
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.primary,
                  shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
                ),
                onPressed: () => context.go(AppRoutes.walletHome),
                child: Text('تم', style: AppTextStyles.buttonText),
              ),
            ),
          ),
        ],
      ),
    );
  }
}

class _Line extends StatelessWidget {
  final String label;
  final String value;
  const _Line({required this.label, required this.value});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 6),
      child: Row(
        children: [
          Expanded(child: Text(label, style: AppTextStyles.bodySmall)),
          Text(value, style: AppTextStyles.labelMedium),
        ],
      ),
    );
  }
}

