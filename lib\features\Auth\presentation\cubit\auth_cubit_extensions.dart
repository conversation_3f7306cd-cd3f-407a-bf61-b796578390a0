import 'dart:io';
import '../../../../core/utils/fcm_utils.dart';
import 'auth_cubit.dart';

/// Extension methods for AuthCubit to handle FCM tokens automatically
extension AuthCubitFCMExtensions on AuthCubit {
  
  /// Login with automatic FCM token
  Future<void> loginWithFCM({
    required String mobile,
    required String password,
  }) async {
    // Get FCM token automatically
    String? fcmToken = await FCMUtils.getTokenForAuth();
    
    // Call original login method with FCM token
    await login(
      mobile: mobile,
      password: password,
      fcmToken: fcmToken,
    );
  }
  
  /// Register with automatic FCM token
  Future<void> registerWithFCM({
    required String name,
    required String email,
    required String mobile,
    required String password,
    required String passwordConfirmation,
    required int userType,
    File? file,
    // Guide-specific fields
    double? dayPrice,
    double? hourPrice,
    String? info,
    String? experience,
    List<String>? interests,
    List<String>? toursType,
    List<int>? languages,
    // Branch fields (optional)
    String? branchName,
    String? branchEmail,
    List<String>? branchPayments,
  }) async {
    // Get FCM token automatically
    String? fcmToken = await FCMUtils.getTokenForAuth();
    
    // Call original register method with FCM token
    await register(
      name: name,
      email: email,
      mobile: mobile,
      password: password,
      passwordConfirmation: passwordConfirmation,
      userType: userType,
      file: file,
      fcmToken: fcmToken,
      dayPrice: dayPrice,
      hourPrice: hourPrice,
      info: info,
      experience: experience,
      interests: interests,
      toursType: toursType,
      languages: languages,
      branchName: branchName,
      branchEmail: branchEmail,
      branchPayments: branchPayments,
    );
  }
  
  /// Sign in with Google with automatic FCM token
  Future<void> signInWithGoogleWithFCM({int userType = 1}) async {
    // Get FCM token automatically
    String? fcmToken = await FCMUtils.getTokenForAuth();
    
    // Call original method with FCM token
    await signInWithGoogle(fcmToken: fcmToken, userType: userType);
  }
  
  /// Sign in with Facebook with automatic FCM token
  Future<void> signInWithFacebookWithFCM({int userType = 1}) async {
    // Get FCM token automatically
    String? fcmToken = await FCMUtils.getTokenForAuth();
    
    // Call original method with FCM token
    await signInWithFacebook(fcmToken: fcmToken, userType: userType);
  }
  
  /// Sign in with Apple with automatic FCM token
  Future<void> signInWithAppleWithFCM({int userType = 1}) async {
    // Get FCM token automatically
    String? fcmToken = await FCMUtils.getTokenForAuth();
    
    // Call original method with FCM token
    await signInWithApple(fcmToken: fcmToken, userType: userType);
  }
}
