# Guide Details API Documentation

## 📋 Overview

This documentation covers the implementation of the Guide Details API endpoint (`/api/guides/details/{id}`) in the Tripooo User app.

## 🏗️ Architecture

The implementation follows Clean Architecture principles with the following layers:

### 📁 Domain Layer
- **Entities**: Core business objects
- **Repositories**: Abstract interfaces
- **Use Cases**: Business logic

### 📁 Data Layer
- **Models**: Data transfer objects
- **Data Sources**: API communication
- **Repository Implementations**: Concrete implementations

### 📁 Presentation Layer
- **Cubits**: State management
- **Pages**: UI screens
- **Widgets**: Reusable UI components

## 🔗 API Endpoint

```
GET /api/guides/details/{guideId}
```

### Response Structure
```json
{
    "status": 200,
    "msg": "success",
    "data": {
        "id": 12,
        "name": "<PERSON> Pace",
        "email": "<EMAIL>",
        "img": null,
        "mobile": "269",
        "user_type": 2,
        "is_activate": 1,
        "day_price": 420,
        "hour_price": 491,
        "info": "Et pariatur Consequ",
        "experience": "Consequatur iure re",
        "interests": ["Chanda Greer"],
        "tours_type": ["Yvette Dalton"],
        "albums": [""],
        "country_id": 2,
        "fcm_token": null,
        "languages": [...],
        "country": {...},
        "days": [...]
    }
}
```

## 📦 Core Components

### 1. Entities

#### GuideDetailsEntity
```dart
class GuideDetailsEntity extends Equatable {
  final int id;
  final String name;
  final String email;
  final List<LanguageEntity> languages;
  final CountryEntity? country;
  final List<WorkingDayEntity> days;
  // ... other properties
}
```

#### LanguageEntity
```dart
class LanguageEntity extends Equatable {
  final int id;
  final String name;
  final List<LanguageTranslation> translations;
  // ... other properties
}
```

#### WorkingDayEntity
```dart
class WorkingDayEntity extends Equatable {
  final String day;
  final String start;
  final String end;
  final int? dayStatus; // null=available, 1=busy, 2=off
  // ... other properties
}
```

### 2. Use Case

```dart
class GetGuideDetails implements UseCase<GuideDetailsEntity, GetGuideDetailsParams> {
  final GuideDetailsRepository repository;

  GetGuideDetails(this.repository);

  @override
  Future<Either<Failure, GuideDetailsEntity>> call(GetGuideDetailsParams params) async {
    return await repository.getGuideDetails(params.guideId);
  }
}
```

### 3. Cubit

```dart
class GuideDetailsCubit extends Cubit<GuideDetailsState> {
  final GetGuideDetails getGuideDetailsUseCase;

  Future<void> loadGuideDetails(int guideId) async {
    emit(GuideDetailsLoading());
    
    final result = await getGuideDetailsUseCase(
      GetGuideDetailsParams(guideId: guideId),
    );
    
    result.fold(
      (failure) => emit(GuideDetailsError(
        message: failure.message,
        statusCode: failure.statusCode,
      )),
      (guideDetails) => emit(GuideDetailsLoaded(guideDetails: guideDetails)),
    );
  }
}
```

## 🎯 Usage Examples

### 1. Basic Usage in a Page

```dart
class GuideDetailsPage extends StatelessWidget {
  final int guideId;

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => di.sl<GuideDetailsCubit>()..loadGuideDetails(guideId),
      child: Scaffold(
        body: BlocBuilder<GuideDetailsCubit, GuideDetailsState>(
          builder: (context, state) {
            if (state is GuideDetailsLoading) {
              return CircularProgressIndicator();
            } else if (state is GuideDetailsLoaded) {
              return _buildGuideDetails(state.guideDetails);
            } else if (state is GuideDetailsError) {
              return _buildErrorState(state.message);
            }
            return Container();
          },
        ),
      ),
    );
  }
}
```

### 2. Using Guide Details Card Widget

```dart
GuideDetailsCard(
  guide: guideDetails,
  onTap: () {
    // Navigate to full details page
    Navigator.push(context, MaterialPageRoute(
      builder: (context) => GuideDetailsPage(guideId: guideDetails.id),
    ));
  },
)
```

### 3. Accessing Guide Properties

```dart
// Basic info
final name = guide.displayName;
final info = guide.displayInfo;
final experience = guide.displayExperience;

// Pricing
final dayPrice = guide.formattedDayPrice; // "420 ريال/يوم"
final hourPrice = guide.formattedHourPrice; // "491 ريال/ساعة"

// Status
final isActive = guide.isActive;
final isEmailVerified = guide.isEmailVerified;
final isMobileVerified = guide.isMobileVerified;

// Languages
final languageNames = guide.languageNames;
final arabicLanguageNames = guide.getLanguageNamesInLocale('ar');

// Working days
final availableDays = guide.availableDays;
final allWorkingDays = guide.workingDays;
final hasWorkingDays = guide.hasWorkingDays;

// Location
final countryName = guide.countryName;

// Image
final hasImage = guide.hasImage;
final imageUrl = guide.imageUrl;
```

## 🔧 Dependency Injection

Add to `injection_container.dart`:

```dart
// Guide Details Data Sources
sl.registerLazySingleton<GuideDetailsRemoteDataSource>(
  () => GuideDetailsRemoteDataSourceImpl(dio: sl()),
);

// Guide Details Repositories
sl.registerLazySingleton<GuideDetailsRepository>(
  () => GuideDetailsRepositoryImpl(remoteDataSource: sl(), networkInfo: sl()),
);

// Guide Details Use Cases
sl.registerLazySingleton(() => GetGuideDetails(sl()));

// Guide Details Cubits
sl.registerFactory(() => GuideDetailsCubit(getGuideDetailsUseCase: sl()));
```

## 🎨 Responsive Design

The implementation includes full responsive support:

```dart
// Using responsive extensions
final isTablet = ResponsiveHelper.isTablet(context);
final spacing = ResponsiveHelper.getSpacing(context, type: 'large');

// Responsive spacing
spacing.verticalSpaceResponsive(context)

// Responsive padding
widget.paddingSymmetric(
  horizontal: isTablet ? 48 : 24,
  vertical: isTablet ? 32 : 16,
  context: context,
)

// Responsive constraints
ConstrainedBox(
  constraints: BoxConstraints(
    maxWidth: isTablet ? 800.w(context) : double.infinity,
  ),
  child: content,
)
```

## 🧪 Testing

### Unit Tests Example

```dart
group('GetGuideDetails', () {
  test('should return GuideDetailsEntity when call is successful', () async {
    // Arrange
    when(mockRepository.getGuideDetails(any))
        .thenAnswer((_) async => Right(tGuideDetailsEntity));

    // Act
    final result = await usecase(GetGuideDetailsParams(guideId: 1));

    // Assert
    expect(result, Right(tGuideDetailsEntity));
    verify(mockRepository.getGuideDetails(1));
  });
});
```

## 🚀 Features

- ✅ **Complete API Integration**: Full support for guide details endpoint
- ✅ **Responsive Design**: Optimized for mobile and tablet
- ✅ **Error Handling**: Comprehensive error states and retry mechanisms
- ✅ **Type Safety**: Strong typing with Dart entities and models
- ✅ **Clean Architecture**: Separation of concerns and testability
- ✅ **State Management**: BLoC pattern with Cubit
- ✅ **Localization Ready**: Support for multiple languages
- ✅ **Caching Support**: Network-aware data fetching
- ✅ **Reusable Components**: Modular widgets and utilities

## 📝 Notes

1. **Language Support**: The API returns language translations in multiple locales
2. **Working Days**: Day status can be null (available), 1 (busy), or 2 (off)
3. **Image Handling**: Includes fallback for missing guide images
4. **Responsive**: All components adapt to different screen sizes
5. **Error Recovery**: Built-in retry mechanisms for failed requests

## 🔄 Future Enhancements

- [ ] Add caching for guide details
- [ ] Implement offline support
- [ ] Add image caching and optimization 
- [ ] Include guide reviews and ratings
- [ ] Add booking integration
- [ ] Implement real-time availability updates
