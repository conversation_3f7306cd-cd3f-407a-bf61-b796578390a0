// features/Auth/domain/usecases/change_mobile_number.dart
import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';
import '../../../../core/error/failures.dart';
import '../../../../core/usecases/usecase.dart';
import '../repositories/auth_repository.dart';

class ChangeMobileNumber implements UseCase<String, ChangeMobileNumberParams> {
  final AuthRepository repository;

  const ChangeMobileNumber(this.repository);

  @override
  Future<Either<Failure, String>> call(ChangeMobileNumberParams params) async {
    return await repository.changeMobileNumber(mobile: params.mobile);
  }
}

class ChangeMobileNumberParams extends Equatable {
  final String mobile;

  const ChangeMobileNumberParams({required this.mobile});

  @override
  List<Object> get props => [mobile];
}
