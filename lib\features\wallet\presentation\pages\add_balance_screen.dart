// features/wallet/presentation/pages/add_balance_screen.dart
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:tripooo_user/core/theme/theme.dart';
import 'package:tripooo_user/core/routing/app_router.dart';

class AddBalanceScreen extends StatefulWidget {
  const AddBalanceScreen({super.key});

  @override
  State<AddBalanceScreen> createState() => _AddBalanceScreenState();
}

class _AddBalanceScreenState extends State<AddBalanceScreen> {
  final TextEditingController amountCtrl = TextEditingController();
  String method = 'بطاقة الائتمان/الخصم';

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        elevation: 0,
        backgroundColor: Colors.white,
        foregroundColor: Colors.black,
        title: Text('إضافة رصيد', style: AppTextStyles.appBarTitle),
      ),
      body: ListView(
        padding: const EdgeInsets.all(16),
        children: [
          Text('إضافة رصيد إلى المحفظة', style: AppTextStyles.labelLarge),
          const SizedBox(height: 8),
          Text('يمكنك شحن محفظتك باستخدام إحدى طرق الدفع المتاحة.', style: AppTextStyles.bodySmall),
          const SizedBox(height: 16),
          Text('المبلغ المراد إضافته', style: AppTextStyles.fieldLabel),
          const SizedBox(height: 8),
          TextField(
            controller: amountCtrl,
            keyboardType: TextInputType.number,
            decoration: InputDecoration(
              hintText: 'ر.س',
              border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
              enabledBorder: OutlineInputBorder(borderRadius: BorderRadius.circular(12), borderSide: const BorderSide(color: Color(0xFFE0E0E0))),
              focusedBorder: OutlineInputBorder(borderRadius: BorderRadius.circular(12), borderSide: const BorderSide(color: AppColors.primary)),
              prefixIcon: const Icon(Icons.attach_money),
            ),
          ),
          const SizedBox(height: 16),
          Text('اختر طريقة الدفع', style: AppTextStyles.fieldLabel),
          const SizedBox(height: 8),
          _PaymentOption(
            title: 'بطاقة الائتمان/الخصم',
            selected: method == 'بطاقة الائتمان/الخصم',
            onTap: () => setState(() => method = 'بطاقة الائتمان/الخصم'),
            icons: const [Icons.credit_card],
          ),
          _PaymentOption(
            title: 'ابل باي',
            selected: method == 'ابل باي',
            onTap: () => setState(() => method = 'ابل باي'),
            icons: const [Icons.phone_iphone],
          ),
          _PaymentOption(
            title: 'مدى',
            selected: method == 'مدى',
            onTap: () => setState(() => method = 'مدى'),
            icons: const [Icons.payment],
          ),
          _PaymentOption(
            title: 'تابي',
            selected: method == 'تابي',
            onTap: () => setState(() => method = 'تابي'),
            icons: const [Icons.payments],
          ),
        ],
      ),
      bottomNavigationBar: SafeArea(
        minimum: const EdgeInsets.all(16),
        child: SizedBox(
          height: 52,
          child: ElevatedButton(
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.primary,
              shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
            ),
            onPressed: () => context.push(AppRoutes.walletPaymentMethod, extra: amountCtrl.text),
            child: Text('متابعة', style: AppTextStyles.buttonText),
          ),
        ),
      ),
    );
  }
}

class _PaymentOption extends StatelessWidget {
  final String title;
  final bool selected;
  final VoidCallback onTap;
  final List<IconData> icons;

  const _PaymentOption({
    required this.title,
    required this.selected,
    required this.onTap,
    required this.icons,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(12.0),
          child: Row(
            children: [
              const SizedBox(width: 8),
              Expanded(child: Text(title, style: AppTextStyles.bodyMedium)),
              ...icons.map((i) => Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 4.0),
                    child: Icon(i, size: 20),
                  )),
              const SizedBox(width: 8),
              Radio<bool>(value: true, groupValue: selected, onChanged: (_) => onTap()),
            ],
          ),
        ),
      ),
    );
  }
}

