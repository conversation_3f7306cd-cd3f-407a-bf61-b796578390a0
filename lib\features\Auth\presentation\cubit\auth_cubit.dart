// features/Auth/presentation/cubit/auth_cubit.dart
import 'dart:developer';
import 'dart:io';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:local_auth/local_auth.dart';
import '../../data/services/local_auth_service.dart';
import '../../domain/entities/user.dart';
import '../../domain/usecases/login.dart';
import '../../domain/usecases/register.dart';
import '../../domain/usecases/logout.dart';
import '../../domain/usecases/get_user.dart';
import '../../domain/usecases/check_token.dart';
import '../../domain/usecases/mobile_check.dart';
import '../../domain/usecases/regenerate_code.dart';
import '../../domain/usecases/update_user.dart';
import '../../domain/usecases/change_password.dart';
import '../../domain/usecases/change_mobile_number.dart';
import '../../domain/usecases/send_reset_code.dart';
import '../../domain/usecases/verify_reset_code.dart';
import '../../domain/usecases/reset_password.dart';
import '../../domain/usecases/social_auth.dart';
import '../../data/services/social_auth_service.dart';
import '../../data/models/social_auth_models.dart';

part 'auth_state.dart';

class AuthCubit extends Cubit<AuthState> {
  final LocalAuthService _localAuthService;
  final Login _login;
  final Register _register;
  final Logout _logout;
  final GetUser _getUser;
  final CheckToken _checkToken;
  final MobileCheck _mobileCheck;
  final RegenerateCode _regenerateCode;
  final UpdateUser _updateUser;
  final ChangePassword _changePassword;
  final ChangeMobileNumber _changeMobileNumber;
  final SendResetCode _sendResetCode;
  final VerifyResetCode _verifyResetCode;
  final ResetPassword _resetPassword;
  final SocialAuth _socialAuth;
  final SocialAuthService _socialAuthService;

  AuthCubit({
    required LocalAuthService localAuthService,
    required Login login,
    required Register register,
    required Logout logout,
    required GetUser getUser,
    required CheckToken checkToken,
    required MobileCheck mobileCheck,
    required RegenerateCode regenerateCode,
    required UpdateUser updateUser,
    required ChangePassword changePassword,
    required ChangeMobileNumber changeMobileNumber,
    required SendResetCode sendResetCode,
    required VerifyResetCode verifyResetCode,
    required ResetPassword resetPassword,
    required SocialAuth socialAuth,
    required SocialAuthService socialAuthService,
  }) : _localAuthService = localAuthService,
       _login = login,
       _register = register,
       _logout = logout,
       _getUser = getUser,
       _checkToken = checkToken,
       _mobileCheck = mobileCheck,
       _regenerateCode = regenerateCode,
       _updateUser = updateUser,
       _changePassword = changePassword,
       _changeMobileNumber = changeMobileNumber,
       _sendResetCode = sendResetCode,
       _verifyResetCode = verifyResetCode,
       _resetPassword = resetPassword,
       _socialAuth = socialAuth,
       _socialAuthService = socialAuthService,
       super(AuthInitial());

  // Biometric Authentication Methods
  Future<void> authenticateWithBiometrics() async {
    emit(AuthLoading());

    final result = await _localAuthService.authenticateWithBiometrics();

    result.fold((error) => emit(AuthError(error)), (success) {
      if (success) {
        emit(AuthBiometricSuccess());
      } else {
        emit(AuthError('Authentication was cancelled'));
      }
    });
  }

  Future<void> checkBiometricAvailability() async {
    final result = await _localAuthService.getAvailableBiometrics();

    result.fold((error) => emit(AuthBiometricUnavailable()), (biometrics) {
      if (biometrics.isNotEmpty) {
        emit(AuthBiometricAvailable(biometrics));
      } else {
        emit(AuthBiometricUnavailable());
      }
    });
  }

  // Authentication Methods
  Future<void> login({
    required String mobile,
    required String password,
    String? fcmToken,
  }) async {
    log(mobile);
    emit(AuthLoading());

    final result = await _login(
      LoginParams(mobile: mobile, password: password, fcmToken: fcmToken),
    );

    result.fold((failure) => emit(AuthError(failure.message)), (user) async {
      emit(AuthLoginSuccess(user));
      // Small delay to ensure all data is saved before navigation
      await Future.delayed(const Duration(milliseconds: 100));
      emit(AuthNavigateToHome());
    });
  }

  Future<void> register({
    required String name,
    required String email,
    required String mobile,
    required String password,
    required String passwordConfirmation,
    required int userType,
    File? file,
    String? fcmToken,
    double? dayPrice,
    double? hourPrice,
    String? info,
    String? experience,
    List<String>? interests,
    List<String>? toursType,
    List<int>? languages,
    String? branchName,
    String? branchEmail,
    List<String>? branchPayments,
  }) async {
    emit(AuthLoading());

    final result = await _register(
      RegisterParams(
        name: name,
        email: email,
        mobile: mobile,
        password: password,
        passwordConfirmation: passwordConfirmation,
        userType: userType,
        file: file,
        fcmToken: fcmToken,
        dayPrice: dayPrice,
        hourPrice: hourPrice,
        info: info,
        experience: experience,
        interests: interests,
        toursType: toursType,
        languages: languages,
        branchName: branchName,
        branchEmail: branchEmail,
        branchPayments: branchPayments,
      ),
    );

    result.fold(
      (failure) => emit(AuthError(failure.message)),
      (_) => emit(AuthRegisterSuccess()),
    );
  }

  Future<void> logout() async {
    emit(AuthLoading());

    final result = await _logout();

    result.fold(
      (failure) => emit(AuthError(failure.message)),
      (_) => emit(AuthLogoutSuccess()),
    );
  }

  Future<void> getUser() async {
    emit(AuthLoading());

    final result = await _getUser();

    result.fold(
      (failure) => emit(AuthError(failure.message)),
      (user) => emit(AuthUserLoaded(user)),
    );
  }

  Future<void> checkToken() async {
    emit(AuthLoading());

    final result = await _checkToken();

    result.fold(
      (failure) => emit(AuthError(failure.message)),
      (user) => emit(AuthTokenValid(user)),
    );
  }

  Future<void> mobileCheck({
    required String code,
    required String mobile,
  }) async {
    emit(AuthLoading());

    final result = await _mobileCheck(
      MobileCheckParams(code: code, mobile: mobile),
    );

    result.fold(
      (failure) => emit(AuthError(failure.message)),
      (user) => emit(AuthMobileVerified(user)),
    );
  }

  Future<void> regenerateCode({required String mobile}) async {
    emit(AuthLoading());

    final result = await _regenerateCode(RegenerateCodeParams(mobile: mobile));

    result.fold(
      (failure) => emit(AuthError(failure.message)),
      (message) => emit(AuthCodeSent(message)),
    );
  }

  Future<void> updateUser({
    String? name,
    String? email,
    File? file,
    double? dayPrice,
    double? hourPrice,
    String? info,
    String? experience,
    List<String>? interests,
    List<String>? toursType,
    List<int>? languages,
  }) async {
    emit(AuthLoading());

    final result = await _updateUser(
      UpdateUserParams(
        name: name,
        email: email,
        file: file,
        dayPrice: dayPrice,
        hourPrice: hourPrice,
        info: info,
        experience: experience,
        interests: interests,
        toursType: toursType,
        languages: languages,
      ),
    );

    result.fold(
      (failure) => emit(AuthError(failure.message)),
      (user) => emit(AuthUserUpdated(user)),
    );
  }

  Future<void> changePassword({
    required String newPassword,
    required String newPasswordConfirmation,
    required String oldPassword,
  }) async {
    emit(AuthLoading());

    final result = await _changePassword(
      ChangePasswordParams(
        newPassword: newPassword,
        newPasswordConfirmation: newPasswordConfirmation,
        oldPassword: oldPassword,
      ),
    );

    result.fold(
      (failure) => emit(AuthError(failure.message)),
      (message) => emit(AuthPasswordChanged(message)),
    );
  }

  Future<void> changeMobileNumber({required String mobile}) async {
    emit(AuthLoading());

    final result = await _changeMobileNumber(
      ChangeMobileNumberParams(mobile: mobile),
    );

    result.fold(
      (failure) => emit(AuthError(failure.message)),
      (message) => emit(AuthMobileChanged(message)),
    );
  }

  Future<void> sendResetCode({required String mobile}) async {
    emit(AuthLoading());

    final result = await _sendResetCode(SendResetCodeParams(mobile: mobile));

    result.fold(
      (failure) => emit(AuthError(failure.message)),
      (message) => emit(AuthResetCodeSent(message)),
    );
  }

  Future<void> verifyResetCode({
    required String mobile,
    required String code,
  }) async {
    emit(AuthLoading());

    final result = await _verifyResetCode(
      VerifyResetCodeParams(mobile: mobile, code: code),
    );

    result.fold(
      (failure) => emit(AuthError(failure.message)),
      (message) => emit(AuthResetCodeVerified(message)),
    );
  }

  Future<void> resetPassword({
    required String mobile,
    required String code,
    required String password,
    required String passwordConfirmation,
  }) async {
    emit(AuthLoading());

    final result = await _resetPassword(
      ResetPasswordParams(
        mobile: mobile,
        code: code,
        password: password,
        passwordConfirmation: passwordConfirmation,
      ),
    );

    result.fold(
      (failure) => emit(AuthError(failure.message)),
      (message) => emit(AuthPasswordReset(message)),
    );
  }

  // Social Authentication Methods
  Future<void> signInWithGoogle({String? fcmToken, int userType = 1}) async {
    emit(AuthLoading());

    final socialResult = await _socialAuthService.signInWithGoogle();

    await socialResult.fold((error) async => emit(AuthError(error)), (
      userInfo,
    ) async {
      final accessToken = await userInfo.getAccessToken();
      final idToken = await userInfo.getIdToken();

      if (accessToken != null) {
        final result = await _socialAuth(
          SocialAuthParams(
            provider: SocialProvider.google,
            accessToken: accessToken,
            idToken: idToken,
            email: userInfo.email,
            name: userInfo.displayName,
            profilePicture: userInfo.profilePicture,
            fcmToken: fcmToken,
            userType: userType,
          ),
        );

        result.fold(
          (failure) => emit(AuthError(failure.message)),
          (user) => emit(AuthSocialLoginSuccess(user, SocialProvider.google)),
        );
      } else {
        emit(AuthError('Failed to get Google access token'));
      }
    });
  }

  Future<void> signInWithFacebook({String? fcmToken, int userType = 1}) async {
    emit(AuthLoading());

    final socialResult = await _socialAuthService.signInWithFacebook();

    await socialResult.fold((error) async => emit(AuthError(error)), (
      userInfo,
    ) async {
      final accessToken = await userInfo.getAccessToken();

      if (accessToken != null) {
        final result = await _socialAuth(
          SocialAuthParams(
            provider: SocialProvider.facebook,
            accessToken: accessToken,
            email: userInfo.email,
            name: userInfo.displayName,
            profilePicture: userInfo.profilePicture,
            fcmToken: fcmToken,
            userType: userType,
          ),
        );

        result.fold(
          (failure) => emit(AuthError(failure.message)),
          (user) => emit(AuthSocialLoginSuccess(user, SocialProvider.facebook)),
        );
      } else {
        emit(AuthError('Failed to get Facebook access token'));
      }
    });
  }

  Future<void> signInWithApple({String? fcmToken, int userType = 1}) async {
    emit(AuthLoading());

    final socialResult = await _socialAuthService.signInWithApple();

    await socialResult.fold((error) async => emit(AuthError(error)), (
      userInfo,
    ) async {
      // For Apple, we use the user identifier as access token
      final result = await _socialAuth(
        SocialAuthParams(
          provider: SocialProvider.apple,
          accessToken: userInfo.id, // Apple user identifier
          email: userInfo.email,
          name: userInfo.displayName,
          fcmToken: fcmToken,
          userType: userType,
        ),
      );

      result.fold(
        (failure) => emit(AuthError(failure.message)),
        (user) => emit(AuthSocialLoginSuccess(user, SocialProvider.apple)),
      );
    });
  }

  Future<void> socialSignOut(SocialProvider provider) async {
    await _socialAuthService.signOut(provider);
  }

  Future<void> signOutFromAllSocialProviders() async {
    await _socialAuthService.signOutAll();
  }
}
