// features/home/<USER>/widgets/bottom_navigation_widget.dart
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:go_router/go_router.dart';
import 'package:tripooo_user/constants/app_images.dart';
import 'package:tripooo_user/core/theme/theme.dart';
import 'package:tripooo_user/core/routing/app_router.dart';
import 'package:tripooo_user/core/utils/responsive_extensions.dart';

class BottomNavigationWidget extends StatelessWidget {
  const BottomNavigationWidget({super.key});

  @override
  Widget build(BuildContext context) {
    final currentLocation = GoRouter.of(
      context,
    ).routerDelegate.currentConfiguration.uri.toString();

    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 10,
            offset: Offset(0, -2),
          ),
        ],
      ),
      child: <PERSON><PERSON><PERSON>(
        child: Padding(
          padding: EdgeInsets.symmetric(horizontal: 20, vertical: 8),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            children: [
              _buildBottomNavItem(
                context,
                Assets.assetsIconsHome,
                'الرئيسية',
                currentLocation == AppRoutes.home,
                () => context.go(AppRoutes.home),
              ),
              _buildBottomNavItem(
                context,
                Assets.assetsIconsMessages,
                'مجموعات',
                false,
                () {
                  // TODO: Navigate to search
                },
              ),
              _buildBottomNavItem(
                context,
                Assets.assetsIconsCalendar,
                'نقاط الولاء',
                false,
                () {
                  // TODO: Navigate to chat
                },
              ),
              _buildBottomNavItem(
                context,
                Assets.assetsIconsMenuMoreHorizontal,
                'المزيد',
                currentLocation == AppRoutes.more,
                () => context.go(AppRoutes.more),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildBottomNavItem(
    BuildContext context,
    String icon,
    String label,
    bool isSelected,
    VoidCallback onTap,
  ) {
    return GestureDetector(
      onTap: onTap,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            padding: EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: isSelected
                  ? AppColors.primary.withValues(alpha: 0.1)
                  : Colors.transparent,
              borderRadius: BorderRadius.circular(12),
            ),
            child: SvgPicture.asset(
              icon,
              height: 20.h(context),
              width: 20.w(context),
              color: isSelected
                  ? AppColors.primary.withValues(alpha: 3)
                  : Colors.black,
            ),
          ),
          SizedBox(height: 4),
          Text(
            label,
            style: TextStyle(
              color: isSelected ? AppColors.primary : Colors.grey[600],
              fontSize: 12,
              fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
            ),
          ),
        ],
      ),
    );
  }
}
