// features/Auth/presentation/pages/mobile_verification_screen.dart
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:tripooo_user/core/routing/app_router.dart';
import 'package:go_router/go_router.dart';
import 'package:tripooo_user/core/widgets/otp_input_widget.dart';
import 'dart:async';
import '../cubit/auth_cubit.dart';

class MobileVerificationScreen extends StatefulWidget {
  final String mobile;

  const MobileVerificationScreen({super.key, required this.mobile});

  @override
  State<MobileVerificationScreen> createState() =>
      _MobileVerificationScreenState();
}

class _MobileVerificationScreenState extends State<MobileVerificationScreen> {
  final int _codeLength = 4;
  Timer? _timer;
  int _seconds = 120;
  bool _canResend = false;
  String _currentCode = '';

  @override
  void initState() {
    super.initState();
    _startTimer();
  }

  @override
  void dispose() {
    _timer?.cancel();
    super.dispose();
  }

  void _startTimer() {
    setState(() {
      _seconds = 120;
      _canResend = false;
    });
    _timer?.cancel();
    _timer = Timer.periodic(Duration(seconds: 1), (timer) {
      if (_seconds == 0) {
        setState(() {
          _canResend = true;
        });
        timer.cancel();
      } else {
        setState(() {
          _seconds--;
        });
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        elevation: 0,
        leading: IconButton(
          icon: Icon(Icons.arrow_back, color: Colors.black),
          onPressed: () {
            if (context.canPop()) {
              context.pop();
            } else {
              context.goToRegister();
            }
          },
        ),
      ),
      body: BlocListener<AuthCubit, AuthState>(
        listener: (context, state) async {
          if (state is AuthMobileVerified) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text('تم التحقق بنجاح'),
                backgroundColor: Colors.green,
              ),
            );
            // Add small delay to ensure data is saved
            await Future.delayed(const Duration(milliseconds: 200));
            if (context.mounted) {
              // Force navigation to home
              context.go(AppRoutes.home);
            }
          } else if (state is AuthCodeSent) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(state.message),
                backgroundColor: Colors.green,
              ),
            );
          } else if (state is AuthError) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(state.message),
                backgroundColor: Colors.red,
              ),
            );
          }
        },
        child: SafeArea(
          child: Padding(
            padding: const EdgeInsets.all(24.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                SizedBox(height: 16),
                Text(
                  'رمز التحقق',
                  style: TextStyle(
                    fontSize: 22,
                    fontWeight: FontWeight.bold,
                    color: Colors.black,
                  ),
                ),
                SizedBox(height: 16),
                Text(
                  'تحقق من بريدك الإلكتروني',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.w600,
                    color: Colors.black,
                  ),
                ),
                SizedBox(height: 8),
                Text(
                  'لقد أرسلنا رمز تحقق مكون من 4 أرقام إلى بريدك الإلكتروني المسجل. يرجى إدخال الرمز لمتابعة العملية.',
                  textAlign: TextAlign.center,
                  style: TextStyle(fontSize: 14, color: Colors.grey[700]),
                ),
                SizedBox(height: 32),
                OtpInputWidget(
                  length: _codeLength,
                  onCompleted: (code) {
                    setState(() {
                      _currentCode = code;
                    });
                  },
                  onChanged: (code) {
                    setState(() {
                      _currentCode = code;
                    });
                  },
                ),
                SizedBox(height: 24),
                _canResend
                    ? Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Text('لم تستلم الرمز؟'),
                          TextButton(
                            onPressed: () {
                              context.read<AuthCubit>().regenerateCode(
                                mobile: widget.mobile,
                              );
                              _startTimer();
                            },
                            child: Text(
                              'أعد الإرسال',
                              style: TextStyle(color: Colors.blue),
                            ),
                          ),
                        ],
                      )
                    : Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Text('لم تستلم الرمز؟'),
                          Text(
                            ' أعد الإرسال خلال ',
                            style: TextStyle(color: Colors.grey[700]),
                          ),
                          Text(
                            '$_seconds',
                            style: TextStyle(color: Colors.blue),
                          ),
                          Text(
                            ' ثانية',
                            style: TextStyle(color: Colors.grey[700]),
                          ),
                        ],
                      ),
                SizedBox(height: 32),
                SizedBox(
                  width: double.infinity,
                  child: BlocBuilder<AuthCubit, AuthState>(
                    builder: (context, state) {
                      return ElevatedButton(
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.blue,
                          padding: EdgeInsets.symmetric(vertical: 16),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                        ),
                        onPressed:
                            state is AuthLoading ||
                                _currentCode.length != _codeLength
                            ? null
                            : () {
                                context.read<AuthCubit>().mobileCheck(
                                  code: _currentCode,
                                  mobile: widget.mobile,
                                );
                              },
                        child: state is AuthLoading
                            ? CircularProgressIndicator(color: Colors.white)
                            : Text(
                                'تأكيد',
                                style: TextStyle(
                                  fontSize: 18,
                                  color: Colors.white,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                      );
                    },
                  ),
                ),
                Spacer(),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
