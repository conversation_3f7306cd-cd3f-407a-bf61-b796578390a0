// features/Auth/data/datasources/auth_remote_data_source.dart
import '../models/user_model.dart';
import '../models/auth_request_models.dart';
import '../models/auth_response_models.dart';
import '../models/social_auth_models.dart';

abstract class AuthRemoteDataSource {
  /// Get current user profile
  /// Requires authentication token
  Future<UserModel> getUser(String token);

  /// Check if the provided token is valid
  /// Returns user data if token is valid
  Future<UserModel> checkToken(String token);

  /// Register a new user (client or guide)
  /// Returns null on successful registration
  Future<void> register(RegisterRequest request);

  /// Verify mobile number with code (OTP)
  /// Returns user data with token on successful verification
  Future<AuthResponse> mobileCheck(MobileCheckRequest request);

  /// Request a new verification code for mobile number
  /// Returns success message
  Future<SimpleResponse> regenerateCode(RegenerateCodeRequest request);

  /// Login with mobile and password
  /// Returns user data with token on successful login
  Future<AuthResponse> login(LoginRequest request);

  /// Update user profile information
  /// Requires authentication token
  Future<UserModel> updateUser(UpdateUserRequest request, String token);

  /// Change user password
  /// Requires authentication token
  Future<SimpleResponse> changePassword(
    ChangePasswordRequest request,
    String token,
  );

  /// Change user mobile number
  /// Requires authentication token
  Future<SimpleResponse> changeMobileNumber(
    ChangeMobileRequest request,
    String token,
  );

  /// Logout user and invalidate token
  /// Requires authentication token
  Future<LogoutResponse> logout(String token);

  /// Send password reset code to mobile
  /// Returns success message
  Future<SimpleResponse> sendResetCode(SendResetCodeRequest request);

  /// Verify password reset code
  /// Returns success message if code is valid
  Future<SimpleResponse> verifyResetCode(VerifyResetCodeRequest request);

  /// Reset password using verification code
  /// Returns success message on successful reset
  Future<SimpleResponse> resetPassword(ResetPasswordRequest request);

  /// Social authentication with Google, Facebook, or Apple
  /// Returns user data with token on successful authentication
  Future<AuthResponse> socialAuth(SocialAuthRequest request);
}
