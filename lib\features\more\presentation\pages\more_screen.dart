// features/more/presentation/pages/more_screen.dart
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:easy_localization/easy_localization.dart' hide TextDirection;
import 'package:tripooo_user/constants/app_images.dart';
import 'package:tripooo_user/core/routing/app_router.dart';
import 'package:tripooo_user/core/theme/theme.dart';
import 'package:tripooo_user/features/more/presentation/widgets/more_footer.dart';
import 'package:tripooo_user/features/more/presentation/widgets/more_header.dart';
import 'package:tripooo_user/features/more/presentation/widgets/more_menu_items.dart';
import 'package:tripooo_user/features/home/<USER>/widgets/bottom_navigation_widget.dart';
import '../../../Auth/presentation/cubit/auth_cubit.dart';

class MoreScreen extends StatefulWidget {
  const MoreScreen({super.key});

  @override
  State<MoreScreen> createState() => _MoreScreenState();
}

class _MoreScreenState extends State<MoreScreen> {
  @override
  void initState() {
    super.initState();
    context.read<AuthCubit>().getUser();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      body: BlocBuilder<AuthCubit, AuthState>(
        builder: (context, state) {
          return SingleChildScrollView(
            child: Column(
              children: [
                MoreHeader(state: state),
                MoreMenuItems(state: state),
                const MoreFooter(),
              ],
            ),
          );
        },
      ),
      bottomNavigationBar: const BottomNavigationWidget(),
    );
  }
}
