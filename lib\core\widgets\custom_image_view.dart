// core/widgets/custom_image_view.dart

import 'dart:io';

import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import '../../constants/app_images.dart';

extension ImageTypeExtension on String {
  ImageType get getimageType {
    if (startsWith('http') || startsWith('https')) {
      return ImageType.network;
    } else if (endsWith('.svg')) {
      return ImageType.svg;
    } else if (startsWith('file://')) {
      return ImageType.file;
    } else if (startsWith('/')) {
      return ImageType.memory;
    } else {
      return ImageType.png;
    }
  }
}

enum ImageType { svg, png, network, file, unknown, memory }

class CustomImageView extends StatelessWidget {
  const CustomImageView({
    super.key,
    this.url,
    this.file,
    this.height,
    this.width,
    this.fit,
    this.angle,
    this.color,
    this.alignment,
    this.placeholder = Assets.assetsImagesLogo,
    this.radius,
    this.margin,
    this.border,
  });

  final String? url;
  final File? file;
  final double? height;
  final double? width;
  final AlignmentGeometry? alignment;
  final BoxFit? fit;
  final double? angle;
  final Color? color;
  final BorderRadius? radius;
  final String? placeholder;
  final EdgeInsetsGeometry? margin;
  final BoxBorder? border;

  @override
  Widget build(BuildContext context) {
    return alignment != null
        ? Align(
            alignment: alignment ?? Alignment.center,
            child: _buildImage(context),
          )
        : _buildImage(context);
  }

  Widget _buildImage(BuildContext context) {
    return Padding(
      padding: margin ?? EdgeInsets.zero,
      child: _buildCircleImage(),
    );
  }

  Widget _buildCircleImage() {
    return (radius != null)
        ? ClipRRect(
            borderRadius: radius ?? BorderRadius.zero,
            child: SizedBox(
              height: height,
              width: width,
              child: _buildImageWidgetWithBorder(),
            ),
          )
        : _buildImageWidgetWithBorder();
  }

  Widget _buildImageWidgetWithBorder() {
    return (border != null)
        ? Container(
            decoration: BoxDecoration(borderRadius: radius, border: border),
            child: _buildImageWidget(),
          )
        : _buildImageWidget();
  }

  Widget _buildImageWidget() {
    if (file != null) {
      return Image.file(
        file!,
        height: height,
        width: width,
        color: color,
        fit: fit ?? BoxFit.cover,
      );
    }

    switch (url?.getimageType ?? ImageType.unknown) {
      case ImageType.svg:
        return SizedBox(
          height: height,
          width: width,
          child: SvgPicture.asset(
            url!,
            height: height,
            width: width,
            fit: fit ?? BoxFit.contain,
          ),
        );
      case ImageType.file:
        return Image.file(
          File(url!),
          height: height,
          width: width,
          color: color,
          fit: fit ?? BoxFit.cover,
        );
      case ImageType.network:
        return CachedNetworkImage(
          imageUrl: url!,
          height: height,
          width: width,
          color: color,
          fit: fit,
          placeholder: (context, url) => SizedBox(
            height: 30,
            width: 30,
            child: LinearProgressIndicator(
              color: Colors.grey.shade300,
              backgroundColor: Colors.grey.shade100,
            ),
          ),
          errorWidget: (context, url, error) {
            return Image.asset(
              placeholder!,
              height: height,
              width: width,
              fit: fit ?? BoxFit.cover,
            );
          },
        );

      case ImageType.png:
      default:
        return Image.asset(
          url!,
          color: color,
          height: height,
          width: width,
          fit: fit ?? BoxFit.fill,
        );
    }
  }
}
