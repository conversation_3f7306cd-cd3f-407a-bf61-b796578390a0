// features/more/presentation/widgets/more_header.dart
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:tripooo_user/core/network/api_endpoints.dart';
import 'package:tripooo_user/core/routing/app_router.dart';
import 'package:tripooo_user/core/theme/theme.dart';
import 'package:tripooo_user/core/utils/responsive_extensions.dart';
import 'package:tripooo_user/features/more/more_constants.dart';
import 'package:tripooo_user/features/onboarding/utils/responsive_helper.dart';

import '../../../Auth/presentation/cubit/auth_cubit.dart';

class MoreHeader extends StatelessWidget {
  final AuthState state;

  const MoreHeader({super.key, required this.state});

  @override
  Widget build(BuildContext context) {
    final isTablet = ResponsiveHelper.isTablet(context);

    return Container(
      width: double.infinity,
      color: AppColors.background,
      child: SafeArea(
        child: Padding(
          padding: EdgeInsets.all(
            isTablet
                ? MoreConstants.paddingTablet
                : MoreConstants.paddingMobile,
          ),
          child: _buildContent(context, isTablet),
        ),
      ),
    );
  }

  Widget _buildContent(BuildContext context, bool isTablet) {
    if (state is AuthLoading) {
      return const Center(child: CircularProgressIndicator());
    } else if (state is AuthError) {
      return _buildErrorContent(
        context,
        isTablet,
        (state as AuthError).message,
      );
    } else {
      return Column(
        children: [
          _buildProfileAvatar(context, isTablet),
          SizedBox(
            height: isTablet
                ? MoreConstants.spacingLarge
                : MoreConstants.spacingMedium,
          ),
          _buildUserName(context, isTablet),
          SizedBox(
            height: isTablet
                ? MoreConstants.spacingMedium
                : MoreConstants.spacingSmall,
          ),
          _buildUserEmail(context, isTablet),
          SizedBox(
            height: isTablet
                ? MoreConstants.spacingExtraLarge
                : MoreConstants.spacingLarge,
          ),
          _buildActionButtons(context, isTablet),
        ],
      );
    }
  }

  Widget _buildErrorContent(
    BuildContext context,
    bool isTablet,
    String errorMessage,
  ) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Icon(
          Icons.error_outline,
          size: isTablet
              ? MoreConstants.iconSizeTablet
              : MoreConstants.iconSizeMobile,
          color: AppColors.error,
        ),
        SizedBox(height: MoreConstants.spacingMedium),
        Text(
          errorMessage.tr(),
          style: AppTextStyles.subtitle.copyWith(
            fontSize: isTablet
                ? MoreConstants.fontSizeSubtitleTablet
                : MoreConstants.fontSizeSubtitleMobile,
            color: AppColors.error,
          ),
          textAlign: TextAlign.center,
        ),
        SizedBox(height: MoreConstants.spacingLarge),
        _buildRetryButton(context, isTablet),
      ],
    );
  }

  Widget _buildRetryButton(BuildContext context, bool isTablet) {
    return Container(
      height: isTablet
          ? MoreConstants.buttonHeightTablet
          : MoreConstants.buttonHeightMobile,
      decoration: BoxDecoration(
        color: AppColors.primary,
        borderRadius: BorderRadius.circular(MoreConstants.borderRadius),
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(MoreConstants.borderRadius),
          onTap: () => context.read<AuthCubit>().getUser(),
          child: Center(
            child: Text(
              'retry'.tr(),
              style: AppTextStyles.bodyMedium.copyWith(
                color: Colors.white,
                fontWeight: FontWeight.w600,
                fontSize: isTablet
                    ? MoreConstants.fontSizeButtonTablet
                    : MoreConstants.fontSizeButtonMobile,
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildProfileAvatar(BuildContext context, bool isTablet) {
    return CircleAvatar(
      radius: isTablet
          ? MoreConstants.avatarRadiusTablet
          : MoreConstants.avatarRadiusMobile,
      backgroundColor: AppColors.textTertiary.withValues(alpha: 0.2),
      child: _getAvatarContent(isTablet, context),
    );
  }

  Widget _getAvatarContent(bool isTablet, BuildContext context) {
    final userImg = _getUserImage();

    if (userImg != null && userImg.isNotEmpty) {
      return ClipOval(
        child: Image.network(
          "${ApiEndpoints.baseUrl}/$userImg",
          width: 80.h(context),
          height: 80.h(context),
          fit: BoxFit.cover,
          loadingBuilder: (context, child, loadingProgress) {
            if (loadingProgress == null) return child;
            return CircularProgressIndicator(
              value: loadingProgress.expectedTotalBytes != null
                  ? loadingProgress.cumulativeBytesLoaded /
                        (loadingProgress.expectedTotalBytes ?? 1)
                  : null,
            );
          },
          errorBuilder: (context, error, stackTrace) => _defaultIcon(isTablet),
        ),
      );
    }
    return _defaultIcon(isTablet);
  }

  Widget _defaultIcon(bool isTablet) {
    return Icon(
      Icons.person,
      size: isTablet
          ? MoreConstants.iconSizeTablet
          : MoreConstants.iconSizeMobile,
      color: AppColors.textSecondary,
    );
  }

  Widget _buildUserName(BuildContext context, bool isTablet) {
    final name = _getUserName();
    return Text(
      name,
      style: AppTextStyles.heading.copyWith(
        fontSize: isTablet
            ? MoreConstants.fontSizeHeadingTablet
            : MoreConstants.fontSizeHeadingMobile,
        color: AppColors.textPrimary,
        fontWeight: FontWeight.w600,
      ),
      textAlign: TextAlign.center,
    );
  }

  Widget _buildUserEmail(BuildContext context, bool isTablet) {
    final email = _getUserEmail();
    return Text(
      email,
      style: AppTextStyles.subtitle.copyWith(
        fontSize: isTablet
            ? MoreConstants.fontSizeSubtitleTablet
            : MoreConstants.fontSizeSubtitleMobile,
        color: AppColors.textSecondary,
      ),
      textAlign: TextAlign.center,
    );
  }

  Widget _buildActionButtons(BuildContext context, bool isTablet) {
    return Row(
      children: [
        Expanded(child: _buildEditButton(context, isTablet)),
        SizedBox(width: MoreConstants.spacingMedium),
        Expanded(child: _buildWalletButton(context, isTablet)),
      ],
    );
  }

  Widget _buildEditButton(BuildContext context, bool isTablet) {
    final isAuthenticated = _isAuthenticated();

    return Container(
      height: isTablet
          ? MoreConstants.buttonHeightTablet
          : MoreConstants.buttonHeightMobile,
      decoration: BoxDecoration(
        color: AppColors.primary,
        borderRadius: BorderRadius.circular(MoreConstants.borderRadius),
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(MoreConstants.borderRadius),
          onTap: isAuthenticated
              ? () => context.go(AppRoutes.profile)
              : () => context.go(AppRoutes.login),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.add,
                color: Colors.white,
                size: isTablet
                    ? MoreConstants.iconSizeSmallTablet
                    : MoreConstants.iconSizeSmallMobile,
              ),
              SizedBox(width: MoreConstants.spacingSmall),
              Text(
                isAuthenticated ? 'تعديل' : 'تسجيل دخول',
                style: AppTextStyles.bodyMedium.copyWith(
                  color: Colors.white,
                  fontWeight: FontWeight.w600,
                  fontSize: isTablet
                      ? MoreConstants.fontSizeButtonTablet
                      : MoreConstants.fontSizeButtonMobile,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildWalletButton(BuildContext context, bool isTablet) {
    return Container(
      height: isTablet
          ? MoreConstants.buttonHeightTablet
          : MoreConstants.buttonHeightMobile,
      decoration: BoxDecoration(
        border: Border.all(color: AppColors.textTertiary),
        borderRadius: BorderRadius.circular(MoreConstants.borderRadius),
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(MoreConstants.borderRadius),
          onTap: () {
            // TODO: Navigate to wallet
          },
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text(
                'المحفظة',
                style: AppTextStyles.bodyMedium.copyWith(
                  color: AppColors.textPrimary,
                  fontWeight: FontWeight.w600,
                  fontSize: isTablet
                      ? MoreConstants.fontSizeButtonTablet
                      : MoreConstants.fontSizeButtonMobile,
                ),
              ),
              SizedBox(width: MoreConstants.spacingSmall),
              Icon(
                Icons.account_balance_wallet_outlined,
                color: AppColors.textPrimary,
                size: isTablet
                    ? MoreConstants.iconSizeSmallTablet
                    : MoreConstants.iconSizeSmallMobile,
              ),
            ],
          ),
        ),
      ),
    );
  }

  // Helper methods to extract user data safely
  String? _getUserImage() {
    if (state is AuthUserLoaded) {
      return (state as AuthUserLoaded).user.img;
    } else if (state is AuthLoginSuccess) {
      return (state as AuthLoginSuccess).user.img;
    } else if (state is AuthSocialLoginSuccess) {
      return (state as AuthSocialLoginSuccess).user.img;
    } else if (state is AuthTokenValid) {
      return (state as AuthTokenValid).user.img;
    } else if (state is AuthMobileVerified) {
      return (state as AuthMobileVerified).user.img;
    } else if (state is AuthUserUpdated) {
      return (state as AuthUserUpdated).user.img;
    }
    return null;
  }

  String _getUserName() {
    if (state is AuthUserLoaded) {
      return (state as AuthUserLoaded).user.name;
    } else if (state is AuthLoginSuccess) {
      return (state as AuthLoginSuccess).user.name;
    } else if (state is AuthSocialLoginSuccess) {
      return (state as AuthSocialLoginSuccess).user.name;
    } else if (state is AuthTokenValid) {
      return (state as AuthTokenValid).user.name;
    } else if (state is AuthMobileVerified) {
      return (state as AuthMobileVerified).user.name;
    } else if (state is AuthUserUpdated) {
      return (state as AuthUserUpdated).user.name;
    }
    return 'guest_user'.tr();
  }

  String _getUserEmail() {
    if (state is AuthUserLoaded) {
      return (state as AuthUserLoaded).user.email;
    } else if (state is AuthLoginSuccess) {
      return (state as AuthLoginSuccess).user.email;
    } else if (state is AuthSocialLoginSuccess) {
      return (state as AuthSocialLoginSuccess).user.email;
    } else if (state is AuthTokenValid) {
      return (state as AuthTokenValid).user.email;
    } else if (state is AuthMobileVerified) {
      return (state as AuthMobileVerified).user.email;
    } else if (state is AuthUserUpdated) {
      return (state as AuthUserUpdated).user.email;
    }
    return 'login_to_access_features'.tr();
  }

  bool _isAuthenticated() {
    return state is AuthUserLoaded ||
        state is AuthLoginSuccess ||
        state is AuthSocialLoginSuccess ||
        state is AuthTokenValid ||
        state is AuthMobileVerified ||
        state is AuthUserUpdated;
  }
}
