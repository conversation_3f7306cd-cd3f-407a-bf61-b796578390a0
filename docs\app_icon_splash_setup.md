# 🎨 App Icon & Splash Screen Setup Guide

Complete guide for setting up app icons and splash screens for the Tripooo User app.

## 📋 Prerequisites

### Required Packages (Already Added)
```yaml
dev_dependencies:
  flutter_launcher_icons: ^0.13.1
  flutter_native_splash: ^2.4.0
```

### Required Assets
Place these files in `assets/icons/`:
- `app_icon.png` (1024x1024px)
- `splash_logo.png` (512x512px) 
- `splash_logo_dark.png` (512x512px)

## 🚀 Quick Setup (3 Steps)

### Step 1: Install Dependencies
```bash
flutter pub get
```

### Step 2: Generate App Icons
```bash
flutter pub run flutter_launcher_icons:main
```

### Step 3: Generate Splash Screen
```bash
flutter pub run flutter_native_splash:create
```

## 🎯 Detailed Configuration

### App Icon Configuration
```yaml
flutter_icons:
  android: "launcher_icon"
  ios: true
  image_path: "assets/icons/app_icon.png"
  min_sdk_android: 21
  web:
    generate: true
    image_path: "assets/icons/app_icon.png"
    background_color: "#hexcode"
    theme_color: "#hexcode"
  windows:
    generate: true
    image_path: "assets/icons/app_icon.png"
    icon_size: 48
```

### Splash Screen Configuration
```yaml
flutter_native_splash:
  color: "#FFFFFF"
  image: assets/icons/splash_logo.png
  color_dark: "#1A1A1A"
  image_dark: assets/icons/splash_logo_dark.png
  
  android_12:
    image: assets/icons/splash_logo.png
    icon_background_color: "#FFFFFF"
    image_dark: assets/icons/splash_logo_dark.png
    icon_background_color_dark: "#1A1A1A"
  
  web: false
  android: true
  ios: true
```

## 🎨 Creating Temporary Icons

### Option 1: Use Provided Python Script
```bash
cd scripts
python create_temp_icons.py
```

### Option 2: Use Flutter Icon Generator
```bash
flutter run lib/tools/icon_generator.dart
```

### Option 3: Manual Creation
Create simple placeholder images:
- Use any image editor (Photoshop, GIMP, Canva)
- Follow the size requirements in `assets/icons/README.md`

## 🔧 Advanced Configuration

### Custom Colors
Update the colors in `pubspec.yaml`:
```yaml
flutter_native_splash:
  color: "#2196F3"  # Your primary color
  color_dark: "#1976D2"  # Your primary dark color
```

### Platform-Specific Settings

#### Android
- Supports adaptive icons (Android 8+)
- Android 12+ uses new splash screen API
- Minimum SDK: 21

#### iOS
- Generates all required icon sizes automatically
- Supports both light and dark themes
- Compatible with iOS 12+

#### Web
- Generates favicon and PWA icons
- Can be disabled if not needed
- Supports manifest.json integration

## 📱 Testing

### Test App Icons
1. Build and install the app
2. Check app drawer/home screen
3. Verify icon appears correctly
4. Test on different devices/screen densities

### Test Splash Screen
1. Launch the app
2. Observe splash screen appearance
3. Test both light and dark themes
4. Check timing and transitions

## 🛠️ Troubleshooting

### Common Issues

#### "Image not found"
- Verify file paths in `pubspec.yaml`
- Ensure files exist in `assets/icons/`
- Check file names match exactly

#### "Generation failed"
- Run `flutter clean`
- Run `flutter pub get`
- Try generating again

#### "Icons not updating"
- Uninstall and reinstall the app
- Clear device cache
- Check if files were actually generated

### File Locations

#### Generated App Icons
- **Android**: `android/app/src/main/res/`
- **iOS**: `ios/Runner/Assets.xcassets/`
- **Web**: `web/icons/`

#### Generated Splash Files
- **Android**: `android/app/src/main/res/drawable/`
- **iOS**: `ios/Runner/Assets.xcassets/`

## 🎉 Verification Checklist

### App Icons ✅
- [ ] Android app drawer shows new icon
- [ ] iOS home screen shows new icon
- [ ] Web favicon updated
- [ ] All sizes generated correctly
- [ ] Icon looks good on different backgrounds

### Splash Screen ✅
- [ ] Splash appears on app launch
- [ ] Logo displays correctly
- [ ] Background color matches design
- [ ] Dark theme works properly
- [ ] Timing feels appropriate
- [ ] Transitions smoothly to main app

## 🔄 Updating Icons

### When You Have Final Designs
1. Replace files in `assets/icons/`
2. Re-run generation commands
3. Test on devices
4. Commit changes to version control

### Version Control
Add to `.gitignore` if needed:
```
# Generated icons (optional)
android/app/src/main/res/mipmap-*/
ios/Runner/Assets.xcassets/AppIcon.appiconset/
```

## 📚 Additional Resources

### Design Tools
- [App Icon Generator](https://appicon.co/)
- [Splash Screen Generator](https://apetools.webprofusion.com/app/#/tools/imagegorilla)
- [Icon8](https://icons8.com/icons)
- [Figma](https://figma.com)

### Documentation
- [Flutter Launcher Icons](https://pub.dev/packages/flutter_launcher_icons)
- [Flutter Native Splash](https://pub.dev/packages/flutter_native_splash)
- [Android Icon Guidelines](https://developer.android.com/guide/practices/ui_guidelines/icon_design_adaptive)
- [iOS Icon Guidelines](https://developer.apple.com/design/human-interface-guidelines/app-icons)

## 🎯 Next Steps

1. **Create/obtain your actual app icon and logo designs**
2. **Replace the temporary icons**
3. **Test thoroughly on multiple devices**
4. **Update app store listings with new icons**
5. **Consider creating seasonal or special event variants**

---

**💡 Pro Tip**: Keep your original design files (PSD, AI, Figma) in a separate `design/` folder for future updates!
