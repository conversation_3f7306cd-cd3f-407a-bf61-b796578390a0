// features/guides/data/models/language_model.dart
import '../../domain/entities/language_entity.dart';

class LanguageTranslationModel extends LanguageTranslation {
  const LanguageTranslationModel({
    required super.id,
    required super.languageId,
    required super.locale,
    required super.name,
  });

  factory LanguageTranslationModel.fromJson(Map<String, dynamic> json) {
    return LanguageTranslationModel(
      id: json['id'] ?? 0,
      languageId: json['language_id'] ?? 0,
      locale: json['locale'] ?? '',
      name: json['name'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'language_id': languageId,
      'locale': locale,
      'name': name,
    };
  }
}

class LanguagePivotModel extends LanguagePivot {
  const LanguagePivotModel({
    required super.userId,
    required super.languageId,
  });

  factory LanguagePivotModel.fromJson(Map<String, dynamic> json) {
    return LanguagePivotModel(
      userId: json['user_id'] ?? 0,
      languageId: json['language_id'] ?? 0,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'user_id': userId,
      'language_id': languageId,
    };
  }
}

class LanguageModel extends LanguageEntity {
  const LanguageModel({
    required super.id,
    required super.createdAt,
    required super.updatedAt,
    required super.name,
    super.deletedAt,
    required super.isActivate,
    super.pivot,
    required super.translations,
  });

  factory LanguageModel.fromJson(Map<String, dynamic> json) {
    return LanguageModel(
      id: json['id'] ?? 0,
      createdAt: json['created_at'] ?? '',
      updatedAt: json['updated_at'] ?? '',
      name: json['name'] ?? '',
      deletedAt: json['deleted_at'],
      isActivate: json['is_activate'] ?? 0,
      pivot: json['pivot'] != null 
          ? LanguagePivotModel.fromJson(json['pivot'])
          : null,
      translations: (json['translations'] as List<dynamic>?)
              ?.map((translation) => LanguageTranslationModel.fromJson(translation))
              .toList() ??
          [],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'created_at': createdAt,
      'updated_at': updatedAt,
      'name': name,
      'deleted_at': deletedAt,
      'is_activate': isActivate,
      'pivot': (pivot as LanguagePivotModel?)?.toJson(),
      'translations': translations
          .map((translation) => (translation as LanguageTranslationModel).toJson())
          .toList(),
    };
  }
}
