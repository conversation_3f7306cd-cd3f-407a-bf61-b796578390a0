// features/home/<USER>/widgets/destinations_section_widget.dart
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:tripooo_user/core/theme/theme.dart';
import 'package:tripooo_user/core/utils/responsive_extensions.dart';
import 'package:tripooo_user/core/utils/widget_extensions.dart';
import 'package:tripooo_user/features/onboarding/utils/responsive_helper.dart';
import 'package:tripooo_user/core/di/injection_container.dart' as di;
import '../../../destinations/presentation/cubit/destinations_cubit.dart';
import '../../../destinations/domain/entities/destination_entity.dart';
import '../../../destinations/presentation/pages/destination_details_page.dart';

class DestinationsSectionWidget extends StatelessWidget {
  const DestinationsSectionWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) =>
          di.sl<DestinationsCubit>()..loadDestinations(limit: 5),
      child:
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildSectionHeader(context),
              ResponsiveHelper.getSpacing(
                context,
                type: 'medium',
              ).verticalSpaceResponsive(context),
              _buildDestinationsList(context),
            ],
          ).paddingSymmetric(
            horizontal: ResponsiveHelper.isTablet(context) ? 32 : 20,
            context: context,
          ),
    );
  }

  Widget _buildSectionHeader(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          'وجهات مقترحة',
          style: AppTextStyles.heading.copyWith(
            fontSize: ResponsiveHelper.isTablet(context) ? 20 : 18,
            fontWeight: FontWeight.bold,
          ),
        ),
        TextButton(
          onPressed: () {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text('سيتم إضافة صفحة جميع الوجهات قريباً'),
                backgroundColor: AppColors.info,
              ),
            );
          },
          child: Text(
            'عرض المزيد',
            style: TextStyle(
              color: AppColors.primary,
              fontSize: ResponsiveHelper.isTablet(context) ? 16 : 14,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildDestinationsList(BuildContext context) {
    return BlocBuilder<DestinationsCubit, DestinationsState>(
      builder: (context, state) {
        if (state is DestinationsLoading) {
          return _buildLoadingState(context);
        } else if (state is DestinationsLoaded) {
          return _buildLoadedState(context, state.destinations);
        } else if (state is DestinationsError) {
          return _buildErrorState(context, state.message);
        }
        return _buildEmptyState(context);
      },
    );
  }

  Widget _buildLoadingState(BuildContext context) {
    return SizedBox(
      height: 200.h(context),
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        itemCount: 3,
        itemBuilder: (context, index) => _buildShimmerCard(context),
      ),
    );
  }

  Widget _buildLoadedState(
    BuildContext context,
    List<DestinationEntity> destinations,
  ) {
    if (destinations.isEmpty) {
      return _buildEmptyState(context);
    }

    return SizedBox(
      height: 200.h(context),
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        itemCount: destinations.length,
        itemBuilder: (context, index) {
          final destination = destinations[index];
          return _buildDestinationCard(context, destination);
        },
      ),
    );
  }

  Widget _buildErrorState(BuildContext context, String message) {
    return SizedBox(
      height: 200.h(context),
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.error_outline, size: 48, color: AppColors.error),
            8.verticalSpace,
            Text(
              'حدث خطأ في تحميل الوجهات',
              style: AppTextStyles.bodyMedium.copyWith(color: AppColors.error),
            ),
            4.verticalSpace,
            Text(
              message,
              style: AppTextStyles.bodySmall.copyWith(
                color: AppColors.textSecondary,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildEmptyState(BuildContext context) {
    return SizedBox(
      height: 200.h(context),
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.location_off, size: 48, color: AppColors.textTertiary),
            8.verticalSpace,
            Text(
              'لا توجد وجهات متاحة حالياً',
              style: AppTextStyles.bodyMedium.copyWith(
                color: AppColors.textTertiary,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildShimmerCard(BuildContext context) {
    return Container(
      width: 160.w(context),
      margin: EdgeInsets.only(right: 16.w(context)),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(AppDimensions.radiusMedium),
        color: Colors.grey.withOpacity(0.1),
      ),
      child: Column(
        children: [
          Expanded(
            flex: 3,
            child: Container(
              decoration: BoxDecoration(
                borderRadius: BorderRadius.vertical(
                  top: Radius.circular(AppDimensions.radiusMedium),
                ),
                color: Colors.grey.withOpacity(0.2),
              ),
            ),
          ),
          Expanded(
            flex: 1,
            child: Container(
              padding: EdgeInsets.all(12),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Container(
                    height: 12,
                    width: double.infinity,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(6),
                      color: Colors.grey.withOpacity(0.2),
                    ),
                  ),
                  4.verticalSpace,
                  Container(
                    height: 10,
                    width: 80,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(5),
                      color: Colors.grey.withOpacity(0.2),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDestinationCard(
    BuildContext context,
    DestinationEntity destination,
  ) {
    final isTablet = ResponsiveHelper.isTablet(context);

    return Container(
      width: isTablet ? 180.w(context) : 160.w(context),
      margin: EdgeInsets.only(right: 16.w(context)),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(AppDimensions.radiusMedium),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 8,
            offset: Offset(0, 2),
          ),
        ],
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(AppDimensions.radiusMedium),
        child: Material(
          child: InkWell(
            onTap: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => DestinationDetailsPage(
                    destinationId: destination.id,
                    destinationName: destination.displayName,
                  ),
                ),
              );
            },
            child: Stack(
              children: [
                // Background Image
                Container(
                  height: 200.h(context),
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      colors: [
                        AppColors.primary.withOpacity(0.7),
                        AppColors.primary,
                      ],
                      begin: Alignment.topCenter,
                      end: Alignment.bottomCenter,
                    ),
                  ),
                  child: destination.hasImage
                      ? Image.network(
                          destination.imageUrl,
                          fit: BoxFit.cover,
                          width: double.infinity,
                          height: double.infinity,
                          errorBuilder: (context, error, stackTrace) =>
                              _buildPlaceholderImage(),
                        )
                      : _buildPlaceholderImage(),
                ),

                // Favorite Button
                Positioned(
                  top: 8,
                  right: 8,
                  child: Container(
                    padding: EdgeInsets.all(4),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      shape: BoxShape.circle,
                    ),
                    child: Icon(
                      Icons.favorite_border,
                      color: AppColors.primary,
                      size: 20,
                    ),
                  ),
                ),

                // Price Badge
                Positioned(
                  top: 8,
                  left: 8,
                  child: Container(
                    padding: EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: AppColors.success,
                      borderRadius: BorderRadius.circular(
                        AppDimensions.radiusSmall,
                      ),
                    ),
                    child: Text(
                      destination.formattedPrice,
                      style: AppTextStyles.bodySmall.copyWith(
                        color: AppColors.white,
                        fontWeight: FontWeight.bold,
                        fontSize: 10,
                      ),
                    ),
                  ),
                ),

                // Content
                Positioned(
                  bottom: 0,
                  left: 0,
                  right: 0,
                  child: Container(
                    padding: EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.only(
                        bottomLeft: Radius.circular(AppDimensions.radiusMedium),
                        bottomRight: Radius.circular(
                          AppDimensions.radiusMedium,
                        ),
                      ),
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          destination.displayName,
                          style: AppTextStyles.bodyMedium.copyWith(
                            fontWeight: FontWeight.bold,
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                        4.verticalSpace,
                        Text(
                          destination.displayLocation,
                          style: AppTextStyles.bodySmall.copyWith(
                            color: AppColors.textSecondary,
                            fontSize: 11,
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildPlaceholderImage() {
    return Container(
      width: double.infinity,
      height: double.infinity,
      color: AppColors.primary.withOpacity(0.1),
      child: Icon(
        Icons.location_on,
        size: 48,
        color: AppColors.primary.withOpacity(0.5),
      ),
    );
  }
}
