// features/guides/domain/entities/working_day_entity.dart
import 'package:equatable/equatable.dart';

enum DayStatus {
  available, // null or 0
  busy,      // 1
  off,       // 2
}

class WorkingDayEntity extends Equatable {
  final int id;
  final String createdAt;
  final String updatedAt;
  final String day;
  final String start;
  final String end;
  final int? dayStatus;
  final String guideId;
  final String? deletedAt;
  final int isActivate;

  const WorkingDayEntity({
    required this.id,
    required this.createdAt,
    required this.updatedAt,
    required this.day,
    required this.start,
    required this.end,
    this.dayStatus,
    required this.guideId,
    this.deletedAt,
    required this.isActivate,
  });

  // Helper methods
  bool get isActive => isActivate == 1;

  DayStatus get status {
    switch (dayStatus) {
      case 1:
        return DayStatus.busy;
      case 2:
        return DayStatus.off;
      default:
        return DayStatus.available;
    }
  }

  String get statusText {
    switch (status) {
      case DayStatus.available:
        return 'متاح';
      case DayStatus.busy:
        return 'مشغول';
      case DayStatus.off:
        return 'إجازة';
    }
  }

  String get timeRange => '$start - $end';

  bool get isAvailable => status == DayStatus.available && isActive;

  @override
  List<Object?> get props => [
        id,
        createdAt,
        updatedAt,
        day,
        start,
        end,
        dayStatus,
        guideId,
        deletedAt,
        isActivate,
      ];
}
