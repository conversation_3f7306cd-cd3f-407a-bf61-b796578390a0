// features/guides/domain/usecases/get_guide_details.dart
import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';
import '../../../../core/error/failures.dart';
import '../../../../core/usecases/usecase.dart';
import '../entities/guide_details_entity.dart';
import '../repositories/guide_details_repository.dart';

class GetGuideDetails implements UseCase<GuideDetailsEntity, GetGuideDetailsParams> {
  final GuideDetailsRepository repository;

  GetGuideDetails(this.repository);

  @override
  Future<Either<Failure, GuideDetailsEntity>> call(GetGuideDetailsParams params) async {
    return await repository.getGuideDetails(params.guideId);
  }
}

class GetGuideDetailsParams extends Equatable {
  final int guideId;

  const GetGuideDetailsParams({required this.guideId});

  @override
  List<Object> get props => [guideId];
}
