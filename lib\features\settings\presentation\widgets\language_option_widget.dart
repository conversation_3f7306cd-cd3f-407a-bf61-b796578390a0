// features/settings/presentation/widgets/language_option_widget.dart
import 'package:flutter/material.dart';
import 'package:tripooo_user/core/theme/theme.dart';
import 'package:tripooo_user/features/onboarding/utils/responsive_helper.dart';

class LanguageOptionWidget extends StatelessWidget {
  final String code;
  final String name;
  final String englishName;
  final String flag;
  final bool isSelected;
  final VoidCallback onTap;

  const LanguageOptionWidget({
    super.key,
    required this.code,
    required this.name,
    required this.englishName,
    required this.flag,
    required this.isSelected,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    final isTablet = ResponsiveHelper.isTablet(context);
    
    return Card(
      margin: EdgeInsets.only(bottom: isTablet ? 16 : 12),
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(AppDimensions.radiusMedium),
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onTap,
          borderRadius: BorderRadius.circular(AppDimensions.radiusMedium),
          child: Container(
            padding: EdgeInsets.all(isTablet ? 24 : 16),
            decoration: BoxDecoration(
              color: isSelected 
                  ? AppColors.primary.withValues(alpha: 0.1) 
                  : Colors.transparent,
              borderRadius: BorderRadius.circular(AppDimensions.radiusMedium),
              border: Border.all(
                color: isSelected 
                    ? AppColors.primary 
                    : Colors.transparent,
                width: 2,
              ),
            ),
            child: Row(
              children: [
                Text(
                  flag, 
                  style: TextStyle(fontSize: isTablet ? 32 : 28),
                ),
                SizedBox(width: isTablet ? 20 : 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        name,
                        style: AppTextStyles.bodyMedium.copyWith(
                          fontSize: isTablet ? 20 : 18,
                          fontWeight: FontWeight.w600,
                          color: isSelected 
                              ? AppColors.primary 
                              : AppColors.textPrimary,
                        ),
                      ),
                      SizedBox(height: 4),
                      Text(
                        englishName,
                        style: AppTextStyles.bodySmall.copyWith(
                          fontSize: isTablet ? 16 : 14,
                          color: AppColors.textSecondary,
                        ),
                      ),
                    ],
                  ),
                ),
                if (isSelected)
                  Container(
                    padding: EdgeInsets.all(isTablet ? 8 : 6),
                    decoration: BoxDecoration(
                      color: AppColors.primary,
                      shape: BoxShape.circle,
                    ),
                    child: Icon(
                      Icons.check,
                      color: AppColors.white,
                      size: isTablet ? 24 : 20,
                    ),
                  ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
