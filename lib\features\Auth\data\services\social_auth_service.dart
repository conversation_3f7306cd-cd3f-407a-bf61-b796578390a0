// features/Auth/data/services/social_auth_service.dart
import 'package:dartz/dartz.dart';
import 'package:google_sign_in/google_sign_in.dart';
import 'package:flutter_facebook_auth/flutter_facebook_auth.dart';
import '../models/social_auth_models.dart';

abstract class SocialAuthService {
  Future<Either<String, SocialUserInfo>> signInWithGoogle();
  Future<Either<String, SocialUserInfo>> signInWithFacebook();
  Future<Either<String, SocialUserInfo>> signInWithApple();
  Future<void> signOut(SocialProvider provider);
  Future<void> signOutAll();
}

class SocialAuthServiceImpl implements SocialAuthService {
  final GoogleSignIn _googleSignIn = GoogleSignIn(scopes: ['email', 'profile']);

  @override
  Future<Either<String, SocialUserInfo>> signInWithGoogle() async {
    try {
      // Check if Google Play Services is available
      final bool isAvailable = await _googleSignIn.isSignedIn();
      print('Google Sign-In availability check: $isAvailable');

      // Sign out first to ensure fresh login
      await _googleSignIn.signOut();

      final GoogleSignInAccount? googleUser = await _googleSignIn.signIn();

      if (googleUser == null) {
        return Left('Google sign in was cancelled by user');
      }

      final GoogleSignInAuthentication googleAuth =
          await googleUser.authentication;

      if (googleAuth.accessToken == null) {
        return Left(
          'Failed to get Google access token - Please check Firebase configuration',
        );
      }

      final userInfo = SocialUserInfo.fromGoogle(
        id: googleUser.id,
        email: googleUser.email,
        displayName: googleUser.displayName,
        photoUrl: googleUser.photoUrl,
      );

      return Right(userInfo);
    } catch (e) {
      print('Google Sign-In Error: $e');

      // Handle specific error types
      if (e.toString().contains('sign_in_failed')) {
        return Left(
          'Google Sign-In failed. Please check your internet connection and try again.',
        );
      } else if (e.toString().contains('network_error')) {
        return Left('Network error. Please check your internet connection.');
      } else if (e.toString().contains('ApiException')) {
        return Left(
          'Google Sign-In configuration error. Please contact support.',
        );
      } else {
        return Left('Google sign in failed: ${e.toString()}');
      }
    }
  }

  @override
  Future<Either<String, SocialUserInfo>> signInWithFacebook() async {
    try {
      // Sign out first to ensure fresh login
      await FacebookAuth.instance.logOut();

      final LoginResult result = await FacebookAuth.instance.login(
        permissions: ['email', 'public_profile'],
      );

      if (result.status == LoginStatus.success) {
        // Get user data
        final userData = await FacebookAuth.instance.getUserData(
          fields: "id,name,email,first_name,last_name,picture.width(200)",
        );

        final userInfo = SocialUserInfo.fromFacebook(
          id: userData['id'],
          email: userData['email'],
          name: userData['name'],
          firstName: userData['first_name'],
          lastName: userData['last_name'],
          pictureUrl: userData['picture']?['data']?['url'],
        );

        return Right(userInfo);
      } else if (result.status == LoginStatus.cancelled) {
        return Left('Facebook sign in was cancelled');
      } else {
        return Left('Facebook sign in failed: ${result.message}');
      }
    } catch (e) {
      return Left('Facebook sign in failed: ${e.toString()}');
    }
  }

  @override
  Future<Either<String, SocialUserInfo>> signInWithApple() async {
    // Temporarily disabled Apple Sign-In to avoid build issues
    // TODO: Re-enable after proper iOS setup
    return Left(
      'Apple Sign In is temporarily disabled. Please use Google or Facebook.',
    );

    /*
    try {
      // Check if Apple Sign In is available
      if (!Platform.isIOS && !Platform.isMacOS) {
        return Left('Apple Sign In is only available on iOS and macOS');
      }

      // Apple Sign-In implementation will be added after proper setup
      return Left('Apple Sign In setup required');
    } catch (e) {
      return Left('Apple sign in failed: ${e.toString()}');
    }
    */
  }

  @override
  Future<void> signOut(SocialProvider provider) async {
    try {
      switch (provider) {
        case SocialProvider.google:
          await _googleSignIn.signOut();
          break;
        case SocialProvider.facebook:
          await FacebookAuth.instance.logOut();
          break;
        case SocialProvider.apple:
          // Apple doesn't provide a sign out method
          // The user needs to revoke access from Settings
          break;
      }
    } catch (e) {
      // Ignore sign out errors
    }
  }

  @override
  Future<void> signOutAll() async {
    await Future.wait([
      signOut(SocialProvider.google),
      signOut(SocialProvider.facebook),
      signOut(SocialProvider.apple),
    ]);
  }
}

// Extension to get access tokens
extension SocialUserInfoExtension on SocialUserInfo {
  Future<String?> getAccessToken() async {
    switch (provider) {
      case SocialProvider.google:
        final GoogleSignIn googleSignIn = GoogleSignIn();
        final GoogleSignInAccount? account = googleSignIn.currentUser;
        if (account != null) {
          final GoogleSignInAuthentication auth = await account.authentication;
          return auth.accessToken;
        }
        return null;
      case SocialProvider.facebook:
        final AccessToken? accessToken =
            await FacebookAuth.instance.accessToken;
        return accessToken?.tokenString;
      case SocialProvider.apple:
        // Apple doesn't provide access tokens in the same way
        // You would typically use the authorization code or identity token
        return null;
    }
  }

  Future<String?> getIdToken() async {
    switch (provider) {
      case SocialProvider.google:
        final GoogleSignIn googleSignIn = GoogleSignIn();
        final GoogleSignInAccount? account = googleSignIn.currentUser;
        if (account != null) {
          final GoogleSignInAuthentication auth = await account.authentication;
          return auth.idToken;
        }
        return null;
      case SocialProvider.facebook:
        // Facebook doesn't provide ID tokens in the same way as Google
        return null;
      case SocialProvider.apple:
        // For Apple, you would use the identity token from the credential
        return null;
    }
  }
}
