// core/network/api_endpoints.dart
class ApiEndpoints {
  // Base URL
  static const String baseUrl = 'http://tripooo.com.sa';

  // Auth endpoints
  static const String user = '/api/auth/';
  static const String checkToken = '/api/auth/check-token';
  static const String register = '/api/auth/register';
  static const String mobileCheck = '/api/auth/mobile-check';
  static const String regenerateCode = '/api/auth/regenerate-code';
  static const String login = '/api/auth/login';
  static const String userUpdate = '/api/auth/update';
  static const String changePassword = '/api/auth/change-password';
  static const String changeMobileNumber = '/api/auth/change-mobile-number';
  static const String logout = '/api/auth/logout';
  static const String sendResetCode = '/api/auth/send-reset-code';
  static const String verifyResetCode = '/api/auth/verify-reset-code';
  static const String resetPassword = '/api/auth/reset';
  static const String socialAuth = '/api/auth/social-login';

  // Guides endpoints
  static const String guides = '/api/guides/0/10';
  static const String featuredGuides = '/api/guides/0/10';
  static const String searchGuides = '/api/guides/search';
  static String guideDetails(int guideId) => '/api/guides/details/$guideId';

  // Destinations endpoints
  static String destinations({
    int offset = 0,
    int limit = 10,
    int? countryId,
    int? priceFrom,
    int? priceTo,
  }) {
    String endpoint = '/api/destinations/$offset/$limit';
    List<String> params = [];

    if (countryId != null) params.add('country_id=$countryId');
    if (priceFrom != null) params.add('price_from=$priceFrom');
    if (priceTo != null) params.add('price_to=$priceTo');

    if (params.isNotEmpty) {
      endpoint += '?${params.join('&')}';
    }

    return endpoint;
  }

  static String destinationDetails(int destinationId) =>
      '/api/destinations/details/$destinationId';

  // User types
  static const int userTypeClient = 1;
  static const int userTypeGuide = 2;

  // Language IDs
  static const int languageEnglish = 8;
  static const int languageArabic = 9;

  // Helper methods
  static String getFullUrl(String endpoint) => '$baseUrl$endpoint';

  // HTTP Methods for reference
  static const String methodGet = 'GET';
  static const String methodPost = 'POST';

  // Common headers
  static Map<String, String> get defaultHeaders => {
    'accept': 'application/json',
  };

  static Map<String, String> authHeaders(String token) => {
    'accept': 'application/json',
    'Authorization': 'Bearer $token',
  };
}
