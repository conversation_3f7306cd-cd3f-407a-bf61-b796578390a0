// features/Auth/presentation/pages/register_screen.dart
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:go_router/go_router.dart';
import 'package:tripooo_user/constants/app_images.dart';
import 'package:tripooo_user/core/theme/theme.dart';
import 'package:tripooo_user/features/Auth/presentation/cubit/auth_cubit.dart';
import 'package:tripooo_user/core/routing/app_router.dart';
import 'dart:ui';
import 'package:tripooo_user/core/utils/responsive_extensions.dart';
import 'package:tripooo_user/core/utils/widget_extensions.dart';
import 'package:tripooo_user/features/onboarding/utils/responsive_helper.dart';
import 'package:tripooo_user/core/models/country_model.dart';
import '../widgets/social_auth_buttons.dart';
import 'package:tripooo_user/core/widgets/widgets.dart';

class RegisterScreen extends StatefulWidget {
  const RegisterScreen({super.key});

  @override
  State<RegisterScreen> createState() => _RegisterScreenState();
}

class _RegisterScreenState extends State<RegisterScreen> {
  final _formKey = GlobalKey<FormState>();
  final _fullNameController = TextEditingController();
  final _phoneController = TextEditingController();
  final _emailController = TextEditingController();
  final _passwordController = TextEditingController();
  final _confirmPasswordController = TextEditingController();
  bool _obscurePassword = true;
  bool _obscureConfirmPassword = true;
  bool _agreeToTerms = false;
  late Country _selectedCountry;

  @override
  void initState() {
    super.initState();
    _selectedCountry = CountryData.getDefaultCountry();
  }

  @override
  void dispose() {
    _fullNameController.dispose();
    _phoneController.dispose();
    _emailController.dispose();
    _passwordController.dispose();
    _confirmPasswordController.dispose();
    super.dispose();
  }

  String? _validateName(String? value) {
    if (value == null || value.trim().isEmpty) {
      return 'full_name_required'.tr();
    }
    if (value.trim().length < 2) {
      return 'name_too_short'.tr();
    }
    return null;
  }

  String? _validateEmail(String? value) {
    if (value == null || value.trim().isEmpty) {
      return 'email_required'.tr();
    }
    final emailRegex = RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$');
    if (!emailRegex.hasMatch(value.trim())) {
      return 'invalid_email'.tr();
    }
    return null;
  }

  String? _validatePhone(String? value) {
    if (value == null || value.trim().isEmpty) {
      return 'phone_required'.tr();
    }

    // Get validation rule for current country
    final rule =
        CountryData.getPhoneRule(_selectedCountry.code) ??
        CountryData.getDefaultPhoneRule();

    // Check length
    if (value.trim().length < rule.minLength ||
        value.trim().length > rule.maxLength) {
      return Localizations.localeOf(context).languageCode == 'ar'
          ? rule.errorMessageAr
          : rule.errorMessage;
    }

    // Check pattern
    final phoneRegex = RegExp(rule.pattern);
    if (!phoneRegex.hasMatch(value.trim())) {
      return Localizations.localeOf(context).languageCode == 'ar'
          ? rule.errorMessageAr
          : rule.errorMessage;
    }

    return null;
  }

  String? _validatePassword(String? value) {
    if (value == null || value.isEmpty) {
      return 'password_required'.tr();
    }
    if (value.length < 8) {
      return 'password_too_short'.tr();
    }
    return null;
  }

  String? _validateConfirmPassword(String? value) {
    if (value == null || value.isEmpty) {
      return 'confirm_password_required'.tr();
    }
    if (value != _passwordController.text) {
      return 'passwords_dont_match'.tr();
    }
    return null;
  }

  void _submitForm() {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    if (!_agreeToTerms) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('please_agree_terms'.tr()),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    context.read<AuthCubit>().register(
      name: _fullNameController.text.trim(),
      email: _emailController.text.trim(),
      mobile: _phoneController.text.trim(),
      password: _passwordController.text,
      passwordConfirmation: _confirmPasswordController.text,
      userType: 1, // Client by default
    );
  }

  @override
  Widget build(BuildContext context) {
    final isTablet = ResponsiveHelper.isTablet(context);
    final isLandscape = ResponsiveHelper.isLandscape(context);
    final isSmallScreen = ResponsiveHelper.isSmallScreen(context);

    return Scaffold(
      backgroundColor: Colors.white,
      body: BlocListener<AuthCubit, AuthState>(
        listener: (context, state) async {
          if (state is AuthRegisterSuccess) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text('registration_successful'.tr()),
                backgroundColor: Colors.green,
              ),
            );
            // Navigate to mobile verification screen
            context.goToMobileVerification(_phoneController.text.trim());
          } else if (state is AuthSocialLoginSuccess) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text('social_registration_successful'.tr()),
                backgroundColor: Colors.green,
              ),
            );
            // Add small delay to ensure data is saved
            await Future.delayed(const Duration(milliseconds: 200));
            if (context.mounted) {
              // Force navigation to home
              context.go(AppRoutes.home);
            }
          } else if (state is AuthError) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(state.message),
                backgroundColor: Colors.red,
              ),
            );
          }
        },
        child: SafeArea(
          child: SingleChildScrollView(
            physics: const BouncingScrollPhysics(),
            child: _buildResponsiveLayout(
              context,
              isTablet,
              isLandscape,
              isSmallScreen,
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildResponsiveLayout(
    BuildContext context,
    bool isTablet,
    bool isLandscape,
    bool isSmallScreen,
  ) {
    if (isLandscape && isTablet) {
      return _buildLandscapeTabletLayout(context);
    } else if (isTablet) {
      return _buildTabletLayout(context);
    } else {
      return _buildMobileLayout(context, isSmallScreen);
    }
  }

  Widget _buildMobileLayout(BuildContext context, bool isSmallScreen) {
    return Stack(
      children: [
        BackdropFilter(
          filter: ImageFilter.blur(sigmaX: 2.0, sigmaY: 2.0),
          child: CustomImageView(
            url: Assets.assetsImagesRegisterPattern,
            alignment: Alignment.topRight,
            height: 350.h(context),
            width: context.screenWidth * 0.35,
            fit: BoxFit.fill,
          ),
        ),
        Padding(
          padding: ResponsiveHelper.getContentPadding(context),
          child: Form(
            key: _formKey,
            child: Column(
              children: [
                (isSmallScreen ? 15 : 25).verticalSpaceResponsive(context),

                Text(
                  'register_title'.tr(),
                  textAlign: TextAlign.center,
                  style: AppTextStyles.title.copyWith(
                    fontSize: ResponsiveHelper.getTitleFontSize(context),
                  ),
                ),
                _buildTabBar(context),

                ResponsiveHelper.getSpacing(
                  context,
                  type: 'large',
                ).verticalSpace,

                ..._buildFormFields(context, isSmallScreen),

                ResponsiveHelper.getSpacing(
                  context,
                  type: 'xlarge',
                ).verticalSpace,
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildTabletLayout(BuildContext context) {
    return Center(
      child: Container(
        constraints: const BoxConstraints(maxWidth: 600),
        child: Stack(
          children: [
            BackdropFilter(
              filter: ImageFilter.blur(sigmaX: 2.0, sigmaY: 2.0),
              child: CustomImageView(
                url: Assets.assetsImagesRegisterPattern,
                alignment: Alignment.topRight,
                height: 257.h(context),
                width: context.screenWidth * 0.3,
                fit: BoxFit.fill,
              ),
            ),
            Padding(
              padding: ResponsiveHelper.getContentPadding(context),
              child: Form(
                key: _formKey,
                child: Column(
                  children: [
                    40.verticalSpaceResponsive(context),

                    Text(
                      'register_title'.tr(),
                      textAlign: TextAlign.center,
                      style: AppTextStyles.title.copyWith(
                        fontSize: ResponsiveHelper.getTitleFontSize(context),
                      ),
                    ),
                    32.verticalSpaceResponsive(context),

                    _buildTabBar(context),

                    ResponsiveHelper.getSpacing(
                      context,
                      type: 'xlarge',
                    ).verticalSpace,

                    ..._buildFormFields(context, false),

                    ResponsiveHelper.getSpacing(
                      context,
                      type: 'xlarge',
                    ).verticalSpace,
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildLandscapeTabletLayout(BuildContext context) {
    return Row(
      children: [
        BackdropFilter(
          filter: ImageFilter.blur(sigmaX: 2.0, sigmaY: 2.0),
          child: CustomImageView(
            url: Assets.assetsImagesRegisterPattern,
            alignment: Alignment.center,
            height: 257.h(context),
            width: context.screenWidth * .3,
            fit: BoxFit.cover,
          ),
        ),
        // Right side - Form
        Expanded(
          flex: 1,
          child: SingleChildScrollView(
            padding: ResponsiveHelper.getContentPadding(context),
            child: Form(
              key: _formKey,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  24.verticalSpaceResponsive(context),

                  Text(
                    'register_title'.tr(),
                    textAlign: TextAlign.center,
                    style: AppTextStyles.title.copyWith(
                      fontSize: ResponsiveHelper.getTitleFontSize(context),
                    ),
                  ),
                  24.verticalSpaceResponsive(context),

                  _buildTabBar(context),

                  ResponsiveHelper.getSpacing(
                    context,
                    type: 'large',
                  ).verticalSpace,

                  ..._buildFormFields(context, false),

                  ResponsiveHelper.getSpacing(
                    context,
                    type: 'large',
                  ).verticalSpace,
                ],
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildTabBar(BuildContext context) {
    return CustomTabBar(
      tabs: [
        CustomTab(text: 'register'.tr()),
        CustomTab(text: 'login'.tr()),
      ],
      selectedIndex: 0,
      onTabSelected: (index) {
        if (index == 1) {
          context.goToLogin();
        }
      },
    );
  }

  List<Widget> _buildFormFields(BuildContext context, bool isSmallScreen) {
    final spacing = isSmallScreen
        ? ResponsiveHelper.getSpacing(context, type: 'medium')
        : ResponsiveHelper.getSpacing(context, type: 'large');

    return [
      // Full Name field
      CustomTextField(
        label: 'full_name'.tr(),
        controller: _fullNameController,
        hintText: 'enter_full_name'.tr(),
        validator: _validateName,
        textAlign: TextAlign.right,
        labelAlignment: CrossAxisAlignment.end,
      ),

      spacing.verticalSpace,

      PhoneTextField(
        label: 'phone_number'.tr(),
        controller: _phoneController,
        validator: _validatePhone,
        labelAlignment: CrossAxisAlignment.end,
        filled: true,
        fillColor: AppColors.surfaceVariant,
        textAlign: TextAlign.right,
        initialCountry: _selectedCountry,
        onCountryChanged: (country) {
          setState(() {
            _selectedCountry = country;
          });
        },
        contentPadding: const EdgeInsets.symmetric(
          horizontal: 16,
          vertical: 16,
        ),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(AppDimensions.radiusMedium),
          borderSide: BorderSide(color: AppColors.borderColor),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(AppDimensions.radiusMedium),
          borderSide: BorderSide(color: AppColors.borderColor),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(AppDimensions.radiusMedium),
          borderSide: BorderSide(color: AppColors.primary, width: 2),
        ),
        errorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(AppDimensions.radiusMedium),
          borderSide: BorderSide(color: AppColors.error),
        ),
        countryCodeTextColor: AppColors.textPrimary,
      ),

      spacing.verticalSpace,

      CustomTextField(
        label: 'email'.tr(),
        controller: _emailController,
        hintText: 'enter_email'.tr(),
        keyboardType: TextInputType.emailAddress,
        validator: _validateEmail,
        textAlign: TextAlign.right,
        labelAlignment: CrossAxisAlignment.end,
      ),

      spacing.verticalSpace,

      CustomTextField(
        label: 'password'.tr(),
        controller: _passwordController,
        hintText: 'enter_password'.tr(),
        obscureText: _obscurePassword,
        validator: _validatePassword,
        textAlign: TextAlign.right,
        labelAlignment: CrossAxisAlignment.end,
        suffixIcon: IconButton(
          icon: Icon(
            _obscurePassword ? Icons.visibility_off : Icons.visibility,
            color: Colors.grey[400],
          ),
          onPressed: () => setState(() => _obscurePassword = !_obscurePassword),
        ),
      ),

      spacing.verticalSpace,

      CustomTextField(
        label: 'confirm_password'.tr(),
        controller: _confirmPasswordController,
        hintText: 'enter_password'.tr(),
        obscureText: _obscureConfirmPassword,
        validator: _validateConfirmPassword,
        textAlign: TextAlign.right,
        labelAlignment: CrossAxisAlignment.end,
        suffixIcon: IconButton(
          icon: Icon(
            _obscureConfirmPassword ? Icons.visibility_off : Icons.visibility,
            color: Colors.grey[400],
          ),
          onPressed: () => setState(
            () => _obscureConfirmPassword = !_obscureConfirmPassword,
          ),
        ),
      ),

      (spacing * 0.7).verticalSpace,

      CustomCheckbox(
        value: _agreeToTerms,
        onChanged: (value) => setState(() => _agreeToTerms = value!),
        richText: [
          TextSpan(text: 'agree_to'.tr()),
          TextSpan(text: ' '),
          ClickableTextSpan(
            text: 'terms_conditions'.tr(),
            onTap: () {
              // TODO: Navigate to terms and conditions
            },
          ),
          TextSpan(text: ' '),
          TextSpan(text: 'and'.tr()),
          TextSpan(text: ' '),
          ClickableTextSpan(
            text: 'privacy_policy'.tr(),
            onTap: () {
              // TODO: Navigate to privacy policy
            },
          ),
        ],
      ),

      spacing.verticalSpace,

      BlocBuilder<AuthCubit, AuthState>(
        builder: (context, state) {
          return CustomButton(
            text: 'create_account'.tr(),
            isLoading: state is AuthLoading,
            onPressed: _submitForm,
            isFullWidth: true,
            size: ButtonSize.large,
          );
        },
      ),

      spacing.verticalSpace,

      // // Or register with
      // Text(
      //   'or'.tr(),
      //   textAlign: TextAlign.center,
      //   style: TextStyle(
      //     fontSize: ResponsiveHelper.getDescriptionFontSize(context),
      //     color: Colors.grey[600],
      //   ),
      // ),

      // spacing.verticalSpace,

      // SocialAuthButtons(userType: 1, fcmToken: null),
    ];
  }
}
