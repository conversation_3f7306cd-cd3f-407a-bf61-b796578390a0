import '../services/fcm_helper.dart';

/// Utility class for FCM token operations
class FCMUtils {
  
  /// Get FCM token for authentication
  /// This method should be called before login/register operations
  static Future<String?> getTokenForAuth() async {
    try {
      String? fcmToken = await FCMHelper.getFCMToken();
      
      if (fcmToken != null && fcmToken.isNotEmpty) {
        print('FCM Token ready for authentication: ${fcmToken.substring(0, 20)}...');
        return fcmToken;
      } else {
        print('Warning: FCM token is null or empty');
        return null;
      }
    } catch (e) {
      print('Error getting FCM token for auth: $e');
      return null;
    }
  }
  
  /// Refresh and get new FCM token
  static Future<String?> refreshTokenForAuth() async {
    try {
      String? fcmToken = await FCMHelper.refreshFCMToken();
      
      if (fcmToken != null && fcmToken.isNotEmpty) {
        print('FCM Token refreshed for authentication: ${fcmToken.substring(0, 20)}...');
        return fcmToken;
      } else {
        print('Warning: Refreshed FCM token is null or empty');
        return null;
      }
    } catch (e) {
      print('Error refreshing FCM token for auth: $e');
      return null;
    }
  }
}
