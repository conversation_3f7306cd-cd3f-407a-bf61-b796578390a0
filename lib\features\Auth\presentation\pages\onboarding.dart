// features/Auth/presentation/pages/onboarding.dart
import 'package:flutter/material.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:tripooo_user/constants/app_images.dart';
import 'package:tripooo_user/core/routing/app_router.dart';
import 'package:tripooo_user/core/theme/theme.dart';
import 'package:tripooo_user/core/widgets/custom_button.dart';

import 'package:tripooo_user/core/utils/responsive_extensions.dart';

class OnboardingScreen extends StatefulWidget {
  const OnboardingScreen({super.key});

  @override
  State<OnboardingScreen> createState() => _OnboardingScreenState();
}

class _OnboardingScreenState extends State<OnboardingScreen> {
  final PageController _controller = PageController();
  int _currentPage = 0;

  List<_OnboardingPageData> get _pages => [
    _OnboardingPageData(
      image: Assets.assetsImagesOnBoarding1,
      titleKey: 'onboarding_title_1',
      descriptionKey: 'onboarding_desc_1',
      buttonTextKey: 'onboarding_next',
    ),
    _OnboardingPageData(
      image: Assets.assetsImagesOnBoarding2,
      titleKey: 'onboarding_title_2',
      descriptionKey: 'onboarding_desc_2',
      buttonTextKey: 'onboarding_next',
    ),
    _OnboardingPageData(
      image: Assets.assetsImagesOnBoarding3,
      titleKey: 'onboarding_title_3',
      descriptionKey: 'onboarding_desc_3',
      buttonTextKey: 'onboarding_start',
    ),
  ];

  void _nextPage() {
    if (_currentPage < _pages.length - 1) {
      _controller.nextPage(
        duration: Duration(milliseconds: 300),
        curve: Curves.ease,
      );
    } else {
      context.goToWelcome();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Column(
        children: [
          Expanded(
            child: PageView.builder(
              controller: _controller,
              itemCount: _pages.length,
              onPageChanged: (index) {
                setState(() {
                  _currentPage = index;
                });
              },
              itemBuilder: (context, index) {
                final page = _pages[index];
                return _OnboardingPage(
                  data: page,
                  isLast: index == _pages.length - 1,
                  onButtonPressed: _nextPage,
                  currentPage: _currentPage,
                  totalPages: _pages.length,
                );
              },
            ),
          ),
        ],
      ),
    );
  }
}

class _OnboardingPageData {
  final String image;
  final String titleKey;
  final String descriptionKey;
  final String buttonTextKey;

  _OnboardingPageData({
    required this.image,
    required this.titleKey,
    required this.descriptionKey,
    required this.buttonTextKey,
  });

  String get title => titleKey.tr();
  String get description => descriptionKey.tr();
  String get buttonText => buttonTextKey.tr();
}

class _OnboardingPage extends StatelessWidget {
  final _OnboardingPageData data;
  final bool isLast;
  final VoidCallback onButtonPressed;
  final int currentPage;
  final int totalPages;

  const _OnboardingPage({
    required this.data,
    required this.isLast,
    required this.onButtonPressed,
    required this.currentPage,
    required this.totalPages,
  });

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Column(
        children: [
          Expanded(
            flex: 3,
            child: Stack(
              children: [
                ClipRRect(
                  child: Image.asset(
                    data.image,
                    fit: BoxFit.cover,
                    width: double.infinity,
                    height: double.infinity,
                  ),
                ),
              ],
            ),
          ),
          Expanded(
            flex: 2,
            child: SafeArea(
              top: false,
              child: Padding(
                padding: EdgeInsets.symmetric(horizontal: 16.w(context)),
                child: Column(
                  children: [
                    SizedBox(height: 24),
                    Text(
                      data.title,
                      style: AppTextStyles.heading,
                      textAlign: TextAlign.center,
                    ),
                    SizedBox(height: 16),
                    Text(
                      data.description,
                      style: AppTextStyles.subtitle,
                      textAlign: TextAlign.center,
                    ),
                    SizedBox(height: 32),

                    // Page Indicator
                    Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: List.generate(
                        totalPages,
                        (index) => Container(
                          margin: EdgeInsets.symmetric(horizontal: 4),
                          width: currentPage == index ? 24 : 8,
                          height: 5,
                          decoration: BoxDecoration(
                            color: currentPage == index
                                ? AppColors.primary
                                : Colors.grey.shade300,
                            borderRadius: BorderRadius.circular(4),
                          ),
                        ),
                      ),
                    ),

                    SizedBox(height: 24),
                    CustomButton(
                      text: data.buttonText,
                      onPressed: onButtonPressed,
                      isFullWidth: true,
                    ),

                    SizedBox(height: 24),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
