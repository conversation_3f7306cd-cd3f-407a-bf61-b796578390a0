// features/Auth/domain/usecases/login.dart
import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';
import '../../../../core/error/failures.dart';
import '../../../../core/usecases/usecase.dart';
import '../entities/user.dart';
import '../repositories/auth_repository.dart';

class Login implements UseCase<User, LoginParams> {
  final AuthRepository repository;

  const Login(this.repository);

  @override
  Future<Either<Failure, User>> call(LoginParams params) async {
    return await repository.login(
      mobile: params.mobile,
      password: params.password,
      fcmToken: params.fcmToken,
    );
  }
}

class LoginParams extends Equatable {
  final String mobile;
  final String password;
  final String? fcmToken;

  const LoginParams({
    required this.mobile,
    required this.password,
    this.fcmToken,
  });

  @override
  List<Object?> get props => [mobile, password, fcmToken];
}
