# More Feature

This feature provides the "More" screen functionality for the Tripooo User app, including user profile management, settings, and app information.

## 📁 Structure

```
lib/features/more/
├── presentation/
│   ├── pages/
│   │   └── more_screen.dart          # Main More screen
│   └── widgets/
│       ├── widgets.dart              # Widget exports
│       ├── more_header_widget.dart   # Header with user info
│       └── more_menu_item_widget.dart # Reusable menu item
└── README.md                         # This file
```

## 🎯 Features

### Guest Mode Support
- Shows guest user interface when not logged in
- Provides login button for easy access
- Displays appropriate messaging for guests

### Authenticated User Features
- User profile display with avatar, name, and email
- Profile settings section
- Personal profile management
- Favorites and booking history access

### General Features
- Language selection
- Notifications management
- Help and support
- About us information
- App settings

## 🎨 UI Components

### MoreScreen
Main screen that adapts based on authentication state:
- **Guest Mode**: Shows login prompt and limited features
- **Authenticated**: Shows full profile and all features

### MoreHeaderWidget
Displays user information in a gradient header:
- User avatar (with fallback to default icon)
- User name or "Guest User"
- User email or login prompt
- Login button for guests

### MoreMenuItemWidget
Reusable component for menu items:
- Icon with colored background
- Title and subtitle text
- Arrow indicator
- Tap handling

## 🔧 Usage

### Navigation
The More screen is accessible via the bottom navigation bar:

```dart
// Navigate to More screen
context.go(AppRoutes.more);

// Or using extension method
context.goToMore();
```

### Integration with Bottom Navigation
The bottom navigation automatically highlights the More tab when active:

```dart
BottomNavigationWidget() // Automatically handles More tab state
```

## 🌐 Internationalization

All text is localized using the translation system:

```dart
Text('guest_user'.tr())           // "مستخدم ضيف" / "Guest User"
Text('profile_settings'.tr())     // "إعدادات الملف الشخصي" / "Profile Settings"
Text('general'.tr())              // "عام" / "General"
```

## 📱 Responsive Design

The More screen adapts to different screen sizes:
- **Tablet**: Larger spacing, fonts, and touch targets
- **Phone**: Optimized for smaller screens
- **Landscape**: Adjusted spacing for landscape orientation

## 🔐 Authentication Integration

The screen integrates with the AuthCubit to:
- Detect user authentication state
- Load user data when available
- Handle logout functionality
- Provide appropriate UI for each state

## 🎨 Theming

Uses the app's design system:
- **Colors**: AppColors for consistent theming
- **Typography**: AppTextStyles for text styling
- **Dimensions**: AppDimensions for spacing and sizing
- **Components**: CustomButton and other core widgets

## 🚀 Future Enhancements

Planned features for future releases:
- [ ] Favorites management
- [ ] Booking history
- [ ] Notification settings
- [ ] Help and support pages
- [ ] About us page
- [ ] Theme selection
- [ ] Privacy settings
- [ ] Account deletion

## 🔗 Related Features

- **Auth**: User authentication and profile management
- **Home**: Main app navigation
- **Profile**: Detailed profile editing (separate from More)
- **Settings**: App-wide settings management
