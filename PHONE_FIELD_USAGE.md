# 📱 PhoneTextField - دليل الاستخدام

## ✅ تم إصلاح مشكلة Navigation!

تم حل مشكلة الـ GoRouter exception التي كانت تحدث عند اختيار دولة من القائمة.

## 🚀 كيفية الاستخدام:

### الاستخدام الأساسي:
```dart
import 'package:flutter/material.dart';
import 'core/widgets/phone_text_field.dart';
import 'core/models/country_model.dart';

class MyForm extends StatefulWidget {
  @override
  _MyFormState createState() => _MyFormState();
}

class _MyFormState extends State<MyForm> {
  final TextEditingController _phoneController = TextEditingController();
  Country? _selectedCountry;

  @override
  Widget build(BuildContext context) {
    return PhoneTextField(
      label: 'رقم الجوال',
      hintText: '5xxxxxxxx',
      controller: _phoneController,
      initialCountry: CountryData.getDefaultCountry(), // السعودية
      onCountryChanged: (country) {
        setState(() {
          _selectedCountry = country;
        });
        // يمكنك هنا حفظ الدولة المختارة
      },
      validator: (value) {
        if (value == null || value.isEmpty) {
          return 'يرجى إدخال رقم الجوال';
        }
        return null;
      },
    );
  }
}
```

### الاستخدام المتقدم:
```dart
PhoneTextField(
  label: 'رقم الجوال',
  hintText: 'أدخل رقم الجوال',
  controller: phoneController,
  
  // إعدادات الدولة
  initialCountry: CountryData.findByCode('SA'), // السعودية
  onCountryChanged: (country) {
    print('تم اختيار: ${country.nameAr} ${country.dialCode}');
  },
  
  // تخصيص المظهر
  countryCodeBackgroundColor: Colors.green,
  countryCodeTextColor: Colors.white,
  showCountryCodeDropdown: true,
  
  // إعدادات النص
  textAlign: TextAlign.right,
  labelAlignment: CrossAxisAlignment.start,
  
  // التحقق من صحة البيانات
  validator: (value) {
    if (value == null || value.isEmpty) {
      return 'يرجى إدخال رقم الجوال';
    }
    if (value.length < 7) {
      return 'رقم الجوال قصير جداً';
    }
    if (value.length > 15) {
      return 'رقم الجوال طويل جداً';
    }
    return null;
  },
  
  // معالجة الأحداث
  onChanged: (value) {
    // يتم استدعاؤها عند تغيير النص
  },
  onSubmitted: (value) {
    // يتم استدعاؤها عند الضغط على Enter
  },
)
```

## 🌍 الدول المدعومة:

### الحصول على دولة معينة:
```dart
// بالكود
Country? saudi = CountryData.findByCode('SA');
Country? uae = CountryData.findByCode('AE');

// بكود الاتصال
Country? country = CountryData.findByDialCode('+966');

// البحث
List<Country> results = CountryData.searchCountries('السعودية');
```

### الدول المتاحة:
- **الدول العربية**: السعودية، الإمارات، مصر، الكويت، قطر، البحرين، عمان، الأردن، لبنان، سوريا، العراق، فلسطين، المغرب، الجزائر، تونس، ليبيا، السودان، اليمن
- **الدول العالمية**: أمريكا، بريطانيا، ألمانيا، فرنسا، إيطاليا، إسبانيا، كندا، أستراليا، اليابان، الصين، الهند، وأكثر من 50 دولة

## 🎨 التخصيص:

### الألوان:
```dart
PhoneTextField(
  countryCodeBackgroundColor: Colors.blue,
  countryCodeTextColor: Colors.white,
  fillColor: Colors.grey[100],
  filled: true,
)
```

### الحدود:
```dart
PhoneTextField(
  border: OutlineInputBorder(
    borderRadius: BorderRadius.circular(12),
    borderSide: BorderSide(color: Colors.blue),
  ),
  focusedBorder: OutlineInputBorder(
    borderRadius: BorderRadius.circular(12),
    borderSide: BorderSide(color: Colors.green, width: 2),
  ),
)
```

### النصوص:
```dart
PhoneTextField(
  labelStyle: TextStyle(
    fontSize: 18,
    fontWeight: FontWeight.bold,
    color: Colors.blue,
  ),
  textStyle: TextStyle(
    fontSize: 16,
    color: Colors.black,
  ),
  hintStyle: TextStyle(
    color: Colors.grey,
    fontStyle: FontStyle.italic,
  ),
)
```

## 📋 الحصول على القيم:

```dart
// رقم الجوال
String phoneNumber = _phoneController.text;

// الدولة المختارة
Country selectedCountry = _selectedCountry ?? CountryData.getDefaultCountry();

// الرقم الكامل
String fullPhoneNumber = '${selectedCountry.dialCode}$phoneNumber';

print('الرقم الكامل: $fullPhoneNumber');
// مثال: الرقم الكامل: +966501234567
```

## 🔧 نصائح للاستخدام:

1. **استخدم Form**: ضع PhoneTextField داخل Form للتحقق من صحة البيانات
2. **احفظ الدولة**: احفظ الدولة المختارة في state أو database
3. **التحقق المتقدم**: استخدم regex للتحقق من صيغة الرقم حسب كل دولة
4. **الاختبار**: اختبر على أجهزة مختلفة للتأكد من RTL support

## 🚨 ملاحظات مهمة:

- تم إصلاح مشكلة Navigation exception
- يدعم RTL بشكل كامل
- يعمل مع جميع إصدارات Flutter الحديثة
- متوافق مع Material Design 3

---

**🎉 الآن PhoneTextField جاهز للاستخدام في جميع أنحاء التطبيق!**
