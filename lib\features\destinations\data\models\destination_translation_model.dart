// features/destinations/data/models/destination_translation_model.dart
import '../../domain/entities/destination_translation_entity.dart';

class DestinationTranslationModel extends DestinationTranslationEntity {
  const DestinationTranslationModel({
    required super.id,
    required super.destinationId,
    required super.locale,
    super.name,
    super.info,
    super.location,
  });

  factory DestinationTranslationModel.fromJson(Map<String, dynamic> json) {
    return DestinationTranslationModel(
      id: json['id'] ?? 0,
      destinationId: json['destination_id'] ?? 0,
      locale: json['locale'] ?? '',
      name: json['name'],
      info: json['info'],
      location: json['location'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'destination_id': destinationId,
      'locale': locale,
      'name': name,
      'info': info,
      'location': location,
    };
  }
}
