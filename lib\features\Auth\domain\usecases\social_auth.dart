// features/Auth/domain/usecases/social_auth.dart
import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';
import '../../../../core/error/failures.dart';
import '../../../../core/usecases/usecase.dart';
import '../entities/user.dart';
import '../repositories/auth_repository.dart';
import '../../data/models/social_auth_models.dart';

class SocialAuth implements UseCase<User, SocialAuthParams> {
  final AuthRepository repository;

  const SocialAuth(this.repository);

  @override
  Future<Either<Failure, User>> call(SocialAuthParams params) async {
    return await repository.socialAuth(
      provider: params.provider,
      accessToken: params.accessToken,
      idToken: params.idToken,
      email: params.email,
      name: params.name,
      profilePicture: params.profilePicture,
      fcmToken: params.fcmToken,
      userType: params.userType,
    );
  }
}

class SocialAuthParams extends Equatable {
  final SocialProvider provider;
  final String accessToken;
  final String? idToken;
  final String? email;
  final String? name;
  final String? profilePicture;
  final String? fcmToken;
  final int userType;

  const SocialAuthParams({
    required this.provider,
    required this.accessToken,
    this.idToken,
    this.email,
    this.name,
    this.profilePicture,
    this.fcmToken,
    this.userType = 1,
  });

  @override
  List<Object?> get props => [
        provider,
        accessToken,
        idToken,
        email,
        name,
        profilePicture,
        fcmToken,
        userType,
      ];
}
